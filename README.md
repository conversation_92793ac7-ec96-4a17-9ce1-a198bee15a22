# Nordics API

## Setup

### Prerequisites

- [Docker](https://www.docker.com/get-started/)
- [npm](https://docs.npmjs.com/downloading-and-installing-node-js-and-npm)

### Setup Script

    ./scripts/setup.sh

See the [setup script](./scripts/setup.sh) for more details.

### Setup Debugging with Xdebug

Update variables in `.env.docker-compose` file:

    PHP_XDEBUG_MODE=debug
    PHP_JIT_MODE=0

Then restart containers by running:

    docker compose down
    docker compose up --build

Then set your IDE to listen for Xdebug connections. See documentation for your IDE.
