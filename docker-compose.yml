services:
    db:
        container_name: "${COMPOSE_PROJECT_NAME}-db"
        image: mariadb:10.5
        env_file:
            - .env
            - .env.docker-compose
        volumes:
            - './docker/mysql/conf.d:/etc/mysql/conf.d:cached'
            - './docker/.containers/db:/var/lib/mysql'
        ports:
            - "127.0.0.1:${COMPOSE_DB_PORT}:3306"
        restart: unless-stopped

    api:
        container_name: "${COMPOSE_PROJECT_NAME}-api"
        image: nio-api:1.0.0
        build:
            context: .
            dockerfile: docker/php/Dockerfile
        depends_on:
            - db
        volumes:
            - './docker/php/conf/php.ini:/usr/local/etc/php/conf.d/99-custom-php.ini:cached'
            - './docker/php/mysql-client/my.cnf:/etc/mysql/my.cnf:cached'
            - './docker/php/supervisor/supervisord.conf:/etc/supervisor/conf.d/supervisord.conf:cached'
            - './:/opt/nio/api'
        env_file:
            - .env.docker-compose
        restart: unless-stopped

    app:
        container_name: "${COMPOSE_PROJECT_NAME}-api-proxy"
        image: nginx:latest
        ports:
            - "127.0.0.1:${COMPOSE_API_PROXY_PORT}:80"
        depends_on:
            - api
        volumes:
            - './docker/nginx/nginx.conf:/etc/nginx/nginx.conf:cached'
            - './:/opt/nio/api:ro'
        restart: unless-stopped

    mail:
        container_name: "${COMPOSE_PROJECT_NAME}-mail"
        image: axllent/mailpit
        ports:
            - "127.0.0.1:${COMPOSE_MAIL_PORT}:8025"
        restart: unless-stopped
        environment:
            - "MAILPIT_HTTP_BIND_ADDR=127.0.0.1:${COMPOSE_MAIL_PORT}"

    cache:
        container_name: "${COMPOSE_PROJECT_NAME}-cache"
        image: redis:7-alpine
        volumes:
            - './docker/.containers/cache:/data'
        restart: unless-stopped
