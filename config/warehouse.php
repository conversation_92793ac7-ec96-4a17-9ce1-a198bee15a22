<?php

use Intervention\Image\Constraint;
use Intervention\Image\Interfaces\ImageInterface;

$scaleDown = function (Constraint $constraint) {
    $constraint->aspectRatio();
    $constraint->upsize();
};

return [

    /*
    |--------------------------------------------------------------------------
    | Warehouse Resources Templates
    |--------------------------------------------------------------------------
    |
    | These are generic resources templates that resources can extend from.
    | They define driver (handler) of the file as well as default config
    | (that each resource can override).
    |
    */
    'templates' => [
        'image' => [
            'driver' => 'image',

            'config' => [
                'disk' => 'public',
                'format' => 'png',
            ],
        ],

        'cover_image' => [
            'driver' => 'image',

            'config' => [
                'disk' => 'public',
                'format' => 'png',

                'commands' => [
                    'cover' => [1920, 1080],
                ],

                'thumbnails' => [
                    [
                        'id' => 'preview',
                        'commands' => [
                            'cover' => [640, 360],
                        ],
                    ],
                ],
            ],
        ],

        'logo_image' => [
            'driver' => 'image',

            'config' => [
                'disk' => 'public',
                'format' => 'png',

                'commands' => [
                    'cover' => [512, 512],
                ],

                'thumbnails' => [
                    [
                        'id' => 'preview',
                        'commands' => [
                            'cover' => [128, 128],
                        ],
                    ],
                ],
            ],
        ],

        'mini_logo_image' => [
            'driver' => 'image',

            'config' => [
                'disk' => 'public',
                'format' => 'png',

                'commands' => [
                    'cover' => [128, 128],
                ],
            ],
        ],

        'avatar_image' => [
            'driver' => 'image',

            'config' => [
                'disk' => 'public',
                'format' => 'png',

                'commands' => [
                    'cover' => [256, 256],
                ],

                'thumbnails' => [
                    [
                        'id' => 'preview',
                        'commands' => [
                            'cover' => [128, 128],
                        ],
                    ],
                ],
            ],
        ],

        'file' => [
            'driver' => 'file',

            'config' => [
                'disk' => 'public',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Warehouse Resources
    |--------------------------------------------------------------------------
    |
    | These are definitions of resources that warehouse stores. Each has its
    | own configuration such as store, processing parameters and more.
    |
    */
    'resources' => [
        'user_avatar_image' => [
            'template' => 'avatar_image',

            'config' => [
                'directory' => 'users/avatars',
            ],
        ],

        'company_logo_image' => [
            'template' => 'logo_image',

            'config' => [
                'directory' => 'companies',
            ],
        ],

        'company_cover_image' => [
            'template' => 'cover_image',

            'config' => [
                'directory' => 'companies',
            ],
        ],

        'company_presentation_file' => [
            'template' => 'file',

            'config' => [
                'directory' => 'companies/files',
                'allowed_types' => ['pdf', 'pptx'],
            ],
        ],

        'company_document' => [
            'template' => 'file',

            'config' => [
                'directory' => 'companies/files',
                'allowed_types' => ['pdf'],
                'max_size' => 1024 * 50, // 50 MB
            ],
        ],

        'tender_cover_image' => [
            'template' => 'cover_image',

            'config' => [
                'directory' => 'tenders',
            ],
        ],

        'solution_cover_image' => [
            'template' => 'cover_image',

            'config' => [
                'directory' => 'solutions',
            ],
        ],

        'solution_medium_image' => [
            'template' => 'image',

            'config' => [
                'directory' => 'solutions/media',

                'commands' => [
                    'resize' => [1920, null],
                ],

                'thumbnails' => [
                    [
                        'id' => 'preview',
                        'commands' => [
                            'resize' => [900, null],
                        ],
                    ],
                    [
                        'id' => 'carousel_thumbnail',
                        'commands' => [
                            'cover' => [600, 400],
                        ],
                    ],
                ],
            ],
        ],

        'solution_medium_carousel_thumbnail_image' => [
            'template' => 'image',
            'uploadable' => false,

            'config' => [
                'directory' => 'solutions/media',

                'commands' => [
                    'cover' => [600, 400],
                ],
            ],
        ],

        'technology_logo_image' => [
            'template' => 'mini_logo_image',

            'config' => [
                'directory' => 'technologies',
            ],
        ],

        'client_logo_image' => [
            'template' => 'mini_logo_image',

            'config' => [
                'directory' => 'clients',
            ],
        ],

        'candidate_cv_file' => [
            'template' => 'file',

            'config' => [
                'directory' => 'candidates',
                'allowed_types' => 'pdf',
                'max_size' => 5120,
            ],
        ],

        'article_image' => [
            'template' => 'image',

            'config' => [
                'directory' => 'articles',

                'commands' => [
                    'resize' => [1280, null],
                    'if' => fn (ImageInterface $image) => $image->width() > 1280,
                ],

                'thumbnails' => [
                    [
                        'id' => '750',
                        'commands' => [
                            'resize' => [750, null],
                        ],
                        'if' => fn (ImageInterface $image) => $image->width() > 750,
                    ],
                    [
                        'id' => '500',
                        'commands' => [
                            'resize' => [500, null],
                        ],
                        'if' => fn (ImageInterface $image) => $image->width() > 500,
                    ],
                    [
                        'id' => '250',
                        'commands' => [
                            'resize' => [250, null],
                        ],
                        'if' => fn (ImageInterface $image) => $image->width() > 250,
                    ],
                ],
            ],
        ],
    ],
];
