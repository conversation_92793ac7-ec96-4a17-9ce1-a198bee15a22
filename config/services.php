<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'affinda' => [
        'key' => env('AFFINDA_API_KEY'),
    ],

    'similarity_api' => [
        'url' => env('SIMILARITY_API_URL'),
    ],

    'assistant_api' => [
        'url' => env('ASSISTANT_API_URL'),
        'key' => env('ASSISTANT_API_KEY'),
    ],

    'matching_api' => [
        'url' => env('MATCHING_API_URL'),
        'key' => env('MATCHING_API_KEY'),
    ],

    'candidate_matching_api' => [
        'url' => env('CANDIDATE_MATCHING_API_URL'),
        'key' => env('CANDIDATE_MATCHING_API_KEY'),
    ],

    'companies_import' => [
        'company_upload_max' => env('COMPANY_UPLOAD_MAX', '')
    ]

];
