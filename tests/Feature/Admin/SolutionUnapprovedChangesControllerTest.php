<?php

namespace Tests\Feature\Admin;

use Tests\Feature\FeatureTestCase;

class SolutionUnapprovedChangesControllerTest extends FeatureTestCase
{
    private array $endpoints = [
        ['post',   '/admin/solutions/1/unapproved-change'],
        ['delete', '/admin/solutions/1/unapproved-change'],
    ];

    public function test_approve_new_solution_unapproved_change_as_requested(): void
    {
        $solutionId = $this->prepareNewSolutionSubmittedForReview();
        $this->actingAsMasterAdmin();

        $response = $this->postJson("/admin/solutions/$solutionId/unapproved-change", $this->newSolutionData($solutionId));
        $response->assertSuccessful();

        $showResponse = $this->getJson("/admin/solutions/$solutionId");
        $showResponse->assertSuccessful();

        $data = $showResponse->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('published', $data['publish_status']);
        $this->assertEquals('AnoSol', $data['name']);
        $this->assertEquals('<h1>THIS IS GOING TO BE GREAT</h1><p>Seriously I mean it</p>', $data['about']);
        $this->assertArrayHasKey('client', $data);
        $this->assertFalse($data['in_house']);
        $this->assertFalse(array_get($data, 'client.anonymous'));
        $this->assertEquals('So far so good', array_get($data, 'client.review'));
        $this->assertNotEmpty($data['media']);
        $this->assertEquals($this->SOLUTION_MEDIA_IMAGE_RESOURCE_PUBLIC_ID, array_get($data, 'media.0.source.id'));
        $this->assertNull($data['unapproved_change']);
    }

    public function test_approve_new_solution_unapproved_change_with_changes(): void
    {
        $solutionId = $this->prepareNewSolutionSubmittedForReview();
        $this->actingAsMasterAdmin();

        $response = $this->postJson("/admin/solutions/$solutionId/unapproved-change", $this->newSolutionDataWithChanges($solutionId));
        $response->assertSuccessful();

        $showResponse = $this->getJson("/admin/solutions/$solutionId");
        $showResponse->assertSuccessful();

        $data = $showResponse->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('published', $data['publish_status']);
        $this->assertEquals('AnoSol', $data['name']);
        $this->assertEquals('<h1>THIS IS GOING TO BE GREAT</h1><p>Seriously I mean it</p>', $data['about']);
        $this->assertArrayHasKey('client', $data);
        $this->assertFalse($data['in_house']);
        $this->assertFalse(array_get($data, 'client.anonymous'));
        $this->assertEquals('So far so good', array_get($data, 'client.review'));
        $this->assertNotEmpty($data['media']);
        $this->assertEquals($this->SOLUTION_MEDIA_IMAGE_RESOURCE_PUBLIC_ID, array_get($data, 'media.0.source.id'));
        $this->assertEquals($this->SOLUTION_MEDIA_IMAGE_RESOURCE_PUBLIC_ID, array_get($data, 'media.1.source.id'));
        $this->assertNull($data['unapproved_change']);
    }

    public function test_approve_updated_solution_unapproved_change_as_requested(): void
    {
        $solutionId = $this->prepareUpdatedSolutionSubmittedForReview();
        $this->actingAsMasterAdmin();

        $response = $this->postJson("/admin/solutions/$solutionId/unapproved-change", $this->updatedSolutionData(syncMedia: true));
        $response->assertSuccessful();

        $showResponse = $this->getJson("/admin/solutions/$solutionId");
        $showResponse->assertSuccessful();

        $data = $showResponse->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('published', $data['publish_status']);
        $this->assertEquals('We are very proud of this solution', $data['name']);
        $this->assertEquals('No longer interested in writing lorem ipsum parodies', $data['about']);
        $this->assertArrayNotHasKey('client', $data);
        $this->assertTrue($data['in_house']);
        $this->assertNotEmpty($data['media']);
        $this->assertEquals($this->SOLUTION_MEDIA_IMAGE_RESOURCE_PUBLIC_ID, array_get($data, 'media.0.source.id'));
        $this->assertNull($data['unapproved_change']);
    }

    public function test_approve_updated_solution_unapproved_change_with_changes(): void
    {
        $solutionId = $this->prepareUpdatedSolutionSubmittedForReview();
        $this->actingAsMasterAdmin();

        $response = $this->postJson("/admin/solutions/$solutionId/unapproved-change", $this->updatedSolutionDataWithChanges());
        $response->assertSuccessful();

        $showResponse = $this->getJson("/admin/solutions/$solutionId");
        $showResponse->assertSuccessful();

        $data = $showResponse->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('published', $data['publish_status']);
        $this->assertEquals('We are very proud of this solution', $data['name']);
        $this->assertEquals('No longer interested in writing lorem ipsum parodies', $data['about']);
        $this->assertArrayNotHasKey('client', $data);
        $this->assertTrue($data['in_house']);
        $this->assertNotEmpty($data['media']);
        $this->assertEquals($this->SOLUTION_MEDIA_IMAGE_RESOURCE_PUBLIC_ID, array_get($data, 'media.0.source.id'));
        $this->assertEquals($this->SOLUTION_MEDIA_IMAGE_RESOURCE_PUBLIC_ID, array_get($data, 'media.1.source.id'));
        $this->assertNull($data['unapproved_change']);
    }

    public function test_reject_new_solution_unapproved_change(): void
    {
        $solutionId = $this->prepareNewSolutionSubmittedForReview();
        $this->actingAsMasterAdmin();

        $response = $this->deleteJson("/admin/solutions/$solutionId/unapproved-change");
        $response->assertSuccessful();

        $showResponse = $this->getJson("/admin/solutions/$solutionId");
        $showResponse->assertSuccessful();

        $data = $showResponse->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('draft', $data['publish_status']);
        $this->assertNull($data['unapproved_change']);
    }

    public function test_reject_updated_solution_unapproved_change(): void
    {
        $solutionId = $this->prepareUpdatedSolutionSubmittedForReview();
        $this->actingAsMasterAdmin();

        $response = $this->deleteJson("/admin/solutions/$solutionId/unapproved-change");
        $response->assertSuccessful();

        $showResponse = $this->getJson("/admin/solutions/$solutionId");
        $showResponse->assertSuccessful();

        $data = $showResponse->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('published', $data['publish_status']);
        $this->assertNull($data['unapproved_change']);
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    public function test_clients_cannot_access_endpoints(): void
    {
        $this->actingAsGenericVendorClient();
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    private function prepareNewSolutionSubmittedForReview(): string
    {
        $this->actingAsGenericVendorClient();

        $response = $this->postJson('/solutions', $this->newSolutionData());
        $response->assertSuccessful();

        return $response->json('data.id');
    }

    private function prepareUpdatedSolutionSubmittedForReview(): string
    {
        $this->actingAsGenericVendorClient();

        $response = $this->putJson("/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID", $this->updatedSolutionData());
        $response->assertSuccessful();

        return $this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID;
    }

    private function newSolutionData(?string $solutionId = null): array
    {
        $mediaId = [];
        if ($solutionId) {
            $response = $this->getJson("/admin/solutions/$solutionId");
            $response->assertSuccessful();

            $mediaId = [
                'id' => $response->json('data.media.0.id'),
            ];
        }

        return [
            'as_draft' => false,

            'name' => 'AnoSol',
            'description' => 'Another great solution guys',
            'about' => '<h1>THIS IS GOING TO BE GREAT</h1><p>Seriously I mean it</p>',
            'cover_resource_id' => '4Hq5t2',
            'country' => 'es',
            'main_industry_id' => 81,
            'length_type' => 'ongoing',
            'length' => null,
            'ftes' => 30,
            'value' => 10_000_000,

            'industries' => [24, 58],
            'technologies' => [100, 218],

            'in_house' => false,

            'client' => [
                'anonymous' => false,
                'id' => null,
                'name' => 'Labaš, s. r. o.',
                'review' => '<p>So far so good</p>',
                'reviewer' => 'Luky',
                'reviewer_position' => 'CEO',
            ],

            'media' => [
                array_merge([
                    'type' => 'image',
                    'source' => $this->SOLUTION_MEDIA_IMAGE_RESOURCE_PUBLIC_ID,
                ], $mediaId),
            ],
        ];
    }

    private function newSolutionDataWithChanges(string $solutionId): array
    {
        $data = $this->newSolutionData($solutionId);
        $data['media'][] = [
            'type' => 'image',
            'source' => $this->SOLUTION_MEDIA_IMAGE_RESOURCE_PUBLIC_ID,
        ];

        return $data;
    }

    private function updatedSolutionData(bool $syncMedia = false): array
    {
        $mediaId = [];
        if ($syncMedia) {
            $response = $this->getJson("/admin/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID");
            $response->assertSuccessful();

            $mediaId = [
                'id' => $response->json('data.unapproved_change.media.0.id'),
            ];
        }

        return [
            'as_draft' => false,

            'name' => 'We are very proud of this solution', // changed
            'description' => 'We are not gonna tell what it is but our claim still stands. We are proud.',
            'about' => 'No longer interested in writing lorem ipsum parodies', // changed
            'cover_resource_id' => '4Hq5t2',
            'country' => 'ge',
            'main_industry_id' => 20, // changed
            'length_type' => 'ongoing',
            'length' => null,
            'ftes' => null,
            'value' => 9_876_543,

            'industries' => [],
            'technologies' => [100, 218, 148], // changed

            'in_house' => true,

            'media' => [
                array_merge([
                    'type' => 'image',
                    'source' => $this->SOLUTION_MEDIA_IMAGE_RESOURCE_PUBLIC_ID,
                ], $mediaId),
            ],
        ];
    }

    private function updatedSolutionDataWithChanges(): array
    {
        $data = $this->updatedSolutionData(syncMedia: true);
        $data['media'][] = [
            'type' => 'image',
            'source' => $this->SOLUTION_MEDIA_IMAGE_RESOURCE_PUBLIC_ID,
        ];

        return $data;
    }
}
