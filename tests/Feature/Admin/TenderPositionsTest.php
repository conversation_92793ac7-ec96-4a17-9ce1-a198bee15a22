<?php

namespace Tests\Feature\Admin;

use Tests\Feature\FeatureTestCase;

class TenderPositionsTest extends FeatureTestCase
{
    private array $endpoints = [
        ['post',   '/admin/tenders/1/positions'],
        ['get',    '/admin/tenders/1/positions/1'],
        ['put',    '/admin/tenders/1/positions/1'],
        ['put',    '/admin/tenders/1/positions/1/apply'],
        ['delete', '/admin/tenders/1/positions/1'],
    ];

    public function test_apply_candidate_to_tender_position(): void
    {
        $this->actingAsMasterAdmin();

        $applyResponse = $this->putJson(
            "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/positions/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID/apply",
            ['candidate_id' => $this->PUBLIC_VENDOR_CANDIDATE_JANE_PUBLIC_ID],
        );
        $applyResponse->assertSuccessful();

        $candidatesResponse = $this->getJson("admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $candidatesResponse->assertSuccessful();
        $this->assertCount(1, $candidatesResponse->json('data'));
        $candidateId = $candidatesResponse->json('data.0.id');

        $candidateResponse = $this->getJson("admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateId");
        $candidateResponse->assertSuccessful();

        $data = $candidateResponse->json('data');
        $this->assertEquals('Joan of Ark', $data['internal_name']);
        $this->assertEquals('pl', $data['country']);

        $this->assertCount(2, $data['skills']);
        $androidTechnology = array_first($data['skills'], fn (array $skill) => $skill['technology_id'] === 10);
        $this->assertNotNull($androidTechnology);
        $this->assertEquals(8, $androidTechnology['years_of_experience']);
        $this->assertContains(array_get($data, 'experiences.0.id'), $androidTechnology['experiences']);

        $this->assertCount(1, $data['experiences']);
        $this->assertEquals('Senior Android Developer at Large Automotive Company', array_get($data, 'experiences.0.name'));
        $this->assertEquals('years', array_get($data, 'experiences.0.length_type'));
        $this->assertEquals(5, array_get($data, 'experiences.0.length'));
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    public function test_clients_cannot_access_endpoints(): void
    {
        $this->actingAsGenericVendorClient();
        $this->assertEndpointsInaccessible($this->endpoints);
    }
}
