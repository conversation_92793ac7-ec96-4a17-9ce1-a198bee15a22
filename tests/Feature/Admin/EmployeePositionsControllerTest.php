<?php

namespace Tests\Feature\Admin;

use Tests\Feature\FeatureTestCase;

class EmployeePositionsControllerTest extends FeatureTestCase
{
    private array $endpoints = [
        ['get',    '/admin/employee-positions'],
        ['post',   '/admin/employee-positions'],
        ['get',    '/admin/employee-positions/1'],
        ['put',    '/admin/employee-positions/1'],
        ['delete', '/admin/employee-positions/1'],
    ];

    public function test_index_first_page_without_filter(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->getJson('/admin/employee-positions');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);

        // Employee positions should be sorted by name
        $this->assertLessThanOrEqual(array_get($data, '1.name'), array_get($data, '0.name'));
    }

    public function test_store_new(): void
    {
        $this->actingAsMasterAdmin();

        $data = [
            'name' => 'Data Scientist',
            'name_plural' => 'Data Scientists',
            'hireable' => true,
            'seniorities' => null,
        ];

        $response = $this->postJson('/admin/employee-positions', $data);
        $response->assertSuccessful();

        $stored = $response->json('data');
        $this->assertNotNull($stored['id']);
        $this->assertEquals($data['name'], $stored['name']);
        $this->assertEquals($data['name_plural'], $stored['name_plural']);
        $this->assertTrue($stored['hireable']);
        $this->assertEmpty($stored['seniorities']);
    }

    public function test_show(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->getJson('/admin/employee-positions/2');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertEquals('Frontend Developer', $data['name']);
        $this->assertEquals(['junior', 'medior', 'senior', 'lead'], $data['seniorities']);
    }

    public function test_update(): void
    {
        $this->actingAsMasterAdmin();

        $data = [
            'name' => 'Project Manager', // changed
            'name_plural' => 'Project Managers', // changed
            'hireable' => false,
            'seniorities' => ['senior', 'lead'], // changed
        ];

        $response = $this->putJson('/admin/employee-positions/11', $data);
        $response->assertSuccessful();

        $showResponse = $this->getJson('/admin/employee-positions/11');
        $showResponse->assertSuccessful();

        $stored = $showResponse->json('data');
        $this->assertEquals($data['name'], $stored['name']);
        $this->assertEquals($data['name_plural'], $stored['name_plural']);
        $this->assertEquals($data['hireable'], $stored['hireable']);
        $this->assertEquals($data['seniorities'], $stored['seniorities']);
    }

    public function test_delete(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->deleteJson('/admin/employee-positions/1');
        $response->assertSuccessful();

        $showResponse = $this->getJson('/admin/employee-positions/1');
        $showResponse->assertNotFound();
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    public function test_clients_cannot_access_endpoints(): void
    {
        $this->actingAsGenericVendorClient();
        $this->assertEndpointsInaccessible($this->endpoints);
    }
}
