<?php

namespace Tests\Feature\Admin;

use App\Models\Company;
use Tests\Feature\FeatureTestCase;

class VendorsControllerTest extends FeatureTestCase
{
    private array $endpoints = [
        ['get',    '/admin/companies/1/vendor'],
        ['put',    '/admin/companies/1/vendor'],
    ];

    public function test_show(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->getJson("/admin/companies/$this->PUBLIC_VENDOR_COMPANY_PUBLIC_ID/vendor");
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('published', $data['publish_status']);
        $this->assertTrue($data['offering_resources']);
        $this->assertEquals(12, $data['main_industry_id']); // Banking & Fintech
    }

    public function test_show_vendor_with_deleted_company_should_not_be_present(): void
    {
        $this->actingAsMasterAdmin();
        Company::query()
            ->slug($this->PUBLIC_VENDOR_COMPANY_PUBLIC_ID)
            ->delete();

        $response = $this->getJson("/admin/companies/$this->PUBLIC_VENDOR_COMPANY_PUBLIC_ID/vendor");
        $response->assertNotFound();
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    public function test_clients_cannot_access_endpoints(): void
    {
        $this->actingAsGenericClient();
        $this->assertEndpointsInaccessible($this->endpoints);
    }
}
