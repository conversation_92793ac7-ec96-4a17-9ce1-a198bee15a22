<?php

namespace Tests\Feature\Admin;

use Tests\Feature\FeatureTestCase;

class TenderCandidatesTest extends FeatureTestCase
{
    private array $endpoints = [
        ['get',    '/admin/tenders/1/candidates'],
        ['get',    '/admin/tenders/1/candidates/1'],
        ['put',    '/admin/tenders/1/candidates/1'],
        ['put',    '/admin/tenders/1/candidates/1/status'],
        ['delete', '/admin/tenders/1/candidates/1'],
    ];

    public function test_update_status_of_candidate(): void
    {
        $this->actingAsMasterAdmin();
        $candidateSlug = $this->prepareCandidateForTender();

        $updateResponse = $this->putJson(
            "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
            ['status' => 'interested'],
        );
        $updateResponse->assertSuccessful();

        $candidatesResponse = $this->getJson("admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $candidatesResponse->assertSuccessful();

        $data = $candidatesResponse->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);
        $this->assertEquals('interested', array_get($data, '0.status'));
    }

    public function test_can_change_tender_candidate_status_to_any_status(): void
    {
        $this->actingAsMasterAdmin();

        $applyResponse = $this->putJson(
            "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/positions/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID/apply",
            ['candidate_id' => $this->PUBLIC_VENDOR_CANDIDATE_JANE_PUBLIC_ID],
        );
        $applyResponse->assertSuccessful();

        $candidatesResponse = $this->getJson("admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $candidatesResponse->assertSuccessful();

        $candidatesData = $candidatesResponse->json('data');
        $this->assertIsArray($candidatesData);
        $this->assertCount(1, $candidatesData);
        $this->assertEquals('awaiting_approval', array_get($candidatesData, '0.status'));
        $candidateId = array_get($candidatesData, '0.id');
        $this->assertNotNull($candidateId);

        $updateResponse = $this->putJson(
            "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateId/status",
            ['status' => 'hired'],
        );
        $updateResponse->assertSuccessful();

        $secondUpdateResponse = $this->putJson(
            "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateId/status",
            ['status' => 'interested'],
        );
        $secondUpdateResponse->assertSuccessful();
    }

    public function test_update_status_to_not_interested_should_allow_empty_rejection_reason(): void
    {
        $this->actingAsMasterAdmin();
        $candidateSlug = $this->prepareCandidateForTender();

        $updateResponse = $this->putJson(
            "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
            [
                'status' => 'not_interested',
                'rejection_reason' => null,
            ],
        );
        $updateResponse->assertSuccessful();

        $candidatesResponse = $this->getJson("admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $candidatesResponse->assertSuccessful();

        $data = $candidatesResponse->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);
        $this->assertEquals('not_interested', array_get($data, '0.status'));
    }

    public function test_update_status_to_rejected_should_allow_empty_rejection_reason(): void
    {
        $this->actingAsMasterAdmin();
        $candidateSlug = $this->prepareCandidateForTender();

        $updateResponse = $this->putJson(
            "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
            [
                'status' => 'not_interested',
                'rejection_reason' => null,
            ],
        );
        $updateResponse->assertSuccessful();

        $candidatesResponse = $this->getJson("admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $candidatesResponse->assertSuccessful();

        $data = $candidatesResponse->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);
        $this->assertEquals('not_interested', array_get($data, '0.status'));
    }

    public function test_update_status_to_not_hired_should_require_rejection_reason(): void
    {
        $this->actingAsMasterAdmin();
        $candidateSlug = $this->prepareCandidateForTender(status: 'interviewed_interested');

        $response = $this->putJson(
            "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
            [
                'status' => 'not_hired',
                'rejection_reason' => null,
            ],
        );
        $response->assertUnprocessable();
        $response->assertJsonValidationErrorFor('rejection_reason', 'error.errors');
    }

    public function test_update_status_to_interested_should_prohibit_rejection_reason(): void
    {
        $this->actingAsMasterAdmin();
        $candidateSlug = $this->prepareCandidateForTender();

        $response = $this->putJson(
            "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
            [
                'status' => 'interested',
                'rejection_reason' => 'Should not be possible to set me',
            ],
        );
        $response->assertUnprocessable();
        $response->assertJsonValidationErrorFor('rejection_reason', 'error.errors');
    }

    public function test_update_status_should_persist_rejection_reason(): void
    {
        $this->actingAsMasterAdmin();
        $candidateSlug = $this->prepareCandidateForTender();

        $updateResponse = $this->putJson(
            "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
            [
                'status' => 'not_interested',
                'rejection_reason' => 'I do not like you',
            ],
        );
        $updateResponse->assertSuccessful();

        $candidateResponse = $this->getJson("admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug");
        $candidateResponse->assertSuccessful();

        $data = $candidateResponse->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);
        $this->assertEquals('not_interested', array_get($data, 'status'));
        $this->assertEquals('I do not like you', array_get($data, 'rejection_reason'));
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    public function test_clients_cannot_access_endpoints(): void
    {
        $this->actingAsGenericVendorClient();
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    private function prepareCandidateForTender(string $status = 'approved'): string
    {
        $applyResponse = $this->putJson(
            "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/positions/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID/apply",
            ['candidate_id' => $this->PUBLIC_VENDOR_CANDIDATE_JANE_PUBLIC_ID],
        );
        $applyResponse->assertSuccessful();

        $candidatesResponse = $this->getJson("admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $candidatesResponse->assertSuccessful();
        $candidateSlug = $candidatesResponse->json('data.0.id');

        if ($status !== 'awaiting_approval') {
            $approveResponse = $this->putJson(
                "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
                ['status' => $status],
            );
            $approveResponse->assertSuccessful();
        }

        return $candidateSlug;
    }
}
