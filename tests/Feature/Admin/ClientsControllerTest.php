<?php

namespace Tests\Feature\Admin;

use Tests\Feature\FeatureTestCase;

class ClientsControllerTest extends FeatureTestCase
{
    private array $endpoints = [
        ['get',    '/admin/clients'],
        ['post',   '/admin/clients'],
        ['get',    '/admin/clients/1'],
        ['put',    '/admin/clients/1'],
        ['delete', '/admin/clients/1'],
    ];

    public function test_index_first_page_without_filter(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->getJson('/admin/clients');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);

        // Clients should be sorted by name
        $this->assertLessThanOrEqual(array_get($data, '1.name'), array_get($data, '0.name'));
    }

    public function test_store_new(): void
    {
        $this->actingAsMasterAdmin();

        $data = [
            'name' => 'Amazon',
            'description' => 'Amazon.com focuses on e-commerce, cloud computing, online advertising, digital streaming, and AI.',
            'logo_resource_id' => null,
        ];

        $response = $this->postJson('/admin/clients', $data);
        $response->assertSuccessful();

        $stored = $response->json('data');
        $this->assertNotNull($stored['id']);
        $this->assertEquals($data['name'], $stored['name']);
        $this->assertEquals($data['description'], $stored['description']);
    }

    public function test_show(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->getJson('/admin/clients/30');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertEquals('IBM', $data['name']);
        $this->assertNull($data['description']);
    }

    public function test_update(): void
    {
        $this->actingAsMasterAdmin();

        $data = [
            'name' => 'Kofola Slovakia', // changed
            'description' => 'Keď ju miluješ, nie je čo riešiť.', // changed
            'logo_resource_id' => null,
        ];

        $response = $this->putJson('/admin/clients/36', $data);
        $response->assertSuccessful();

        $showResponse = $this->getJson('/admin/clients/36');
        $showResponse->assertSuccessful();

        $stored = $showResponse->json('data');
        $this->assertEquals($data['name'], $stored['name']);
        $this->assertEquals($data['description'], $stored['description']);
    }

    public function test_delete(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->deleteJson('/admin/clients/1');
        $response->assertSuccessful();

        $showResponse = $this->getJson('/admin/clients/1');
        $showResponse->assertNotFound();
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    public function test_clients_cannot_access_endpoints(): void
    {
        $this->actingAsGenericVendorClient();
        $this->assertEndpointsInaccessible($this->endpoints);
    }
}
