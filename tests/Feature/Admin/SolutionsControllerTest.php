<?php

namespace Tests\Feature\Admin;

use Tests\Feature\FeatureTestCase;

class SolutionsControllerTest extends FeatureTestCase
{
    private array $endpoints = [
        ['get',    '/admin/solutions'],
        ['post',   '/admin/solutions'],
        ['get',    '/admin/solutions/1'],
        ['put',    '/admin/solutions/1'],
        ['delete', '/admin/solutions/1'],
    ];

    public function test_update_solution(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->putJson("/admin/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID", [
            'as_draft' => false,

            'name' => 'AnoSol',
            'description' => 'Another great solution guys',
            'about' => '<h1>THIS IS GOING TO BE GREAT</h1><p>Seriously I mean it</p>',
            'cover_resource_id' => '4Hq5t2',
            'country' => 'es',
            'main_industry_id' => 81,
            'length_type' => 'ongoing',
            'length' => null,
            'ftes' => 30,
            'value' => 10_000_000,

            'industries' => [24, 58],
            'technologies' => [100, 218],

            'in_house' => false,

            'client' => [
                'anonymous' => false,
                'id' => null,
                'name' => 'Labaš, s. r. o.',
                'review' => '<p>So far so good</p>',
                'reviewer' => 'Luky',
                'reviewer_position' => 'CEO',
            ],

            'media' => [
                [
                    'type' => 'image',
                    'source' => $this->SOLUTION_MEDIA_IMAGE_RESOURCE_PUBLIC_ID,
                ],
            ],
        ]);
        $response->assertSuccessful();

        $showResponse = $this->getJson("/admin/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID");
        $showResponse->assertSuccessful();

        $data = $showResponse->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('published', $data['publish_status']);
        $this->assertEquals('AnoSol', $data['name']);
        $this->assertEquals('<h1>THIS IS GOING TO BE GREAT</h1><p>Seriously I mean it</p>', $data['about']);
        $this->assertArrayHasKey('client', $data);
        $this->assertFalse($data['in_house']);
        $this->assertFalse(array_get($data, 'client.anonymous'));
        $this->assertEquals('So far so good', array_get($data, 'client.review'));
        $this->assertNotEmpty($data['media']);
        $this->assertEquals($this->SOLUTION_MEDIA_IMAGE_RESOURCE_PUBLIC_ID, array_get($data, 'media.0.source.id'));
        $this->assertNull($data['unapproved_change']);
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    public function test_clients_cannot_access_endpoints(): void
    {
        $this->actingAsGenericVendorClient();
        $this->assertEndpointsInaccessible($this->endpoints);
    }
}
