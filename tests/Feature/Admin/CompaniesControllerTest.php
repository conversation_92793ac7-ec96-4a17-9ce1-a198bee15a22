<?php

namespace Tests\Feature\Admin;

use Tests\Feature\FeatureTestCase;

class CompaniesControllerTest extends FeatureTestCase
{
    private array $endpoints = [
        ['get',    '/admin/companies'],
        ['post',   '/admin/companies'],
        ['get',    '/admin/companies/1'],
        ['put',    '/admin/companies/1'],
        ['delete', '/admin/companies/1'],
    ];

    public function test_delete_should_fail_for_vendor_with_ended_tender()
    {
        $this->actingAsMasterAdmin();

        $response = $this->deleteJson("/admin/companies/$this->HIDDEN_VENDOR_COMPANY_PUBLIC_ID");
        $response->assertConflict();
        $this->assertEquals('model_still_in_relationships', $response->json('error.type'));
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    public function test_clients_cannot_access_endpoints(): void
    {
        $this->actingAsGenericClient();
        $this->assertEndpointsInaccessible($this->endpoints);
    }
}
