<?php

namespace Tests\Feature\Admin;

use Tests\Feature\FeatureTestCase;

class IndustriesControllerTest extends FeatureTestCase
{
    private array $endpoints = [
        ['get',    '/admin/industries'],
        ['post',   '/admin/industries'],
        ['get',    '/admin/industries/1'],
        ['put',    '/admin/industries/1'],
        ['delete', '/admin/industries/1'],
    ];

    public function test_index_first_page_without_filter(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->getJson('/admin/industries');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);

        // Industries should be sorted by featured and then by name
        $this->assertTrue(array_get($data, '0.featured'));
        $this->assertLessThanOrEqual(array_get($data, '1.name'), array_get($data, '0.name'));
    }

    public function test_store_new(): void
    {
        $this->actingAsMasterAdmin();

        $data = [
            'name' => 'Competitions',
            'parent_id' => null,
            'featured' => true,
        ];

        $response = $this->postJson('/admin/industries', $data);
        $response->assertSuccessful();

        $stored = $response->json('data');
        $this->assertNotNull($stored['id']);
        $this->assertEquals($data['name'], $stored['name']);
        $this->assertArrayNotHasKey('parent', $stored);
        $this->assertEquals($data['featured'], $stored['featured']);
    }

    public function test_show(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->getJson('/admin/industries/30');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertEquals('Data Analytics', $data['name']);
        $this->assertArrayHasKey('parent', $data);
        $this->assertEquals('Big Data', array_get($data, 'parent.name'));
    }

    public function test_update(): void
    {
        $this->actingAsMasterAdmin();

        $data = [
            'name' => 'DevSec Operations', // changed
            'parent_id' => 35, // changed
            'featured' => false,
        ];

        $response = $this->putJson('/admin/industries/36', $data);
        $response->assertSuccessful();

        $showResponse = $this->getJson('/admin/industries/36');
        $showResponse->assertSuccessful();

        $stored = $showResponse->json('data');
        $this->assertEquals($data['name'], $stored['name']);
        $this->assertArrayHasKey('parent', $stored);
        $this->assertEquals($data['parent_id'], array_get($stored, 'parent.id'));
    }

    public function test_delete(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->deleteJson('/admin/industries/1');
        $response->assertSuccessful();

        $showResponse = $this->getJson('/admin/industries/1');
        $showResponse->assertNotFound();
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    public function test_clients_cannot_access_endpoints(): void
    {
        $this->actingAsGenericVendorClient();
        $this->assertEndpointsInaccessible($this->endpoints);
    }
}
