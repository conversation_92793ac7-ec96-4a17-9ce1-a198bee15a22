<?php

namespace Tests\Feature\Admin;

use Tests\Feature\FeatureTestCase;

class CandidatesTest extends FeatureTestCase
{
    private array $endpoints = [
        ['get',    '/admin/candidates'],
        ['post',   '/admin/candidates'],
        ['post',   '/admin/candidates/cv'],
        ['get',    '/admin/candidates/1'],
        ['put',    '/admin/candidates/1'],
        ['delete', '/admin/candidates/1'],
    ];

    public function test_store_new(): void
    {
        $this->actingAsMasterAdmin();

        $data = [
            'vendor_id' => $this->PUBLIC_VENDOR_COMPANY_VENDOR_PUBLIC_ID,
            'cv_resource_id' => null,
            'internal_name' => '<PERSON>',
            'name' => 'Joe Who',
            'profession' => 'Software Engineer',
            'seniority' => 'medior',
            'rate' => 25,
            'last_job_title' => 'Java Developer',
            'years_of_experience' => 3,
            'highest_education' => 'bachelor',
            'field_of_study' => 'Computer Science',
            'country' => 'gb',
            'city' => null,

            'experiences' => [
                [
                    'pseudo_id' => 3_345_089,
                    'name' => 'Software Engineer at Fintech Company',
                    'description' => 'My first job which I can be proud of',
                    'length_type' => 'ongoing',
                    'length' => null,
                ],
            ],

            'skills' => [
                [
                    'technology_id' => 100, // Java
                    'years_of_experience' => 4,
                    'new_experiences' => [3_345_089],
                ],
            ],
        ];

        $response = $this->postJson('/admin/candidates', $data);
        $response->assertSuccessful();

        $stored = $response->json('data');
        $this->assertNotNull($stored['id']);
        $this->assertEquals('Joe Mama', $stored['internal_name']);
        $this->assertEquals('gb', $stored['country']);

        $this->assertNotEmpty($stored['skills']);
        $this->assertEquals(100, array_get($stored, 'skills.0.technology_id'));
        $this->assertEquals(4, array_get($stored, 'skills.0.years_of_experience'));
        $this->assertContains(array_get($stored, 'experiences.0.id'), array_get($stored, 'skills.0.experiences'));

        $this->assertNotEmpty($stored['experiences']);
        $this->assertEquals('Software Engineer at Fintech Company', array_get($stored, 'experiences.0.name'));
        $this->assertEquals('ongoing', array_get($stored, 'experiences.0.length_type'));
        $this->assertNull(array_get($stored, 'experiences.0.length'));
    }

    public function test_show(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->getJson("/admin/candidates/$this->PUBLIC_VENDOR_CANDIDATE_JANE_PUBLIC_ID");
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertEquals($this->PUBLIC_VENDOR_CANDIDATE_JANE_PUBLIC_ID, $data['id']);
        $this->assertEquals('Joan of Ark', $data['internal_name']);
        $this->assertEquals('pl', $data['country']);

        $this->assertCount(2, $data['skills']);
        $androidTechnology = array_first($data['skills'], fn (array $skill) => $skill['technology_id'] === 10);
        $this->assertNotNull($androidTechnology);
        $this->assertEquals(8, $androidTechnology['years_of_experience']);
        $this->assertContains($this->PUBLIC_VENDOR_CANDIDATE_JANE_EXPERIENCE_PUBLIC_ID, $androidTechnology['experiences']);

        $this->assertCount(1, $data['experiences']);
        $androidExperience = array_first($data['experiences'], fn (array $experience) => $experience['id'] === $this->PUBLIC_VENDOR_CANDIDATE_JANE_EXPERIENCE_PUBLIC_ID);
        $this->assertNotNull($androidExperience);
        $this->assertEquals('Senior Android Developer at Large Automotive Company', $androidExperience['name']);
        $this->assertEquals('years', $androidExperience['length_type']);
        $this->assertEquals(5, $androidExperience['length']);
    }

    public function test_update(): void
    {
        $this->actingAsMasterAdmin();

        $data = [
            'cv_resource_id' => null,
            'internal_name' => 'Joan of Arc', // Changed
            'name' => 'Jane',
            'profession' => 'Software Engineer',
            'seniority' => 'lead', // Changed
            'rate' => 40,
            'last_job_title' => 'Lead Android Developer', // Changed
            'years_of_experience' => 10,
            'highest_education' => 'master',
            'field_of_study' => 'Computer Science',
            'country' => 'pl',
            'city' => null, // Changed

            'experiences' => [
                [
                    'id' => $this->PUBLIC_VENDOR_CANDIDATE_JANE_EXPERIENCE_PUBLIC_ID,
                    'name' => 'Senior Android Developer at Large Automotive Company',
                    'description' => 'Started as an intern on this position and slowly build up my way to the top. Key team player.',
                    'length_type' => 'years',
                    'length' => 10, // Changed
                ],
            ],

            'skills' => [
                [
                    'technology_id' => 100, // Java
                    'years_of_experience' => 11, // Changed
                    'experiences' => [$this->PUBLIC_VENDOR_CANDIDATE_JANE_EXPERIENCE_PUBLIC_ID],
                    'new_experiences' => [],
                ],
                [
                    'technology_id' => 10, // Android
                    'years_of_experience' => 8,
                    'experiences' => [], // Changed
                    'new_experiences' => [],
                ],
                // New
                [
                    'technology_id' => 107, // Kotlin
                    'years_of_experience' => 2,
                    'experiences' => [$this->PUBLIC_VENDOR_CANDIDATE_JANE_EXPERIENCE_PUBLIC_ID],
                    'new_experiences' => [],
                ],
            ],
        ];

        $updateResponse = $this->putJson("/admin/candidates/$this->PUBLIC_VENDOR_CANDIDATE_JANE_PUBLIC_ID", $data);
        $updateResponse->assertSuccessful();

        $showResponse = $this->getJson("/admin/candidates/$this->PUBLIC_VENDOR_CANDIDATE_JANE_PUBLIC_ID");
        $showResponse->assertSuccessful();

        $updated = $showResponse->json('data');
        $this->assertEquals($data['internal_name'], $updated['internal_name']);
        $this->assertEquals($data['name'], $updated['name']);
        $this->assertEquals($data['seniority'], $updated['seniority']);
        $this->assertEquals($data['last_job_title'], $updated['last_job_title']);
        $this->assertEquals($data['city'], $updated['city']);
        $this->assertCount(3, $updated['skills']);
        $javaTechnology = array_first($updated['skills'], fn (array $skill) => $skill['technology_id'] === 100);
        $this->assertNotNull($javaTechnology);
        $this->assertEquals(11, $javaTechnology['years_of_experience']);
        $this->assertEquals($this->PUBLIC_VENDOR_CANDIDATE_JANE_EXPERIENCE_PUBLIC_ID, array_get($data, 'skills.2.experiences.0'));
        $this->assertEquals($this->PUBLIC_VENDOR_CANDIDATE_JANE_EXPERIENCE_PUBLIC_ID, array_get($data, 'experiences.0.id'));
        $this->assertEquals(10, array_get($data, 'experiences.0.length'));
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    public function test_clients_cannot_access_endpoints(): void
    {
        $this->actingAsGenericClient();
        $this->assertEndpointsInaccessible($this->endpoints);
    }
}
