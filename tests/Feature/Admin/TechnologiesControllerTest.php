<?php

namespace Tests\Feature\Admin;

use Tests\Feature\FeatureTestCase;

class TechnologiesControllerTest extends FeatureTestCase
{
    private array $endpoints = [
        ['get',    '/admin/technologies'],
        ['post',   '/admin/technologies'],
        ['get',    '/admin/technologies/1'],
        ['put',    '/admin/technologies/1'],
        ['delete', '/admin/technologies/1'],
    ];

    public function test_index_first_page_without_filter(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->getJson('/admin/technologies');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);

        // Technologies should be sorted by featured and then by name
        $this->assertTrue(array_get($data, '0.featured'));
        $this->assertLessThanOrEqual(array_get($data, '1.name'), array_get($data, '0.name'));
    }

    public function test_index_filter_by_name(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->getJson('/admin/technologies?filters[name]=Ja');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertTrue(
            collect($data)->every(fn ($technology) => str_contains(mb_strtolower($technology['name']), 'ja')),
        );
    }

    public function test_index_filter_by_parent_id(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->getJson('/admin/technologies?filters[parent_id]=100,168');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertTrue(
            collect($data)->every(fn ($technology) => in_array($technology['parent_id'], [100, 168])),
        );
    }

    public function test_store_new(): void
    {
        $this->actingAsMasterAdmin();

        $data = [
            'name' => 'Tailwind CSS',
            'emsi_id' => null,
            'parent_id' => null,
            'featured' => true,
            'logo_resource_id' => null,
        ];

        $response = $this->postJson('/admin/technologies', $data);
        $response->assertSuccessful();

        $stored = $response->json('data');
        $this->assertNotNull($stored['id']);
        $this->assertEquals($data['name'], $stored['name']);
        $this->assertArrayNotHasKey('parent', $stored);
        $this->assertEquals($data['featured'], $stored['featured']);
    }

    public function test_show(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->getJson('/admin/technologies/171');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertEquals('Redux', $data['name']);
        $this->assertArrayHasKey('parent', $data);
        $this->assertEquals('React', array_get($data, 'parent.name'));
    }

    public function test_update(): void
    {
        $this->actingAsMasterAdmin();

        $data = [
            'name' => 'Spring Framework', // changed
            'emsi_id' => 'KS125HH5XDBPZT3RFGZZ',
            'parent_id' => 100, // changed
            'featured' => false,
            'logo_resource_id' => null,
        ];

        $response = $this->putJson('/admin/technologies/196', $data);
        $response->assertSuccessful();

        $showResponse = $this->getJson('/admin/technologies/196');
        $showResponse->assertSuccessful();

        $stored = $showResponse->json('data');
        $this->assertEquals($data['emsi_id'], $stored['emsi_id']);
        $this->assertEquals($data['name'], $stored['name']);
        $this->assertArrayHasKey('parent', $stored);
        $this->assertEquals($data['parent_id'], array_get($stored, 'parent.id'));
    }

    public function test_delete(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->deleteJson('/admin/technologies/215');
        $response->assertSuccessful();

        $showResponse = $this->getJson('/admin/technologies/215');
        $showResponse->assertNotFound();
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    public function test_clients_cannot_access_endpoints(): void
    {
        $this->actingAsGenericVendorClient();
        $this->assertEndpointsInaccessible($this->endpoints);
    }
}
