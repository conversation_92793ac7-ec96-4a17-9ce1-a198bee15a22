<?php

namespace Tests\Feature\Enterprise;

use Tests\Feature\FeatureTestCase;

class TenderControllerTest extends FeatureTestCase
{
    private array $endpoints = [
        ['get',    '/enterprise/tenders'],
        ['get',    '/enterprise/tenders/1'],
        ['put',    '/enterprise/tenders/1'],
    ];

    public function test_update_tender_submission_deadline(): void
    {
        $this->actingAsMasterAdmin();

        $newDeadline = now()->addDays(14)->format('Y-m-d H:i:s');

        $response = $this->putJson(
            "/enterprise/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID",
            ['submissions_deadline' => $newDeadline]
        );

        $response->assertSuccessful();

        // Verify the deadline was updated in the response
        $tenderData = $response->json('data');
        $this->assertEquals($newDeadline, $tenderData['submissions_deadline']);
    }

    public function test_update_tender_validation_fails_for_past_date(): void
    {
        $this->actingAsMasterAdmin();

        $pastDeadline = now()->subDays(1)->format('Y-m-d H:i:s');

        $response = $this->putJson(
            "/enterprise/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID",
            ['submissions_deadline' => $pastDeadline]
        );

        $response->assertUnprocessable();
        $this->assertArrayHasKey('submissions_deadline', $response->json('errors'));
    }

    public function test_update_tender_allows_partial_updates(): void
    {
        $this->actingAsMasterAdmin();

        // Test that we can send an empty request (no required fields)
        $response = $this->putJson(
            "/enterprise/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID",
            []
        );

        $response->assertSuccessful();

        // Should return the tender data
        $tenderData = $response->json('data');
        $this->assertIsArray($tenderData);
        $this->assertArrayHasKey('id', $tenderData);
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    public function test_vendors_cannot_access_endpoints(): void
    {
        $this->actingAsGenericVendorClient();
        $this->assertEndpointsInaccessible($this->endpoints);
    }
}
