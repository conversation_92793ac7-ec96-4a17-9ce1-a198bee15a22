<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Testing\TestResponse;
use Tests\Feature\Fixtures\AuthFixtures;
use Tests\Feature\Fixtures\CandidatesFixtures;
use Tests\Feature\Fixtures\CompaniesFixtures;
use Tests\Feature\Fixtures\ResourcesFixtures;
use Tests\Feature\Fixtures\SolutionsFixtures;
use Tests\Feature\Fixtures\TendersFixtures;
use Tests\TestCase;

abstract class FeatureTestCase extends TestCase
{
    use AuthFixtures;
    use CandidatesFixtures;
    use CompaniesFixtures;
    use RefreshDatabase;
    use ResourcesFixtures;
    use SolutionsFixtures;
    use TendersFixtures;

    /**
     * Indicates whether the default seeder should run before each test.
     *
     * @var bool
     */
    protected $seed = true;

    /**
     * Verifies that currently configured user or guest cannot access
     * any of the given endpoints.
     *
     * @param  array  $endpoints  list of tuples of request method and endpoints
     */
    protected function assertEndpointsInaccessible(array $endpoints): void
    {
        foreach ($endpoints as [$method, $endpoint]) {
            /** @var TestResponse $response */
            $response = $this->{$method}($endpoint);
            static::assertTrue(
                $response->status() === 401 || $response->status() === 403,
                "Expected endpoint [$endpoint] to fail with status [401 Unauthorized] or [403 Forbidden], got [{$response->status()} {$response->statusText()}]",
            );
        }
    }

    /**
     * Reads given and parses JSON test data.
     *
     * @param  string  $name  name of the file relative to `tests/data` directory
     * @param  bool|null  $associative  when `true`, returned objects will be converted into associative arrays [default: true]
     * @return mixed decoded file contents
     */
    protected function loadJsonTestData(string $name, ?bool $associative = true): mixed
    {
        $path = base_path("tests/data/$name");
        $contents = file_get_contents($path);

        return json_decode($contents, $associative);
    }
}
