<?php

namespace Tests\Feature\Auth;

use <PERSON>hskohbo\NoCaptcha\Facades\NoCaptcha;
use App\Models\PasswordReset;
use Tests\Feature\FeatureTestCase;

class AuthenticationTest extends FeatureTestCase
{
    public function test_login_fails_with_incorrect_credentials(): void
    {
        $response = $this->postJson('/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'wrong password',
        ]);

        $response->assertUnauthorized();
        $response->assertJsonStructure([
            'error' => [
                'message',
            ],
        ]);
    }

    public function test_staff_log_in(): void
    {
        $response = $this->postJson('/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Password1!',
        ]);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'refresh_token',
                'jwt',
            ],
        ]);
        $jwt = $response->json('data.jwt');

        $profileResponse = $this->getJson('/profile', [
            'Authorization' => "Bearer $jwt",
        ]);
        $profileResponse->assertSuccessful();
        $this->assertEquals('<EMAIL>', $profileResponse->json('data.email'));
    }

    public function test_client_log_in(): void
    {
        $response = $this->postJson('/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Password1!',
        ]);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'refresh_token',
                'jwt',
            ],
        ]);
        $jwt = $response->json('data.jwt');

        $profileResponse = $this->getJson('/profile', [
            'Authorization' => "Bearer $jwt",
        ]);
        $profileResponse->assertSuccessful();
        $this->assertEquals('<EMAIL>', $profileResponse->json('data.email'));
    }

    public function test_client_of_unapproved_company_should_not_be_able_to_log_in(): void
    {
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'deleted_at' => null,
        ]);

        $response = $this->postJson('/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Password1!',
        ]);

        $response->assertUnauthorized();
    }

    public function test_refresh_token(): void
    {
        $loginResponse = $this->postJson('/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Password1!',
        ]);
        $loginResponse->assertSuccessful();
        $jwt = $loginResponse->json('data.jwt');

        $response = $this->postJson('/auth/token', [
            'refresh_token' => $loginResponse->json('data.refresh_token'),
        ], [
            'Authorization' => "Bearer $jwt",
        ]);

        $response->assertSuccessful();
        $response->assertJsonStructure([
            'data' => [
                'jwt',
            ],
        ]);
    }

    public function test_logout(): void
    {
        $loginResponse = $this->postJson('/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Password1!',
        ]);
        $loginResponse->assertSuccessful();
        $jwt = $loginResponse->json('data.jwt');

        $profileResponse = $this->getJson('/profile', [
            'Authorization' => "Bearer $jwt",
        ]);
        $profileResponse->assertSuccessful();

        $response = $this->postJson('/auth/logout', [], [
            'Authorization' => "Bearer $jwt",
        ]);
        $response->assertSuccessful();

        $refreshResponse = $this->postJson('/auth/token', [
            'refresh_token' => $loginResponse->json('data.refresh_token'),
        ], [
            'Authorization' => "Bearer $jwt",
        ]);
        $refreshResponse->assertUnauthorized();
    }

    public function test_password_reset_should_silently_fail_for_non_existing_accounts(): void
    {
        NoCaptcha::shouldReceive('verifyResponse')->once()->andReturn(true);

        $requestResponse = $this->postJson('/auth/password-reset', [
            'email' => '<EMAIL>',
            'recaptcha' => 1,
        ]);

        $requestResponse->assertSuccessful();
        $this->assertDatabaseMissing('password_resets', [
            'email' => '<EMAIL>',
        ]);
    }

    public function test_password_reset_should_not_work_for_staff_accounts(): void
    {
        NoCaptcha::shouldReceive('verifyResponse')->once()->andReturn(true);

        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'deleted_at' => null,
        ]);

        $requestResponse = $this->postJson('/auth/password-reset', [
            'email' => '<EMAIL>',
            'recaptcha' => 1,
        ]);

        $requestResponse->assertSuccessful();
        $this->assertDatabaseMissing('password_resets', [
            'email' => '<EMAIL>',
        ]);
    }

    public function test_password_reset_should_work_for_clients(): void
    {
        NoCaptcha::shouldReceive('verifyResponse')->once()->andReturn(true);

        $requestResponse = $this->postJson('/auth/password-reset', [
            'email' => '<EMAIL>',
            'recaptcha' => 1,
        ]);
        $requestResponse->assertSuccessful();

        /** @var PasswordReset $passwordReset */
        $passwordReset = PasswordReset::where('email', '<EMAIL>')->first();
        $this->assertNotNull($passwordReset);

        $resetResponse = $this->postJson('/auth/password-reset', [
            'token' => $passwordReset->token,
            'password' => 'Password2!',
            'password_confirmation' => 'Password2!',
        ]);
        $resetResponse->assertSuccessful();

        $response = $this->postJson('/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Password2!',
        ]);
        $response->assertSuccessful();
    }
}
