<?php

namespace Tests\Feature\Company;

use GuzzleHttp\Promise\RejectedPromise;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Tests\Feature\FeatureTestCase;

class CandidatesTest extends FeatureTestCase
{
    private array $endpoints = [
        ['get',    '/company/candidates'],
        ['post',   '/company/candidates/cv'],
        ['get',    '/company/candidates/1'],
        ['put',    '/company/candidates/1'],
        ['delete', '/company/candidates/1'],
    ];

    public function test_index_own_candidates(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson('/company/candidates');
        $response->assertSuccessful();

        $ids = collect(array_pluck($response->json('data'), 'id'));
        $this->assertNotEmpty($ids);
        $this->assertTrue($ids->some(fn ($id) => $id === $this->PUBLIC_VENDOR_CANDIDATE_JANE_PUBLIC_ID));
        $this->assertFalse($ids->some(fn ($id) => $id === $this->PUBLIC_SECOND_VENDOR_CANDIDATE_GERMAIN_PUBLIC_ID));
    }

    public function test_show_own_candidate(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson("/company/candidates/$this->PUBLIC_VENDOR_CANDIDATE_JANE_PUBLIC_ID");
        $response->assertSuccessful();
        $data = $response->json('data');
        $this->assertIsArray($data);

        $this->assertEquals('Jane', array_get($data, 'name'));
        $this->assertEquals('senior', array_get($data, 'seniority'));

        $this->assertCount(2, $data['skills']);
        $androidTechnology = array_first($data['skills'], fn (array $skill) => $skill['technology_id'] === 10);
        $this->assertNotNull($androidTechnology);
        $this->assertEquals(8, $androidTechnology['years_of_experience']);
        $this->assertContains($this->PUBLIC_VENDOR_CANDIDATE_JANE_EXPERIENCE_PUBLIC_ID, $androidTechnology['experiences']);

        $this->assertCount(1, $data['experiences']);
        $androidExperience = array_first($data['experiences'], fn (array $experience) => $experience['id'] === $this->PUBLIC_VENDOR_CANDIDATE_JANE_EXPERIENCE_PUBLIC_ID);
        $this->assertNotNull($androidExperience);
        $this->assertEquals('Senior Android Developer at Large Automotive Company', $androidExperience['name']);
        $this->assertEquals('years', $androidExperience['length_type']);
        $this->assertEquals(5, $androidExperience['length']);
    }

    public function test_should_not_be_able_to_get_foreign_candidate(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson("/company/candidates/$this->PUBLIC_SECOND_VENDOR_CANDIDATE_GERMAIN_PUBLIC_ID");
        $response->assertNotFound();
    }

    public function test_edit_own_candidate(): void
    {
        $this->actingAsGenericVendorClient();

        $updateResponse = $this->putJson(
            "/company/candidates/$this->PUBLIC_VENDOR_CANDIDATE_JANE_PUBLIC_ID",
            [
                'internal_name' => 'Joan of Ark',
                'name' => 'Joan', // Changed
                'country' => 'pl',
                'city' => 'Kraków',
                'rate' => 40,

                'profession' => 'Software Engineer',
                'seniority' => 'lead', // Changed
                'last_job_title' => 'Senior Android Developer',
                'years_of_experience' => 10,
                'highest_education' => 'master',
                'field_of_study' => 'Computer Science',

                'experiences' => [
                    [
                        'id' => $this->PUBLIC_VENDOR_CANDIDATE_JANE_EXPERIENCE_PUBLIC_ID,
                        'name' => 'Senior Android Developer at Large Automotive Company',
                        'description' => 'Started as an intern on this position and slowly build up my way to the top. Key team player.',
                        'length_type' => 'years',
                        'length' => 10, // Changed
                    ],
                ],

                'skills' => [
                    [
                        'technology_id' => 100, // Java
                        'years_of_experience' => 10,
                        'experiences' => [$this->PUBLIC_VENDOR_CANDIDATE_JANE_EXPERIENCE_PUBLIC_ID],
                        'new_experiences' => [],
                    ],
                    [
                        'technology_id' => 10, // Android
                        'years_of_experience' => 8,
                        'experiences' => [], // Changed
                        'new_experiences' => [],
                    ],
                    [
                        'technology_id' => 197, // Spring Boot - changed
                        'years_of_experience' => 4,
                        'experiences' => [$this->PUBLIC_VENDOR_CANDIDATE_JANE_EXPERIENCE_PUBLIC_ID],
                        'new_experiences' => [],
                    ],
                ],

                'cv_resource_id' => $this->CANDIDATE_CV_RESOURCE_PUBLIC_ID,
            ],
        );
        $updateResponse->assertSuccessful();

        $showResponse = $this->getJson("/company/candidates/$this->PUBLIC_VENDOR_CANDIDATE_JANE_PUBLIC_ID");
        $showResponse->assertSuccessful();
        $data = $showResponse->json('data');
        $this->assertIsArray($data);

        $this->assertEquals('Joan', array_get($data, 'name'));
        $this->assertEquals('pl', array_get($data, 'country'));
        $this->assertEquals(40, array_get($data, 'rate'));
        $this->assertEquals('lead', array_get($data, 'seniority'));
        $this->assertCount(3, array_get($data, 'skills'));
        $this->assertEmpty(array_get($data, 'skills.1.experiences'));
        $this->assertEquals(197, array_get($data, 'skills.2.technology_id'));
        $this->assertEquals($this->PUBLIC_VENDOR_CANDIDATE_JANE_EXPERIENCE_PUBLIC_ID, array_get($data, 'skills.2.experiences.0'));
        $this->assertEquals($this->PUBLIC_VENDOR_CANDIDATE_JANE_EXPERIENCE_PUBLIC_ID, array_get($data, 'experiences.0.id'));
        $this->assertEquals(10, array_get($data, 'experiences.0.length'));
    }

    public function test_should_not_be_able_to_edit_foreign_candidate(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->putJson(
            "/company/candidates/$this->PUBLIC_SECOND_VENDOR_CANDIDATE_GERMAIN_PUBLIC_ID",
            [
                'internal_name' => 'Joan of Ark',
                'name' => 'Joan', // Changed
                'country' => 'pl',
                'city' => 'Kraków',
                'rate' => 40,

                'profession' => 'Software Engineer',
                'seniority' => 'lead', // Changed
                'last_job_title' => 'Senior Android Developer',
                'years_of_experience' => 10,
                'highest_education' => 'master',
                'field_of_study' => 'Computer Science',

                'experiences' => [],

                'skills' => [
                    [
                        'technology_id' => 100, // Java
                        'years_of_experience' => 10,
                        'experiences' => [],
                        'new_experiences' => [],
                    ],
                    [
                        'technology_id' => 10, // Android
                        'years_of_experience' => 8,
                        'experiences' => [],
                        'new_experiences' => [],
                    ],
                    [
                        'technology_id' => 197, // Spring Boot - changed
                        'years_of_experience' => 4,
                        'experiences' => [],
                        'new_experiences' => [],
                    ],
                ],

                'cv_resource_id' => $this->CANDIDATE_CV_RESOURCE_PUBLIC_ID,
            ],
        );
        $response->assertNotFound();
    }

    public function test_should_not_be_able_to_edit_own_tender_candidate(): void
    {
        $candidateSlug = $this->prepareCandidateForTender();
        $this->actingAsGenericVendorClient();

        $response = $this->putJson(
            "/company/candidates/$candidateSlug",
            [
                'internal_name' => 'Joan of Ark',
                'name' => 'Joan', // Changed
                'country' => 'pl',
                'city' => 'Kraków',
                'rate' => 40,

                'profession' => 'Software Engineer',
                'seniority' => 'lead', // Changed
                'last_job_title' => 'Senior Android Developer',
                'years_of_experience' => 10,
                'highest_education' => 'master',
                'field_of_study' => 'Computer Science',

                'experiences' => [],

                'skills' => [
                    [
                        'technology_id' => 100, // Java
                        'years_of_experience' => 10,
                        'experiences' => [],
                        'new_experiences' => [],
                    ],
                    [
                        'technology_id' => 10, // Android
                        'years_of_experience' => 8,
                        'experiences' => [],
                        'new_experiences' => [],
                    ],
                    [
                        'technology_id' => 197, // Spring Boot - changed
                        'years_of_experience' => 4,
                        'experiences' => [],
                        'new_experiences' => [],
                    ],
                ],

                'cv_resource_id' => $this->CANDIDATE_CV_RESOURCE_PUBLIC_ID,
            ],
        );
        $response->assertNotFound();
    }

    public function test_delete_own_candidate(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->deleteJson("/company/candidates/$this->PUBLIC_VENDOR_CANDIDATE_JANE_PUBLIC_ID");
        $response->assertSuccessful();
    }

    public function test_should_not_be_able_to_delete_foreign_candidate(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->deleteJson("/company/candidates/$this->PUBLIC_SECOND_VENDOR_CANDIDATE_GERMAIN_PUBLIC_ID");
        $response->assertNotFound();
    }

    public function test_should_not_be_able_to_delete_own_tender_candidate(): void
    {
        $candidateSlug = $this->prepareCandidateForTender();
        $this->actingAsGenericVendorClient();

        $deleteResponse = $this->deleteJson("/company/candidates/$candidateSlug");
        $deleteResponse->assertNotFound();
    }

    public function test_parse_candidate_cv(): void
    {
        $this->actingAsGenericVendorClient();
        $this->mockAffindaSuccessfulResponse();
        $this->fakeDummyResources();

        $response = $this->postJson('/company/candidates/cv', [
            'cv_resource_id' => $this->CANDIDATE_CV_RESOURCE_PUBLIC_ID,
        ]);
        $response->assertCreated();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('full', $data['parse_status']);
        $this->assertNotNull($data['candidate_slug']);

        $candidateResponse = $this->getJson("/company/candidates/{$data['candidate_slug']}?with_unfinished=true");
        $candidateResponse->assertSuccessful();

        $candidateData = $candidateResponse->json('data');
        $this->assertFalse($candidateData['finished']);

        $this->assertEquals('Jozef Primator', $candidateData['internal_name']);
        $this->assertEquals('Jozef P.', $candidateData['name']);
        $this->assertEquals('sk', $candidateData['country']);
        $this->assertEquals('Trencin', $candidateData['city']);
        $this->assertNull($candidateData['rate']);

        $this->assertEquals('Project Manager - Manager', $candidateData['profession']);
        $this->assertNull($candidateData['seniority']);
        $this->assertEquals('Project Manager', $candidateData['last_job_title']);
        $this->assertEquals(16, $candidateData['years_of_experience']);
        $this->assertNull($candidateData['highest_education']);
        $this->assertNull($candidateData['field_of_study']);

        $this->assertNotNull($candidateData['cv']);

        $this->assertCount(6, $candidateData['experiences']);
        $this->assertEquals('Project Manager at Q-EX', array_get($candidateData, 'experiences.2.name'));
        $this->assertEquals('ongoing', array_get($candidateData, 'experiences.2.length_type'));
        $this->assertNull(array_get($candidateData, 'experiences.2.length'));
        $this->assertEquals('IT Project manager / Technical support at Q-EX', array_get($candidateData, 'experiences.4.name'));
        $this->assertEquals(
            "Technical support for systems like: \n-Customer Flow Management (CFM) \n-Digital Signage \n-Customer Satisfaction I planning new installations of systems, coordinate human and material resources, staff training and support for customers. Development new systems. ",
            array_get($candidateData, 'experiences.4.description'),
        );
        $this->assertEquals('years', array_get($candidateData, 'experiences.4.length_type'));
        $this->assertEquals(4, array_get($candidateData, 'experiences.4.length'));

        $this->assertCount(4, $candidateData['skills']);
        $this->assertEquals(101, array_get($candidateData, 'skills.0.technology_id'));
        $this->assertEquals(7, array_get($candidateData, 'skills.0.years_of_experience'));
        $this->assertContains(array_get($candidateData, 'experiences.2.id'), array_get($candidateData, 'skills.0.experiences'));
        $this->assertEquals(148, array_get($candidateData, 'skills.1.technology_id'));
        $this->assertNull(array_get($candidateData, 'skills.1.years_of_experience'));
        $this->assertEquals(132, array_get($candidateData, 'skills.2.technology_id'));
        $this->assertNull(array_get($candidateData, 'skills.2.years_of_experience'));
        $this->assertEquals(168, array_get($candidateData, 'skills.3.technology_id'));
        $this->assertNull(array_get($candidateData, 'skills.3.years_of_experience'));
    }

    public function test_parse_candidate_cv_service_unavailable(): void
    {
        $this->actingAsGenericVendorClient();
        $this->mockAffindaFailedResponse();
        $this->fakeDummyResources();

        $response = $this->postJson('/company/candidates/cv', [
            'cv_resource_id' => $this->CANDIDATE_CV_RESOURCE_PUBLIC_ID,
        ]);
        $response->assertCreated();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('failed', $data['parse_status']);
        $this->assertNotNull($data['candidate_slug']);

        $candidateResponse = $this->getJson("/company/candidates/{$data['candidate_slug']}?with_unfinished=true");
        $candidateResponse->assertSuccessful();

        $candidateData = $candidateResponse->json('data');
        $this->assertIsArray($candidateData);
        $this->assertFalse($candidateData['finished']);
        $this->assertNotNull($candidateData['cv']);
        $this->assertNotNull($candidateData['internal_name']);
        $this->assertNotNull($candidateData['name']);
    }

    private function mockAffindaSuccessfulResponse(): void
    {
        Http::fake([
            'https://api.affinda.com/*' => fn () => Http::response($this->loadJsonTestData('affinda-parsed-cv-response.json')),
        ]);
    }

    private function mockAffindaFailedResponse(): void
    {
        Http::fake([
            'https://api.affinda.com/*' => fn () => new RejectedPromise(new ConnectionException('Service unavailable')),
        ]);
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    public function test_clients_cannot_access_endpoints(): void
    {
        $this->actingAsGenericClient();
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    private function prepareCandidateForTender(): string
    {
        $this->actingAsMasterAdmin();

        $applyResponse = $this->putJson(
            "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/positions/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID/apply",
            ['candidate_id' => $this->PUBLIC_VENDOR_CANDIDATE_JANE_PUBLIC_ID],
        );
        $applyResponse->assertSuccessful();

        $candidatesResponse = $this->getJson("admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $candidatesResponse->assertSuccessful();
        $candidateSlug = $candidatesResponse->json('data.0.id');

        $approveResponse = $this->putJson(
            "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
            ['status' => 'approved'],
        );
        $approveResponse->assertSuccessful();

        return $candidateSlug;
    }
}
