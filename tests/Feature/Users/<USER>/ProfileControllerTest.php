<?php

namespace Tests\Feature\Users\Profile;

use Tests\Feature\FeatureTestCase;

class ProfileControllerTest extends FeatureTestCase
{
    private array $endpoints = [
        ['get', '/profile'],
        ['put', '/profile'],
    ];

    public function test_staff_can_view_their_profile(): void
    {
        $this->actingAsMasterAdmin();

        $response = $this->getJson('/profile');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertEquals('<EMAIL>', $data['email']);
        $this->assertArrayNotHasKey('company', $data);
    }

    public function test_client_can_view_their_profile(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson('/profile');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertEquals('<EMAIL>', $data['email']);
        $this->assertArrayHas<PERSON>ey('company', $data);
        $this->assertEquals('Vendor company', array_get($data, 'company.name'));
    }

    public function test_client_can_update_their_profile(): void
    {
        $this->actingAsGenericVendorClient(fromDatabase: true);

        $data = [
            'name' => 'Johnathan', // changed
            'surname' => 'Doe',
            'avatar_resource_id' => null,
            'phone' => '+*********** 321', // changed
            'position' => 'CEO', // changed
        ];

        $response = $this->putJson('/profile', $data);
        $response->assertSuccessful();

        $showResponse = $this->getJson('/profile');
        $showResponse->assertSuccessful();

        $stored = $showResponse->json('data');
        $this->assertEquals($data['name'], $stored['name']);
        $this->assertEquals($data['surname'], $stored['surname']);
        $this->assertArrayNotHasKey('avatar', $stored);
        $this->assertEquals($data['phone'], $stored['phone']);
        $this->assertEquals($data['position'], $stored['position']);
    }

    public function test_client_cannot_update_their_phone_if_malformed(): void
    {
        $this->actingAsGenericVendorClient(fromDatabase: true);

        $data = [
            'name' => 'John',
            'surname' => 'Doe',
            'avatar_resource_id' => null,
            'phone' => '+*********** 321 0', // 1 digit more for SVK format than possible
            'position' => 'CEO',
        ];

        $response = $this->putJson('/profile', $data);
        $response->assertUnprocessable();
        $response->assertJsonValidationErrorFor('phone', 'error.errors');
    }

    public function test_client_cannot_update_their_password_if_current_is_not_matching(): void
    {
        $this->actingAsGenericVendorClient(fromDatabase: true);

        $data = [
            'name' => 'John',
            'surname' => 'Doe',
            'avatar_resource_id' => null,
            'phone' => '+*********** 321',
            'position' => 'CEO',

            'password' => 'Password2!',
            'password_confirmation' => 'Password2!',
            'current_password' => 'bad password',
        ];

        $response = $this->putJson('/profile', $data);
        $response->assertUnprocessable();
        $response->assertJsonValidationErrorFor('current_password', 'error.errors');
    }

    public function test_client_can_update_their_password_if_current_is_correct(): void
    {
        $this->actingAsGenericVendorClient(fromDatabase: true);

        $data = [
            'name' => 'John',
            'surname' => 'Doe',
            'avatar_resource_id' => null,
            'phone' => '+*********** 321',
            'position' => 'CEO',

            'password' => 'Password2!',
            'password_confirmation' => 'Password2!',
            'current_password' => 'Password1!',
        ];

        $response = $this->putJson('/profile', $data);
        $response->assertSuccessful();

        $loginResponse = $this->postJson('/auth/login', [
            'email' => '<EMAIL>',
            'password' => 'Password2!',
        ]);
        $loginResponse->assertSuccessful();
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->endpoints);
    }
}
