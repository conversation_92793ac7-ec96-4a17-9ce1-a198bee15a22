<?php

namespace Tests\Feature;

class DataListsTest extends FeatureTestCase
{
    public function test_technologies_list_is_available(): void
    {
        $response = $this->getJson('/technologies');
        $response->assertSuccessful();

        // Ensure some data has been returned
        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);

        // Check one featured technology without parent
        $dotnet = array_first($data, fn ($item) => $item['id'] === 1);
        $this->assertIsArray($dotnet);
        $this->assertEquals('.NET', $dotnet['name']);
        $this->assertTrue($dotnet['featured']);

        // Check one regular technology with parent
        $jakarta = array_first($data, fn ($item) => $item['id'] === 98);
        $this->assertIsArray($jakarta);
        $this->assertEquals('Jakarta EE', $jakarta['name']);
        $this->assertFalse($jakarta['featured']);
        $this->assertEquals(100, $jakarta['parent_id']);
    }

    public function test_industries_list_is_available(): void
    {
        $response = $this->getJson('/industries');
        $response->assertSuccessful();

        // Ensure some data has been returned
        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);

        // Check one featured industry without parent
        $ai = array_first($data, fn ($item) => $item['id'] === 4);
        $this->assertIsArray($ai);
        $this->assertEquals('AI & Machine Learning', $ai['name']);
        $this->assertTrue($ai['featured']);

        // Check one regular industry with parent
        $fintech = array_first($data, fn ($item) => $item['id'] === 49);
        $this->assertIsArray($fintech);
        $this->assertEquals('Fintech', $fintech['name']);
        $this->assertFalse($fintech['featured']);
        $this->assertEquals(12, $fintech['parent_id']);
    }

    public function test_clients_list_is_available(): void
    {
        $response = $this->getJson('/clients');
        $response->assertSuccessful();

        // Ensure some data has been returned
        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);

        // Check one client with description
        $cisco = array_first($data, fn ($item) => $item['id'] === 11);
        $this->assertIsArray($cisco);
        $this->assertEquals('CISCO', $cisco['name']);
        $this->assertEquals(
            'American-based multinational digital communications technology conglomerate corporation headquartered in San Jose, California.',
            $cisco['description'],
        );

        // Check one client without description
        $google = array_first($data, fn ($item) => $item['id'] === 26);
        $this->assertIsArray($google);
        $this->assertEquals('Google', $google['name']);
        $this->assertNull($google['description']);
    }

    public function test_employee_positions_list_is_available(): void
    {
        $response = $this->getJson('/employee-positions');
        $response->assertSuccessful();

        // Ensure some data has been returned
        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);

        // Check one position with seniorities
        $be = array_first($data, fn ($item) => $item['id'] === 1);
        $this->assertIsArray($be);
        $this->assertEquals('Backend Developer', $be['name']);
        $this->assertEquals('Backend Developers', $be['name_plural']);
        $this->assertTrue($be['hireable']);
        $this->assertEquals(
            ['junior', 'medior', 'senior', 'lead'],
            $be['seniorities'],
        );

        // Check one position without seniorities
        $manager = array_first($data, fn ($item) => $item['id'] === 11);
        $this->assertIsArray($manager);
        $this->assertEquals('Product/Project Manager', $manager['name']);
        $this->assertEquals('Product/Project Managers', $manager['name_plural']);
        $this->assertFalse($manager['hireable']);
        $this->assertNull($manager['seniorities']);
    }
}
