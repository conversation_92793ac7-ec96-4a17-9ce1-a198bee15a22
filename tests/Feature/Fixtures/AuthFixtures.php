<?php

namespace Tests\Feature\Fixtures;

use App\Models\User;

trait AuthFixtures
{
    public function actingAsMasterAdmin(bool $fromDatabase = false): void
    {
        if ($fromDatabase) {
            $this->actingAsDatabaseUser(1);
        } else {
            $this->actingAsProvisionalUser([
                'id' => 1,
                'name' => 'Master',
                'surname' => 'Admin',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => 'Password1!',
            ]);
        }
    }

    public function actingAsGenericVendorClient(bool $fromDatabase = false): void
    {
        if ($fromDatabase) {
            $this->actingAsDatabaseUser(2);
        } else {
            $this->actingAsProvisionalUser([
                'id' => 2,
                'company_id' => 1,
                'name' => 'John',
                'surname' => 'Doe',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => 'Password1!',
            ]);
        }
    }

    public function actingAsGenericClient(bool $fromDatabase = false): void
    {
        if ($fromDatabase) {
            $this->actingAsDatabaseUser(4);
        } else {
            $this->actingAsProvisionalUser([
                'id' => 4,
                'company_id' => 2,
                'name' => 'Ignác',
                'surname' => 'Bajza',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => 'Password1!',
            ]);
        }
    }

    public function actingAsGenericSecondVendorClient(bool $fromDatabase = false): void
    {
        if ($fromDatabase) {
            $this->actingAsDatabaseUser(5);
        } else {
            $this->actingAsProvisionalUser([
                'id' => 5,
                'company_id' => 4,
                'name' => 'Ivan',
                'surname' => 'Krasko',
                'email' => '<EMAIL>',
                'email_verified_at' => now(),
                'password' => 'Password1!',
            ]);
        }
    }

    public function actingAsProvisionalUser(array $data): void
    {
        $user = (new User)->forceFill($data);
        $this->actingAs($user);
    }

    public function actingAsDatabaseUser(int $id): void
    {
        $user = User::find($id);
        $this->actingAs($user);
    }
}
