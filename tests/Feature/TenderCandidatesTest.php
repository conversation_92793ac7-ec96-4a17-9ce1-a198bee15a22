<?php

namespace Tests\Feature;

class TenderCandidatesTest extends FeatureTestCase
{
    private array $endpoints = [
        ['get', '/tenders/1/candidates'],
        ['put', '/tenders/1/candidates/1/status'],
        ['put', '/tenders/1/candidates/1/note'],
    ];

    public function test_index_for_client(): void
    {
        $candidateSlug = $this->prepareCandidateForTender();
        $this->actingAsGenericClient();

        $response = $this->getJson("tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);

        $candidate = array_first($data, fn (array $candidateData) => $candidateData['id'] === $candidateSlug);
        $this->assertNotNull($candidate);

        $this->assertArrayNotHasKey('internal_name', $candidate);
        $this->assertEquals('pl', $candidate['country']);

        $this->assertCount(2, $candidate['skills']);
        $androidTechnology = array_first($candidate['skills'], fn (array $skill) => $skill['technology_id'] === 10);
        $this->assertNotNull($androidTechnology);
        $this->assertEquals(8, $androidTechnology['years_of_experience']);
        $this->assertContains(array_get($candidate, 'experiences.0.id'), $androidTechnology['experiences']);

        $this->assertCount(1, $candidate['experiences']);
        $androidExperience = array_get($candidate, 'experiences.0');
        $this->assertNotNull($androidExperience);
        $this->assertEquals('Senior Android Developer at Large Automotive Company', $androidExperience['name']);
        $this->assertEquals('years', $androidExperience['length_type']);
        $this->assertEquals(5, $androidExperience['length']);
    }

    public function test_index_for_vendor(): void
    {
        $candidateSlug = $this->prepareCandidateForTender();
        $this->actingAsGenericVendorClient();

        $response = $this->getJson("tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);

        $candidate = array_first($data, fn (array $candidateData) => $candidateData['id'] === $candidateSlug);
        $this->assertNotNull($candidate);

        $this->assertEquals('Joan of Ark', $candidate['internal_name']);
        $this->assertEquals('pl', $candidate['country']);

        $this->assertCount(2, $candidate['skills']);
        $androidTechnology = array_first($candidate['skills'], fn (array $skill) => $skill['technology_id'] === 10);
        $this->assertNotNull($androidTechnology);
        $this->assertEquals(8, $androidTechnology['years_of_experience']);
        $this->assertContains(array_get($candidate, 'experiences.0.id'), $androidTechnology['experiences']);

        $this->assertCount(1, $candidate['experiences']);
        $androidExperience = array_get($candidate, 'experiences.0');
        $this->assertNotNull($androidExperience);
        $this->assertEquals('Senior Android Developer at Large Automotive Company', $androidExperience['name']);
        $this->assertEquals('years', $androidExperience['length_type']);
        $this->assertEquals(5, $androidExperience['length']);
    }

    public function test_index_for_client_should_not_show_candidates_awaiting_approval(): void
    {
        $this->prepareCandidateForTender(status: 'awaiting_approval');
        $this->actingAsGenericClient();

        $response = $this->getJson("tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertEmpty($data);
    }

    public function test_index_for_vendor_should_show_all_candidates(): void
    {
        $candidateSlug = $this->prepareCandidateForTender(status: 'awaiting_approval');
        $this->actingAsGenericVendorClient();

        $response = $this->getJson("tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);
        $this->assertEquals($candidateSlug, array_get($data, '0.id'));
    }

    public function test_index_for_second_vendor_should_show_not_show_foreign_candidates(): void
    {
        $this->prepareCandidateForTender();
        $this->actingAsGenericSecondVendorClient();

        $response = $this->getJson("tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertEmpty($data);
    }

    public function test_vendor_should_not_be_able_to_update_status_of_own_candidate(): void
    {
        $candidateSlug = $this->prepareCandidateForTender();
        $this->actingAsGenericVendorClient();

        $response = $this->putJson(
            "tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
            ['status' => 'interested'],
        );
        $response->assertNotFound();
    }

    public function test_client_update_status_of_candidate(): void
    {
        $candidateSlug = $this->prepareCandidateForTender();
        $this->actingAsGenericClient();

        $updateResponse = $this->putJson(
            "tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
            ['status' => 'interested'],
        );
        $updateResponse->assertSuccessful();

        $candidatesResponse = $this->getJson("tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $candidatesResponse->assertSuccessful();

        $data = $candidatesResponse->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);
        $this->assertEquals('interested', array_get($data, '0.status'));
    }

    public function test_client_should_not_be_able_to_update_status_of_candidate_to_invalid_status(): void
    {
        $candidateSlug = $this->prepareCandidateForTender();
        $this->actingAsGenericClient();

        $response = $this->putJson(
            "tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
            ['status' => 'hired'],
        );
        $response->assertConflict();
        $this->assertEquals('invalid_tender_candidate_next_status', $response->json('error.type'));
    }

    public function test_client_update_status_to_not_interested_should_allow_empty_rejection_reason(): void
    {
        $candidateSlug = $this->prepareCandidateForTender();
        $this->actingAsGenericClient();

        $updateResponse = $this->putJson(
            "tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
            [
                'status' => 'not_interested',
                'rejection_reason' => null,
            ],
        );
        $updateResponse->assertSuccessful();

        $candidatesResponse = $this->getJson("tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $candidatesResponse->assertSuccessful();

        $data = $candidatesResponse->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);
        $this->assertEquals('not_interested', array_get($data, '0.status'));
    }

    public function test_client_update_status_to_not_hired_should_require_rejection_reason(): void
    {
        $candidateSlug = $this->prepareCandidateForTender(status: 'interviewed_interested');
        $this->actingAsGenericClient();

        $response = $this->putJson(
            "tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
            [
                'status' => 'not_hired',
                'rejection_reason' => null,
            ],
        );
        $response->assertUnprocessable();
        $response->assertJsonValidationErrorFor('rejection_reason', 'error.errors');
    }

    public function test_client_update_status_to_interested_should_prohibit_rejection_reason(): void
    {
        $candidateSlug = $this->prepareCandidateForTender();
        $this->actingAsGenericClient();

        $response = $this->putJson(
            "tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
            [
                'status' => 'interested',
                'rejection_reason' => 'Should not be possible to set me',
            ],
        );
        $response->assertUnprocessable();
        $response->assertJsonValidationErrorFor('rejection_reason', 'error.errors');
    }

    public function test_client_update_status_should_persist_rejection_reason(): void
    {
        $candidateSlug = $this->prepareCandidateForTender();
        $this->actingAsGenericClient();

        $updateResponse = $this->putJson(
            "tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
            [
                'status' => 'not_interested',
                'rejection_reason' => 'I do not like you',
            ],
        );
        $updateResponse->assertSuccessful();

        $candidatesResponse = $this->getJson("tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $candidatesResponse->assertSuccessful();

        $data = $candidatesResponse->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);
        $this->assertEquals('not_interested', array_get($data, '0.status'));
        $this->assertEquals('I do not like you', array_get($data, '0.rejection_reason'));
    }

    public function test_client_update_note_of_candidate(): void
    {
        $candidateSlug = $this->prepareCandidateForTender();
        $this->actingAsGenericClient();

        $updateResponse = $this->putJson(
            "tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/note",
            ['note' => 'This candidate is awesome!'],
        );
        $updateResponse->assertSuccessful();

        $candidatesResponse = $this->getJson("tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $candidatesResponse->assertSuccessful();

        $data = $candidatesResponse->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);
        $this->assertEquals('This candidate is awesome!', array_get($data, '0.note'));
    }

    public function test_client_update_note_of_candidate_to_null(): void
    {
        $candidateSlug = $this->prepareCandidateForTender();
        $this->actingAsGenericClient();

        $firstUpdateResponse = $this->putJson(
            "tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/note",
            ['note' => 'Meh'],
        );
        $firstUpdateResponse->assertSuccessful();

        $firstCandidatesResponse = $this->getJson("tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $firstCandidatesResponse->assertSuccessful();

        $firstData = $firstCandidatesResponse->json('data');
        $this->assertIsArray($firstData);
        $this->assertNotEmpty($firstData);
        $this->assertEquals('Meh', array_get($firstData, '0.note'));

        $secondUpdateResponse = $this->putJson(
            "tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/note",
            ['note' => null],
        );
        $secondUpdateResponse->assertSuccessful();

        $secondCandidatesResponse = $this->getJson("tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $secondCandidatesResponse->assertSuccessful();

        $secondData = $secondCandidatesResponse->json('data');
        $this->assertIsArray($secondData);
        $this->assertNotEmpty($secondData);
        $this->assertNull(array_get($secondData, '0.note'));
    }

    public function test_vendor_should_not_be_able_to_see_client_note(): void
    {
        $candidateSlug = $this->prepareCandidateForTender();
        $this->actingAsGenericClient();

        $updateResponse = $this->putJson(
            "tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/note",
            ['note' => 'This candidate is awesome!'],
        );
        $updateResponse->assertSuccessful();

        $this->actingAsGenericVendorClient();

        $candidatesResponse = $this->getJson("tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $candidatesResponse->assertSuccessful();

        $data = $candidatesResponse->json('data');
        $this->assertIsArray($data);
        $this->assertNotEmpty($data);
        $this->assertArrayNotHasKey('note', $data[0]);
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->endpoints);
    }

    private function prepareCandidateForTender(string $status = 'approved'): string
    {
        $this->actingAsMasterAdmin();

        $applyResponse = $this->putJson(
            "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/positions/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID/apply",
            ['candidate_id' => $this->PUBLIC_VENDOR_CANDIDATE_JANE_PUBLIC_ID],
        );
        $applyResponse->assertSuccessful();

        $candidatesResponse = $this->getJson("admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $candidatesResponse->assertSuccessful();
        $candidateSlug = $candidatesResponse->json('data.0.id');

        if ($status !== 'awaiting_approval') {
            $approveResponse = $this->putJson(
                "admin/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateSlug/status",
                ['status' => $status],
            );
            $approveResponse->assertSuccessful();
        }

        return $candidateSlug;
    }
}
