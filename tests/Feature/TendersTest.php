<?php

namespace Tests\Feature;

use App\Enums\TenderCandidateStatus;
use App\Enums\TenderStatus;
use App\Models\Candidate;
use App\Models\Tender;
use App\Models\TenderPosition;

class TendersTest extends FeatureTestCase
{
    private array $genericEndpoints = [
        ['get',    '/tenders'],
        ['get',    '/tenders/1'],
        ['get',    '/tenders/1/candidates'],
        ['put',    '/tenders/1/candidates/1/status'],
        ['get',    '/tenders/1/positions/1'],
    ];

    private array $vendorEndpoints = [
        ['put',    'tenders/1/positions/1/apply'],
    ];

    public function test_index_without_filter_should_return_nothing(): void
    {
        $this->actingAsGenericClient();

        $response = $this->getJson('/tenders');
        $response->assertSuccessful();
        $this->assertEmpty($response->json('data'));
    }

    public function test_index_should_return_only_own_published_tenders(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson('/tenders?filters[category]=my');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $data = collect($data);

        // Any tender from other companies should not be in this list
        $hiddenTenderIds = [
            $this->HIDDEN_VENDOR_PUBLISHED_ENDED_TENDER_PUBLIC_ID,
            $this->CLIENT_PUBLISHED_ENDED_TENDER_PUBLIC_ID,
            $this->CLIENT_PUBLISHED_OPEN_NO_VENDORS_TENDER_PUBLIC_ID,
            $this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID,
        ];
        $this->assertFalse($data->some(fn (array $tender) => in_array($tender['id'], $hiddenTenderIds)));

        // All of own tenders should be there
        $ownTenderIds = [
            $this->PUBLIC_VENDOR_PUBLISHED_OPEN_TENDER_PUBLIC_ID,
            $this->PUBLIC_VENDOR_DRAFT_TENDER_PUBLIC_ID,
        ];
        $this->assertTrue(collect($ownTenderIds)->every(
            fn (string $id) => $data->some(fn (array $tender) => $tender['id'] === $id)),
        );
    }

    public function test_index_should_return_only_visible_open_published_tenders(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson('/tenders?filters[category]=open');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $data = collect($data);

        // Only open and visible tender to the company should be in this list
        $openTenderIds = [
            $this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID,
        ];
        $this->assertTrue(collect($openTenderIds)->every(
            fn (string $id) => $data->some(fn (array $tender) => $tender['id'] === $id)),
        );

        // Own and any other tender should not be there
        $ownAndOtherTenderIds = [
            $this->PUBLIC_VENDOR_PUBLISHED_OPEN_TENDER_PUBLIC_ID,
            $this->PUBLIC_VENDOR_DRAFT_TENDER_PUBLIC_ID,
            $this->HIDDEN_VENDOR_PUBLISHED_ENDED_TENDER_PUBLIC_ID,
            $this->CLIENT_PUBLISHED_ENDED_TENDER_PUBLIC_ID,
            $this->CLIENT_PUBLISHED_OPEN_NO_VENDORS_TENDER_PUBLIC_ID,
        ];
        $this->assertFalse($data->some(fn (array $tender) => in_array($tender['id'], $ownAndOtherTenderIds)));
    }

    public function test_index_should_return_all_completed_open_published_tenders(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson('/tenders?filters[category]=completed');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $data = collect($data);

        // Any completed tender should be visible
        $openTenderIds = [
            $this->HIDDEN_VENDOR_PUBLISHED_ENDED_TENDER_PUBLIC_ID,
            $this->CLIENT_PUBLISHED_ENDED_TENDER_PUBLIC_ID,
        ];
        $this->assertTrue(collect($openTenderIds)->every(
            fn (string $id) => $data->some(fn (array $tender) => $tender['id'] === $id)),
        );

        // Other tenders should not be there
        $ownAndOtherTenderIds = [
            $this->PUBLIC_VENDOR_PUBLISHED_OPEN_TENDER_PUBLIC_ID,
            $this->PUBLIC_VENDOR_DRAFT_TENDER_PUBLIC_ID,
            $this->CLIENT_PUBLISHED_OPEN_NO_VENDORS_TENDER_PUBLIC_ID,
            $this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID,
        ];
        $this->assertFalse($data->some(fn (array $tender) => in_array($tender['id'], $ownAndOtherTenderIds)));
    }

    public function test_index_should_not_show_tender_of_deleted_company(): void
    {
        Tender::withUnavailable()
            ->where('public_id', $this->HIDDEN_VENDOR_PUBLISHED_ENDED_TENDER_PUBLIC_ID)
            ->firstOrFail()
            ->company
            ->delete();

        $this->actingAsGenericClient();

        $response = $this->getJson('/tenders?filters[category]=completed');
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $data = collect($data);
        $this->assertEmpty($data->where('public_id', $this->HIDDEN_VENDOR_PUBLISHED_ENDED_TENDER_PUBLIC_ID));
    }

    public function test_show_own_tender(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson("/tenders/$this->PUBLIC_VENDOR_PUBLISHED_OPEN_TENDER_PUBLIC_ID");
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('open', $data['status']);
        $this->assertEquals('Breaking Hell', $data['name']);
        $this->assertArrayHasKey('company', $data);
        $this->assertEquals('US Cooking Showdown', array_get($data, 'project.name'));
        $this->assertEquals('Gordon Ramsay', array_get($data, 'project.client.name'));
        $this->assertArrayNotHasKey('positions', $data);
        $this->assertArrayNotHasKey('has_applied_candidates', $data);
    }

    public function test_show_available_tender(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson("/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID");
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('open', $data['status']);
        $this->assertEquals('We need C# devs', $data['name']);
        $this->assertArrayNotHasKey('company', $data);
        $this->assertTrue(array_get($data, 'project.anonymous'));
        $this->assertArrayNotHasKey('name', $data['project']);
        $this->assertArrayNotHasKey('description', $data['project']);
        $this->assertArrayNotHasKey('client', $data['project']);
        $this->assertArrayHasKey('positions', $data);
        $this->assertEquals('Full Stack .NET Developer', array_get($data, 'positions.0.name'));
        $this->assertArrayHasKey('has_applied_candidates', $data);
        $this->assertFalse($data['has_applied_candidates']);
    }

    public function test_show_dangerous_html_is_sanitized(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson("/tenders/$this->PUBLIC_VENDOR_DRAFT_TENDER_PUBLIC_ID");
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);

        $this->assertEquals('<p>This is another paragraph of a text</p>', $data['about']);
        $this->assertEquals('<h1>Gordon Ramsays US Cooking Showdown</h1><p>Yes, really</p>', array_get($data, 'project.about'));
        $this->assertEquals('<p>Will do anything haha</p>', array_get($data, 'positions.0.description'));
        $this->assertEquals('<p>Know everything and be cheap</p>', array_get($data, 'positions.0.requirements'));
        $this->assertEquals('<p>AI, GenAI, Quantum Computing, Big Data, IoT, Blockchain, Crypto, Virtual Realtiy</p>', array_get($data, 'positions.0.must_have_requirements'));
    }

    public function test_show_draft_tender_should_be_hidden(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson("/tenders/$this->HIDDEN_VENDOR_DRAFT_SOLUTION_PUBLIC_ID");
        $response->assertNotFound();
    }

    public function test_show_unavailable_tender_should_be_hidden(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson("/tenders/$this->CLIENT_PUBLISHED_OPEN_NO_VENDORS_TENDER_PUBLIC_ID");
        $response->assertNotFound();
    }

    public function test_show_tender_of_deleted_company_should_be_hidden(): void
    {
        Tender::withUnavailable()
            ->where('public_id', $this->HIDDEN_VENDOR_PUBLISHED_ENDED_TENDER_PUBLIC_ID)
            ->firstOrFail()
            ->company
            ->delete();

        $this->actingAsGenericVendorClient();

        $response = $this->getJson("/tenders/$this->HIDDEN_VENDOR_PUBLISHED_ENDED_TENDER_PUBLIC_ID");
        $response->assertNotFound();
    }

    public function test_apply_freshly_parsed_candidate_to_tender_position(): void
    {
        $this->actingAsGenericVendorClient();

        $data = [
            'cv_resource_id' => $this->CANDIDATE_CV_RESOURCE_PUBLIC_ID,

            'internal_name' => 'Joe Mama',
            'name' => 'Joe Who',
            'profession' => 'Software Engineer',
            'seniority' => 'medior',
            'rate' => 25,
            'last_job_title' => 'Java Developer',
            'years_of_experience' => 3,
            'highest_education' => 'bachelor',
            'field_of_study' => 'Computer Science',
            'country' => 'gb',
            'city' => null,

            'experiences' => [
                [
                    'pseudo_id' => 3_345_089,
                    'name' => 'Software Engineer at Fintech Company',
                    'description' => 'My first job which I can be proud of',
                    'length_type' => 'ongoing',
                    'length' => null,
                ],
            ],

            'skills' => [
                [
                    'technology_id' => 100, // Java
                    'years_of_experience' => 4,
                    'new_experiences' => [3_345_089],
                ],
            ],
        ];

        $response = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/positions/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID/apply",
            $data,
        );
        $response->assertSuccessful();

        $tenderCandidatesResponse = $this->getJson("/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $tenderCandidatesResponse->assertSuccessful();
        $tenderCandidateData = $tenderCandidatesResponse->json('data.0');
        $this->assertNotNull($tenderCandidateData);

        $this->assertEquals('Joe Who', $tenderCandidateData['name']);
        $this->assertEquals('medior', $tenderCandidateData['seniority']);
        $this->assertNull($tenderCandidateData['city']);
        $this->assertCount(1, $tenderCandidateData['skills']);
        $this->assertEquals(100, array_get($tenderCandidateData, 'skills.0.technology_id'));
        $this->assertCount(1, array_get($tenderCandidateData, 'skills.0.experiences'));
        $this->assertEquals(array_get($tenderCandidateData, 'experiences.0.id'), array_get($tenderCandidateData, 'skills.0.experiences.0'));
        $this->assertCount(1, $tenderCandidateData['experiences']);
        $this->assertEquals('Software Engineer at Fintech Company', array_get($tenderCandidateData, 'experiences.0.name'));
    }

    public function test_apply_existing_candidate_to_tender_position(): void
    {
        $this->actingAsGenericVendorClient();

        $candidateData = $this->existingCandidateData();

        $response = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/positions/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID/apply",
            $candidateData,
        );
        $response->assertSuccessful();

        $tenderCandidatesResponse = $this->getJson("/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates");
        $tenderCandidatesResponse->assertSuccessful();
        $tenderCandidateData = $tenderCandidatesResponse->json('data.0');
        $this->assertNotNull($tenderCandidateData);

        $this->assertNotEquals($candidateData['id'], $tenderCandidateData['id']);
        $this->assertEquals($candidateData['name'], $tenderCandidateData['name']);
        $this->assertEquals($candidateData['seniority'], $tenderCandidateData['seniority']);
        $this->assertEquals($candidateData['city'], $tenderCandidateData['city']);
        $this->assertSameSize($candidateData['skills'], $tenderCandidateData['skills']);
        $this->assertEquals(array_get($candidateData, 'skills.0.technology_id'), array_get($tenderCandidateData, 'skills.0.technology_id'));
        $this->assertSameSize(array_get($candidateData, 'skills.0.experiences'), array_get($tenderCandidateData, 'skills.0.experiences'));
        $this->assertSameSize($candidateData['experiences'], $tenderCandidateData['experiences']);
        $this->assertEquals(array_get($candidateData, 'experiences.0.name'), array_get($tenderCandidateData, 'experiences.0.name'));
    }

    public function test_should_not_be_able_to_apply_same_candidate_twice(): void
    {
        $this->actingAsGenericVendorClient();

        $candidateData = $this->existingCandidateData();

        $firstResponse = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/positions/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID/apply",
            $candidateData,
        );
        $firstResponse->assertSuccessful();

        $secondResponse = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/positions/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID/apply",
            $candidateData,
        );
        $secondResponse->assertConflict();
        $this->assertEquals('candidate_already_applied_to_tender_position', $secondResponse->json('error.type'));
    }

    public function test_should_not_be_able_to_apply_to_reviewing_tender(): void
    {
        Tender::withUnavailable()
            ->where('public_id', $this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID)
            ->firstOrFail()
            ->forceFill(['status' => TenderStatus::Ended])
            ->save();

        $this->actingAsGenericVendorClient();

        $candidateData = $this->existingCandidateData();

        $response = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/positions/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID/apply",
            $candidateData,
        );
        $response->assertConflict();
        $this->assertEquals('tender_not_open', $response->json('error.type'));
    }

    public function test_should_not_be_able_to_apply_to_own_tender(): void
    {
        Tender::withUnavailable()
            ->where('public_id', $this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID)
            ->firstOrFail()
            ->forceFill(['company_id' => 1])
            ->save();

        $this->actingAsGenericVendorClient();

        $candidateData = $this->existingCandidateData();

        $response = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/positions/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID/apply",
            $candidateData,
        );
        $response->assertConflict();
        $this->assertEquals('cannot_apply_to_own_tender', $response->json('error.type'));
    }

    public function test_should_not_be_able_to_apply_to_hidden_tender(): void
    {
        Tender::withUnavailable()
            ->where('public_id', $this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID)
            ->firstOrFail()
            ->vendors()
            ->detach();

        $this->actingAsGenericVendorClient();

        $candidateData = $this->existingCandidateData();

        $response = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/positions/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID/apply",
            $candidateData,
        );
        $response->assertNotFound();
    }

    public function test_change_status_of_applied_candidate(): void
    {
        $this->actingAsGenericVendorClient();

        $candidateData = $this->existingCandidateData();

        $applyResponse = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/positions/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID/apply",
            $candidateData,
        );
        $applyResponse->assertSuccessful();

        /** @var Candidate $candidateEntity */
        $candidateEntity = TenderPosition::query()
            ->where('public_id', $this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID)
            ->firstOrFail()
            ->candidates()
            ->where('origin_id', $this->PUBLIC_VENDOR_CANDIDATE_JANE_PRIVATE_ID)
            ->firstOrFail();
        $candidateEntity->pivot->status = TenderCandidateStatus::Approved;
        $candidateEntity->pivot->save();

        $this->actingAsGenericClient();

        $tryInvitedToInterviewResponse = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateEntity->public_id/status",
            ['status' => 'invited_to_interview'],
        );
        $tryInvitedToInterviewResponse->assertConflict();
        $this->assertEquals('invalid_tender_candidate_next_status', $tryInvitedToInterviewResponse->json('error.type'));

        $interestedResponse = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateEntity->public_id/status",
            ['status' => 'interested'],
        );
        $interestedResponse->assertSuccessful();

        $invitedToInterviewResponse = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateEntity->public_id/status",
            ['status' => 'invited_to_interview'],
        );
        $invitedToInterviewResponse->assertSuccessful();

        $tryHiredResponse = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateEntity->public_id/status",
            ['status' => 'hired'],
        );
        $tryHiredResponse->assertConflict();
        $this->assertEquals('invalid_tender_candidate_next_status', $tryHiredResponse->json('error.type'));

        $interviewedInterestedResponse = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateEntity->public_id/status",
            ['status' => 'interviewed_interested'],
        );
        $interviewedInterestedResponse->assertSuccessful();

        $hiredResponse = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateEntity->public_id/status",
            ['status' => 'hired'],
        );
        $hiredResponse->assertSuccessful();
    }

    public function test_cannot_change_status_of_own_candidate(): void
    {
        $this->actingAsGenericVendorClient();

        $candidateData = $this->existingCandidateData();

        $applyResponse = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/positions/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID/apply",
            $candidateData,
        );
        $applyResponse->assertSuccessful();

        /** @var Candidate $candidateEntity */
        $candidateEntity = TenderPosition::query()
            ->where('public_id', $this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_POSITION_PUBLIC_ID)
            ->firstOrFail()
            ->candidates()
            ->where('origin_id', $this->PUBLIC_VENDOR_CANDIDATE_JANE_PRIVATE_ID)
            ->firstOrFail();
        $candidateEntity->pivot->status = TenderCandidateStatus::Approved;
        $candidateEntity->pivot->save();

        $response = $this->putJson(
            "/tenders/$this->CLIENT_PUBLISHED_OPEN_WITH_VENDORS_TENDER_PUBLIC_ID/candidates/$candidateEntity->public_id/status",
            ['status' => 'interested'],
        );
        $response->assertNotFound();
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->genericEndpoints);
        $this->assertEndpointsInaccessible($this->vendorEndpoints);
    }

    public function test_clients_cannot_access_vendor_endpoints(): void
    {
        $this->actingAsGenericClient();
        $this->assertEndpointsInaccessible($this->vendorEndpoints);
    }

    private function existingCandidateData(): array
    {
        $candidateResponse = $this->getJson("/company/candidates/$this->PUBLIC_VENDOR_CANDIDATE_JANE_PUBLIC_ID");
        $candidateResponse->assertSuccessful();

        $candidateData = $candidateResponse->json('data');
        $candidateData['candidate_id'] = $candidateData['id'];
        foreach ($candidateData['skills'] as &$skill) {
            $skill['new_experiences'] = [];
        }

        return $candidateData;
    }
}
