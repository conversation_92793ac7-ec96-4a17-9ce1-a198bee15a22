<?php

namespace Tests\Feature;

use App\Models\Solution;
use Illuminate\Support\Facades\Mail;

class SolutionsControllerTest extends FeatureTestCase
{
    private array $genericEndpoints = [
        ['get',    '/solutions'],
        ['get',    '/solutions/1'],
    ];

    private array $vendorEndpoints = [
        ['post',   '/solutions'],
        ['put',    '/solutions/1'],
        ['delete', '/solutions/1'],
        ['delete', '/solutions/1/unapproved-change'],
    ];

    public function test_index_without_filter_should_show_only_published_or_own_solutions(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson('/solutions?per_page=100');
        $response->assertSuccessful();

        // Ensure all solutions are fetched in this request,
        // as otherwise the test would not make sense
        $this->assertLessThan(100, $response->json('meta.total'));
        $this->assertEquals($response->json('meta.current'), $response->json('meta.last'));

        $data = $response->json('data');
        $this->assertIsArray($data);
        $data = collect($data);

        // Drafted and unapproved solutions of other vendors should not be visible
        $privateSolutionIds = [
            $this->HIDDEN_VENDOR_DRAFT_SOLUTION_PUBLIC_ID,
        ];
        $this->assertFalse($data->some(fn (array $solution) => in_array($solution['id'], $privateSolutionIds)));

        // Published solutions of other vendors and all own solutions should be visible
        $publicSolutionIds = [
            $this->HIDDEN_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID,
            $this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID,
            $this->PUBLIC_VENDOR_DRAFT_SOLUTION_PUBLIC_ID,
            $this->PUBLIC_VENDOR_AWAITING_APPROVAL_SOLUTION_PUBLIC_ID,
        ];
        $this->assertTrue(collect($publicSolutionIds)->every(
            fn (string $id) => $data->some(fn (array $solution) => $solution['id'] === $id)),
        );
    }

    public function test_index_hidden_vendors_should_not_be_displayed(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson('/solutions?per_page=100');
        $response->assertSuccessful();

        // Ensure all solutions are fetched in this request,
        // as otherwise the test would not make sense
        $this->assertLessThan(100, $response->json('meta.total'));
        $this->assertEquals($response->json('meta.current'), $response->json('meta.last'));

        $data = $response->json('data');
        $this->assertIsArray($data);
        $data = collect($data);

        // Public vendor should be displayed
        $this->assertArrayHasKey('vendor', $data->first(fn (array $solution) => $solution['id'] === $this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID));

        // Hidden vendor should not be displayed
        $this->assertArrayNotHasKey('vendor', $data->first(fn (array $solution) => $solution['id'] === $this->HIDDEN_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID));
    }

    public function test_index_hidden_clients_should_not_be_displayed(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson('/solutions?per_page=100');
        $response->assertSuccessful();

        // Ensure all solutions are fetched in this request,
        // as otherwise the test would not make sense
        $this->assertLessThan(100, $response->json('meta.total'));
        $this->assertEquals($response->json('meta.current'), $response->json('meta.last'));

        $data = $response->json('data');
        $this->assertIsArray($data);
        $data = collect($data);

        // Public client should be displayed
        $this->assertNotNull($data->first(fn (array $solution) => $solution['id'] === $this->PUBLIC_VENDOR_AWAITING_APPROVAL_SOLUTION_PUBLIC_ID)['client']);

        // Anonymous client should not be displayed
        $this->assertArrayNotHasKey('client', $data->first(fn (array $solution) => $solution['id'] === $this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID));
    }

    public function test_index_solutions_of_soft_deleted_companies_should_not_be_displayed(): void
    {
        Solution::with('vendor.company')
            ->where('public_id', $this->HIDDEN_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID)
            ->firstOrFail()
            ->vendor
            ->company
            ->delete();

        $this->actingAsGenericVendorClient();

        $response = $this->getJson('/solutions?per_page=100');
        $response->assertSuccessful();

        // Ensure all solutions are fetched in this request,
        // as otherwise the test would not make sense
        $this->assertLessThan(100, $response->json('meta.total'));
        $this->assertEquals($response->json('meta.current'), $response->json('meta.last'));

        $data = $response->json('data');
        $this->assertIsArray($data);
        $data = collect($data);

        $this->assertFalse($data->some(fn (array $solution) => $solution['id'] === $this->HIDDEN_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID));
    }

    public function test_store_new_draft(): void
    {
        $this->actingAsGenericVendorClient();

        $data = [
            'as_draft' => true,

            'name' => 'AnoSol',
            'description' => 'Another great solution guys',
            'about' => '<h1>THIS IS GOING TO BE GREAT</h1><p>Seriously I mean it</p>',
            'cover_resource_id' => '4Hq5t2',
            'country' => 'es',
            'main_industry_id' => 81,
            'length_type' => 'ongoing',
            'length' => null,
            'ftes' => 30,
            'value' => 10_000_000,

            'industries' => [24, 58],
            'technologies' => [100, 218],

            'in_house' => false,

            'client' => [
                'anonymous' => false,
                'id' => null,
                'name' => 'Labaš, s. r. o.',
                'review' => '<p>So far so good</p>',
                'reviewer' => 'Luky',
                'reviewer_position' => 'CEO',
            ],

            'media' => [
                [
                    'type' => 'image',
                    'source' => $this->SOLUTION_MEDIA_IMAGE_RESOURCE_PUBLIC_ID,
                ],
            ],
        ];

        $response = $this->postJson('/solutions', $data);
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('draft', $data['publish_status']);
        $this->assertEquals('AnoSol', $data['name']);
        $this->assertEquals('<h1>THIS IS GOING TO BE GREAT</h1><p>Seriously I mean it</p>', $data['about']);
        $this->assertArrayHasKey('client', $data);
        $this->assertFalse($data['in_house']);
        $this->assertFalse(array_get($data, 'client.anonymous'));
        $this->assertEquals('So far so good', array_get($data, 'client.review'));
        $this->assertArrayNotHasKey('unapproved_change', $data);

        // Solution should not be visible to other companies
        $this->actingAsGenericClient();
        $showResponse = $this->getJson("/solutions/{$response->json('data.slug')}");
        $showResponse->assertNotFound();
    }

    public function test_store_new_submit_for_review(): void
    {
        $this->actingAsGenericVendorClient();

        $data = [
            'as_draft' => false,

            'name' => 'AnoSol',
            'description' => 'Another great solution guys',
            'about' => '<h1>THIS IS GOING TO BE GREAT</h1><p>Seriously I mean it</p>',
            'cover_resource_id' => '4Hq5t2',
            'country' => 'es',
            'main_industry_id' => 81,
            'length_type' => 'ongoing',
            'length' => null,
            'ftes' => 30,
            'value' => 10_000_000,

            'industries' => [24, 58],
            'technologies' => [100, 218],

            'in_house' => true,

            'media' => [],
        ];

        $response = $this->postJson('/solutions', $data);
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('awaiting_approval', $data['publish_status']);
        $this->assertEquals('AnoSol', $data['name']);
        $this->assertEquals('<h1>THIS IS GOING TO BE GREAT</h1><p>Seriously I mean it</p>', $data['about']);
        $this->assertArrayNotHasKey('client', $data);
        $this->assertTrue($data['in_house']);
        $this->assertArrayNotHasKey('unapproved_change', $data);

        // Solution should not be visible to other companies
        $this->actingAsGenericClient();
        $showResponse = $this->getJson("/solutions/{$response->json('data.slug')}");
        $showResponse->assertNotFound();
    }

    public function test_show_own(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson("/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID");
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('We are proud of this solution', $data['name']);
        $this->assertArrayHasKey('publish_status', $data);
        $this->assertArrayHasKey('vendor', $data);
        $this->assertArrayNotHasKey('unapproved_changes', $data);
    }

    public function test_show_other_public(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson("/solutions/$this->HIDDEN_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID");
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('YAPA', $data['name']);
        $this->assertArrayNotHasKey('publish_status', $data);
        $this->assertArrayNotHasKey('vendor', $data);
        $this->assertArrayNotHasKey('unapproved_changes', $data);
    }

    public function test_show_other_hidden_should_fail(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->getJson("/solutions/$this->HIDDEN_VENDOR_DRAFT_SOLUTION_PUBLIC_ID");
        $response->assertNotFound();
    }

    public function test_show_of_soft_deleted_company_should_fail(): void
    {
        Solution::with('vendor.company')
            ->where('public_id', $this->HIDDEN_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID)
            ->firstOrFail()
            ->vendor
            ->company
            ->delete();
        $this->actingAsGenericVendorClient();

        $response = $this->getJson("/solutions/$this->HIDDEN_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID");
        $response->assertNotFound();
    }

    public function test_show_dangerous_html_is_sanitized(): void
    {
        $this->actingAsGenericVendorClient();

        $data = [
            'as_draft' => false,

            'name' => 'AnoSol',
            'description' => 'Another great solution guys',
            'about' => '<h1 onclick="alert(\'xss\')">THIS IS GOING TO BE GREAT</h1><p>Seriously I mean it</p><script>alert("xss")</script>',
            'cover_resource_id' => '4Hq5t2',
            'country' => 'es',
            'main_industry_id' => 81,
            'length_type' => 'ongoing',
            'length' => null,
            'ftes' => 30,
            'value' => 10_000_000,

            'industries' => [24, 58],
            'technologies' => [100, 218],

            'in_house' => false,

            'client' => [
                'anonymous' => false,
                'id' => null,
                'name' => 'Labaš, s. r. o.',
                'review' => '<p onclick="alert(\'xss\')">Cheeky</p><script>alert("xss")</script>',
                'reviewer' => 'Luky',
                'reviewer_position' => 'CEO',
            ],

            'media' => [],
        ];

        $response = $this->postJson('/solutions', $data);
        $response->assertSuccessful();

        $data = $response->json('data');
        $this->assertIsArray($data);
        $this->assertEquals('<h1>THIS IS GOING TO BE GREAT</h1><p>Seriously I mean it</p>', $data['about']);
        $this->assertEquals('Cheekyalert("xss")', array_get($data, 'client.review'));
    }

    public function test_request_contact_should_send_email(): void
    {
        $this->actingAsGenericClient();

        $response = $this->postJson("/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID/contact", ['message' => 'I want you']);
        $response->assertSuccessful();

        // Having issues testing mail due to typing - if mails are faked, different class is returned
        // and this breaks our typing settings
    }

    public function test_request_contact_should_fail_for_own_solution(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->postJson("/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID/contact", ['message' => 'I want you']);
        $response->assertNotFound();
    }

    public function test_update_draft(): void
    {
        $this->actingAsGenericVendorClient();

        $data = [
            'as_draft' => true,

            'name' => 'Updated drafted solution', // changed
            'description' => 'Not sure what this is going to end up being but oh boi here we go',
            'about' => 'Placeholder but longer', // changed
            'cover_resource_id' => '4Hq5t2',
            'country' => 'cr',
            'main_industry_id' => 34,
            'length_type' => 'years',
            'length' => 3,
            'ftes' => 10, // changed
            'value' => null,

            'industries' => [],
            'technologies' => [],

            'in_house' => true,

            'media' => [],
        ];

        $response = $this->putJson("/solutions/$this->PUBLIC_VENDOR_DRAFT_SOLUTION_PUBLIC_ID", $data);
        $response->assertSuccessful();

        $showResponse = $this->getJson("/solutions/$this->PUBLIC_VENDOR_DRAFT_SOLUTION_PUBLIC_ID");
        $showResponse->assertSuccessful();
        $stored = $showResponse->json('data');

        $this->assertIsArray($stored);
        $this->assertEquals('draft', $stored['publish_status']);
        $this->assertEquals($data['name'], $stored['name']);
        $this->assertEquals($data['description'], $stored['description']);
        $this->assertEquals($data['about'], $stored['about']);
        $this->assertEquals($data['ftes'], $stored['ftes']);
        $this->assertEquals($data['value'], $stored['value']);
        $this->assertEmpty($stored['media']);
        $this->assertEmpty($stored['industries']);
        $this->assertEmpty($stored['technologies']);
        $this->assertArrayNotHasKey('unapproved_change', $stored);
    }

    public function test_update_draft_submit_for_review(): void
    {
        $this->actingAsGenericVendorClient();

        $data = [
            'as_draft' => false,

            'name' => 'Updated drafted solution', // changed
            'description' => 'Not sure what this is going to end up being but oh boi here we go',
            'about' => 'Placeholder but longer', // changed
            'cover_resource_id' => '4Hq5t2',
            'country' => 'cr',
            'main_industry_id' => 34,
            'length_type' => 'years',
            'length' => 3,
            'ftes' => 10, // changed
            'value' => null,

            'industries' => [],
            'technologies' => [],

            'in_house' => true,

            'media' => [],
        ];

        $response = $this->putJson("/solutions/$this->PUBLIC_VENDOR_DRAFT_SOLUTION_PUBLIC_ID", $data);
        $response->assertSuccessful();

        $showResponse = $this->getJson("/solutions/$this->PUBLIC_VENDOR_DRAFT_SOLUTION_PUBLIC_ID");
        $showResponse->assertSuccessful();
        $stored = $showResponse->json('data');

        $this->assertIsArray($stored);
        $this->assertEquals('awaiting_approval', $stored['publish_status']);
        $this->assertEquals($data['name'], $stored['name']);
        $this->assertEquals($data['description'], $stored['description']);
        $this->assertEquals($data['about'], $stored['about']);
        $this->assertEquals($data['ftes'], $stored['ftes']);
        $this->assertEquals($data['value'], $stored['value']);
        $this->assertEmpty($stored['media']);
        $this->assertEmpty($stored['industries']);
        $this->assertEmpty($stored['technologies']);
        $this->assertArrayNotHasKey('unapproved_change', $stored);
    }

    public function test_update_published(): void
    {
        $this->actingAsGenericVendorClient();

        $data = [
            'as_draft' => false,

            'name' => 'We are very proud of this solution', // changed
            'description' => 'We are not gonna tell what it is but our claim still stands. We are proud.',
            'about' => 'No longer interested in writing lorem ipsum parodies', // changed
            'cover_resource_id' => '4Hq5t2',
            'country' => 'ge',
            'main_industry_id' => 20, // changed
            'length_type' => 'ongoing',
            'length' => null,
            'ftes' => null,
            'value' => 9_876_543,

            'industries' => [],
            'technologies' => [100, 218, 148], // changed

            'in_house' => true,

            'media' => [],
        ];

        $response = $this->putJson("/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID", $data);
        $response->assertSuccessful();

        $showResponse = $this->getJson("/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID");
        $showResponse->assertSuccessful();
        $stored = $showResponse->json('data');

        $this->assertIsArray($stored);
        $this->assertEquals('published', $stored['publish_status']);

        $this->assertNotEquals($data['name'], $stored['name']);
        $this->assertEquals($data['description'], $stored['description']);
        $this->assertNotEquals($data['about'], $stored['about']);
        $this->assertNotEquals($data['main_industry_id'], $stored['main_industry_id']);
        $this->assertNotEquals($data['technologies'], $stored['technologies']);

        $this->assertArrayHasKey('unapproved_change', $stored);
        $this->assertEquals($data['name'], array_get($stored, 'unapproved_change.name'));
        $this->assertEquals($data['description'], array_get($stored, 'unapproved_change.description'));
        $this->assertEquals($data['about'], array_get($stored, 'unapproved_change.about'));
        $this->assertEquals($data['main_industry_id'], array_get($stored, 'unapproved_change.main_industry_id'));
        $this->assertEquals($data['technologies'], array_get($stored, 'unapproved_change.technologies'));
    }

    public function test_update_published_with_new_media(): void
    {
        $this->actingAsGenericVendorClient();

        $data = [
            'as_draft' => false,

            'name' => 'We are very proud of this solution', // changed
            'description' => 'We are not gonna tell what it is but our claim still stands. We are proud.',
            'about' => 'No longer interested in writing lorem ipsum parodies', // changed
            'cover_resource_id' => '4Hq5t2',
            'country' => 'ge',
            'main_industry_id' => 20, // changed
            'length_type' => 'ongoing',
            'length' => null,
            'ftes' => null,
            'value' => 9_876_543,

            'industries' => [],
            'technologies' => [100, 218, 148], // changed

            'in_house' => true,

            'media' => [
                [
                    'type' => 'image',
                    'source' => $this->SOLUTION_MEDIA_IMAGE_RESOURCE_PUBLIC_ID,
                ],
            ],
        ];

        $response = $this->putJson("/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID", $data);
        $response->assertSuccessful();

        $showResponse = $this->getJson("/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID");
        $showResponse->assertSuccessful();
        $stored = $showResponse->json('data');

        $this->assertIsArray($stored);
        $this->assertEquals('published', $stored['publish_status']);

        $this->assertNotEquals($data['name'], $stored['name']);
        $this->assertEquals($data['description'], $stored['description']);
        $this->assertNotEquals($data['about'], $stored['about']);
        $this->assertNotEquals($data['main_industry_id'], $stored['main_industry_id']);
        $this->assertNotEquals($data['technologies'], $stored['technologies']);
        $this->assertEmpty($stored['media']);

        $this->assertArrayHasKey('unapproved_change', $stored);
        $this->assertEquals($data['name'], array_get($stored, 'unapproved_change.name'));
        $this->assertEquals($data['description'], array_get($stored, 'unapproved_change.description'));
        $this->assertEquals($data['about'], array_get($stored, 'unapproved_change.about'));
        $this->assertEquals($data['main_industry_id'], array_get($stored, 'unapproved_change.main_industry_id'));
        $this->assertEquals($data['technologies'], array_get($stored, 'unapproved_change.technologies'));
        $this->assertNotEmpty(array_get($stored, 'unapproved_change.media'));
        $this->assertEquals(array_get($data, 'media.0.source'), array_get($stored, 'unapproved_change.media.0.source.id'));
    }

    public function test_discard_update(): void
    {
        $this->actingAsGenericVendorClient();

        $data = [
            'as_draft' => false,

            'name' => 'We are very proud of this solution', // changed
            'description' => 'We are not gonna tell what it is but our claim still stands. We are proud.',
            'about' => 'No longer interested in writing lorem ipsum parodies', // changed
            'cover_resource_id' => '4Hq5t2',
            'country' => 'ge',
            'main_industry_id' => 20, // changed
            'length_type' => 'ongoing',
            'length' => null,
            'ftes' => null,
            'value' => 9_876_543,

            'industries' => [],
            'technologies' => [100, 218, 148], // changed

            'in_house' => true,

            'media' => [],
        ];

        $response = $this->putJson("/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID", $data);
        $response->assertSuccessful();

        $showResponse = $this->getJson("/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID");
        $showResponse->assertSuccessful();
        $updated = $showResponse->json('data');
        $this->assertEquals('published', $updated['publish_status']);
        $this->assertArrayHasKey('unapproved_change', $updated);

        $discardResponse = $this->deleteJson("/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID/unapproved-change");
        $discardResponse->assertSuccessful();

        $secondShowResponse = $this->getJson("/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID");
        $secondShowResponse->assertSuccessful();
        $discarded = $secondShowResponse->json('data');
        $this->assertEquals('published', $discarded['publish_status']);
        $this->assertArrayNotHasKey('unapproved_change', $discarded);
    }

    public function test_discard_initial_review(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->deleteJson("/solutions/$this->PUBLIC_VENDOR_AWAITING_APPROVAL_SOLUTION_PUBLIC_ID/unapproved-change");
        $response->assertSuccessful();

        $showResponse = $this->getJson("/solutions/$this->PUBLIC_VENDOR_AWAITING_APPROVAL_SOLUTION_PUBLIC_ID");
        $this->assertEquals('draft', $showResponse->json('data.publish_status'));
    }

    public function test_delete(): void
    {
        $this->actingAsGenericVendorClient();

        $response = $this->deleteJson("/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID");
        $response->assertSuccessful();

        $showResponse = $this->getJson("/solutions/$this->PUBLIC_VENDOR_PUBLISHED_SOLUTION_PUBLIC_ID");
        $showResponse->assertNotFound();
    }

    public function test_guests_cannot_access_endpoints(): void
    {
        $this->assertEndpointsInaccessible($this->genericEndpoints);
        $this->assertEndpointsInaccessible($this->vendorEndpoints);
    }

    public function test_clients_cannot_access_vendor_endpoints(): void
    {
        $this->actingAsGenericClient();
        $this->assertEndpointsInaccessible($this->vendorEndpoints);
    }
}
