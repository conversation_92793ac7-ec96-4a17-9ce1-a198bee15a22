<?php

namespace Tests\Unit\Helpers;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Validator;
use Tests\Unit\UnitTestCase;

require_once './app/Helpers/password-rules.php';

class PasswordRulesTest extends UnitTestCase
{
    const VALID_BUT_COMPROMISED_PASSWORD = 'Password1!';

    public function test_that_uncompromised_will_be_ignored_in_local_environment(): void
    {
        App::detectEnvironment(fn () => 'local');

        $rules = [
            'password' => password_rules(),
        ];
        $input = [
            'password' => self::VALID_BUT_COMPROMISED_PASSWORD,
            'password_confirmation' => self::VALID_BUT_COMPROMISED_PASSWORD,
        ];

        $validator = Validator::make($input, $rules);
        $this->assertTrue($validator->passes());
    }

    public function test_that_uncompromised_will_be_ignored_in_test_environment(): void
    {
        $rules = [
            'password' => password_rules(),
        ];
        $input = [
            'password' => self::VALID_BUT_COMPROMISED_PASSWORD,
            'password_confirmation' => self::VALID_BUT_COMPROMISED_PASSWORD,
        ];

        $validator = Validator::make($input, $rules);
        $this->assertTrue($validator->passes());
    }

    public function test_that_uncompromised_will_be_enforced_in_production_environment(): void
    {
        App::detectEnvironment(fn () => 'production');

        $rules = [
            'password' => password_rules(),
        ];
        $input = [
            'password' => self::VALID_BUT_COMPROMISED_PASSWORD,
            'password_confirmation' => self::VALID_BUT_COMPROMISED_PASSWORD,
        ];

        $validator = Validator::make($input, $rules);
        $this->assertFalse($validator->passes());
    }

    public function test_min_size_fail(): void
    {
        $rules = [
            'password' => password_rules(),
        ];
        $input = [
            'password' => 'Pass1!',
            'password_confirmation' => 'Pass1!',
        ];

        $validator = Validator::make($input, $rules);
        $this->assertFalse($validator->passes());
    }

    public function test_min_size_pass(): void
    {
        $rules = [
            'password' => password_rules(),
        ];
        $input = [
            'password' => self::VALID_BUT_COMPROMISED_PASSWORD,
            'password_confirmation' => self::VALID_BUT_COMPROMISED_PASSWORD,
        ];

        $validator = Validator::make($input, $rules);
        $this->assertTrue($validator->passes());
    }

    public function test_max_size_fail(): void
    {
        $rules = [
            'password' => password_rules(),
        ];
        $input = [
            'password' => 'Password1!Password1!Password1!Password1!Password1!Password1!Password1!Password1!Password1!Password1!!',
            'password_confirmation' => 'Password1!Password1!Password1!Password1!Password1!Password1!Password1!Password1!Password1!Password1!!',
        ];

        $validator = Validator::make($input, $rules);
        $this->assertFalse($validator->passes());
    }

    public function test_max_size_pass(): void
    {
        $rules = [
            'password' => password_rules(),
        ];
        $input = [
            'password' => 'Password1!Password1!Password1!Password1!Password1!Password1!Password1!Password1!Password1!Password1!',
            'password_confirmation' => 'Password1!Password1!Password1!Password1!Password1!Password1!Password1!Password1!Password1!Password1!',
        ];

        $validator = Validator::make($input, $rules);
        $this->assertTrue($validator->passes());
    }

    public function test_confirmed_fail(): void
    {
        $rules = [
            'password' => password_rules(),
        ];
        $input = [
            'password' => 'Password1!',
            'password_confirmation' => 'Password2!',
        ];

        $validator = Validator::make($input, $rules);
        $this->assertFalse($validator->passes());
    }

    public function test_string_fail(): void
    {
        $rules = [
            'password' => password_rules(),
        ];
        $input = [
            'password' => 10,
            'password_confirmation' => 10,
        ];

        $validator = Validator::make($input, $rules);
        $this->assertFalse($validator->passes());
    }

    public function test_does_not_contain_numbers_fail(): void
    {
        $rules = [
            'password' => password_rules(),
        ];
        $input = [
            'password' => 'Password!!',
            'password_confirmation' => 'Password!!',
        ];

        $validator = Validator::make($input, $rules);
        $this->assertFalse($validator->passes());
    }

    public function test_does_not_contain_lowercase_chars_fail(): void
    {
        $rules = [
            'password' => password_rules(),
        ];
        $input = [
            'password' => 'PASSWORD1!',
            'password_confirmation' => 'PASSWORD1!',
        ];

        $validator = Validator::make($input, $rules);
        $this->assertFalse($validator->passes());
    }

    public function test_does_not_contain_uppercase_chars_fail(): void
    {
        $rules = [
            'password' => password_rules(),
        ];
        $input = [
            'password' => 'password1!',
            'password_confirmation' => 'password1!',
        ];

        $validator = Validator::make($input, $rules);
        $this->assertFalse($validator->passes());
    }

    public function test_default_required(): void
    {
        $rule = password_rules();
        $this->assertEquals('required', $rule[0]);
    }

    public function test_custom_required(): void
    {
        $rule = password_rules('required_without:email');
        $this->assertEquals('required_without:email', $rule[0]);
    }
}
