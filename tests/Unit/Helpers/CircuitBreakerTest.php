<?php

namespace Tests\Unit\Helpers;

use App\Exceptions\BrokenCircuitException;
use BadMethodCallException;
use Carbon\CarbonInterval;
use Exception;
use Illuminate\Support\Carbon;
use Tests\Unit\UnitTestCase;

require_once './app/Helpers/circuit-breaker.php';

class CircuitBreakerTest extends UnitTestCase
{
    public function test_that_circuit_breaker_returns_value_on_success(): void
    {
        $value = circuit_breaker('circuit_breaker_test_1', fn () => 'success');
        $this->assertEquals('success', $value);
    }

    public function test_that_circuit_breaker_rethrows_exception_on_failure(): void
    {
        $this->expectException(BadMethodCallException::class);
        circuit_breaker('circuit_breaker_test_2', fn () => throw new BadMethodCallException);
    }

    public function test_that_circuit_breaker_breaks_after_n_tries(): void
    {
        $action = fn () => circuit_breaker('circuit_breaker_test_3', fn () => throw new BadMethodCallException, threshold: 1);

        rescue($action);
        $this->expectException(BrokenCircuitException::class);
        $action();
    }

    public function test_that_circuit_breaker_resets_after_decay_time(): void
    {
        Carbon::setTestNow(now()->subMinutes(10));
        $action = fn (bool $failing) => fn () => circuit_breaker(
            'circuit_breaker_test_4',
            fn () => $failing ? throw new BadMethodCallException : 'success',
            decay: CarbonInterval::minute(),
            threshold: 1,
        );

        rescue($action(failing: true));
        $this->expectException(BrokenCircuitException::class);
        try {
            $action(failing: true)();
        } catch (Exception $e) {
            $this->assertInstanceOf(BrokenCircuitException::class, $e);
        }

        Carbon::setTestNow(now());
        $value = $action(failing: false)();
        $this->assertEquals('success', $value);
    }
}
