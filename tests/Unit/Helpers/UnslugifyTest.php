<?php

namespace Tests\Unit\Helpers;

use Tests\Unit\UnitTestCase;

require_once './app/Helpers/unslugify.php';

class UnslugifyTest extends UnitTestCase
{
    public function test_null_returns_null(): void
    {
        $result = unslugify(null);
        $this->assertNull($result);
    }

    public function test_id_without_slug_returns_id(): void
    {
        $result = unslugify('myid');
        $this->assertEquals('myid', $result);
    }

    public function test_slug_returns_only_id(): void
    {
        $result = unslugify('myid-with-some-slug');
        $this->assertEquals('myid', $result);
    }
}
