{"data": {"certifications": ["Keyword Research Course Semrush"], "dateOfBirth": null, "education": [{"id": 20789304, "organization": "Brno University of Technology", "accreditation": {"education": "Engineer's degree, Telecommunications and Information Technology", "educationLevel": null, "inputStr": "Engineer's degree, Telecommunications and Information Technology", "matchStr": ""}, "grade": null, "location": null, "dates": {"startDate": "2010-01-01", "completionDate": "2012-01-01", "isCurrent": false, "rawText": "2010 - 2012"}}], "emails": ["<EMAIL>"], "location": {"formatted": "Trencin, TC, Slovakia", "streetNumber": null, "street": null, "apartmentNumber": null, "city": "<PERSON><PERSON><PERSON>", "postalCode": null, "state": "Trenciansky kraj", "country": "Slovakia", "rawInput": "<PERSON><PERSON><PERSON><PERSON>, Trenciansky, Slovakia", "countryCode": "SK", "latitude": 18.029527, "longitude": 48.887725}, "name": {"raw": "<PERSON><PERSON>", "last": "Primator", "first": "<PERSON><PERSON>", "title": "", "middle": ""}, "objective": "", "phoneNumbers": ["+421987654321"], "publications": [], "referees": [], "sections": [{"bbox": [48.22, 58.492004, 398.1, 141.00098], "text": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Slovakia <EMAIL> +421987654321 linkedin.com/in/jozefprimator", "pageIndex": 0, "sectionType": "PersonalDetails"}, {"bbox": [48, 158.85797, 533.72974, 215.27301], "text": "Summary As a fullstack developer with over 8 years of experience, I have a strong background in Node and React and a proven track record of leading development teams. I have worked on a wide range of projects and have also developed several successful side projects. My skills and experience make me a valuable asset to any team.", "pageIndex": 0, "sectionType": "Summary"}, {"bbox": [49.022, 229.04602, 528.4098, 749.683], "text": "Experience Co-Founder LabZone Mar 2017 - Present (5 years 11 months) As a project manager at LabZone, I have been responsible for managing several client projects, ensuring that they are delivered on time and to the highest standards. I have also been involved in the development for Saidot, AI governance documentation tool, where I have worked as a fullstack developer. In this role, I have used my expertise in both project management and fullstack development to help drive the success of these projects. Co-Founder ARify Sep 2020 - Present (2 years 5 months) At ARify, I was responsible for implementing a software product for managing and displaying 3D models in AR. I implemented the product from the provided design, doing the programming and deployment to the servers. My work on this project involved using my skills in software development to deliver a successful product that met the requirements and exceeded the expectations of the client. Project Manager Q-EX Oct 2016 - Present (6 years 4 months) As a project manager at QEX, I was responsible for delivering queue management systems to clients such as Telecom, Slovak Post, and the Ministry of Interior. In addition to my project management responsibilities, I also worked as a developer, creating plugins and extensions for these systems using HTML, JavaScript, and CSS. Through my work at QEX, I have gained experience in both project management and software development, and have helped to deliver successful projects to a range of clien Project Manager IBM Jun 2012 - Sep 2016 (4 years 4 months) As a leader of international teams, I have been responsible for delivering web projects based on customer requirements. This has involved working with stakeholders, development, and management teams to plan project schedules, sprints, and releases. In some projects, I have acted as a", "pageIndex": 0, "sectionType": "WorkExperience"}, {"bbox": [253.165, 756.205, 357.785, 765.685], "text": "<PERSON><PERSON> - page 1", "pageIndex": 0, "sectionType": "Footer"}, {"bbox": [70.05, 57.204956, 526.7398, 411.195], "text": "ScrumMaster, and in others, I have acted as a Product Owner. My work has involved delivering a range of projects, including the TMS integration with IBM Web Content Manager, the migration of the Cloud Computing site to WCM and redesign with Havas, the migration of more than 30 thousand support documents to Enterprise Content Management, the creation of information about IBM client centers in ECM and feeding it to the sales portal, and managing monthly sprints for ECM common components modules development. I have also worked on the Human Ability and Accessibility project. Through my work, I have gained extensive experience in project management and web development. IT Project manager / Technical support Q-EX Jun 2008 - Jul 2012 (4 years 2 months) Technical support for systems like: - Customer Flow Management (CFM) - Digital Signage - Customer Satisfaction I planning new installations of systems, coordinate human and material resources, staff training and support for customers. Development new systems. Electrician EleSol Ltd. Jan 2006 - Dec 2006 (1 year) - wiring for small houses and companies - maintenance for Zara, Bershka, and Pull & Bear", "pageIndex": 1, "sectionType": "WorkExperience"}, {"bbox": [49.022, 433.658, 435.51996, 565.125], "text": "Education Brno University of Technology Engineer's degree, Telecommunications and Information Technology 2010 - 2012 Trencianska univerzita Alexandra Dubceka v Trencíne bachelor, Mechatronics 2004 - 2008", "pageIndex": 1, "sectionType": "Education"}, {"bbox": [49.078, 588.70996, 316.36993, 736.692], "text": "Licenses & Certifications Inbound - HubSpot Issued Apr 2020 - Expires May 2022 745eeefd1 be0449eb7a6cefe0a1ec3e3-1586724378624 <PERSON><PERSON><PERSON>y digitálneho marketingu - Google PN7 P5F M67 SEO Fundamentals - Semrush", "pageIndex": 1, "sectionType": "Training/Certifications"}, {"bbox": [253.165, 756.205, 359.085, 765.685], "text": "<PERSON><PERSON> - page 2", "pageIndex": 1, "sectionType": "Footer"}, {"bbox": [70.11, 57.294983, 329.068, 181.81598], "text": "Issued Jul 2021 - Expires Jul 2022 189526ff4e7f601 Keyword Research Course - Semrush Issued Aug 2021 - Expires Aug 2022 #15dbb4365047e95 Introduction to DevOps Course - Codecademy", "pageIndex": 2, "sectionType": "Training/Certifications"}, {"bbox": [48.504, 203.88995, 552.3, 246.29498], "text": "Skills Project Management • Enterprise Content Management • Agile Project Management • Problem Solving • PHP • User Experience • JavaServer Faces • JIRA • React.js • Node.js", "pageIndex": 2, "sectionType": "Skills/Interests/Languages"}, {"bbox": [253.165, 756.205, 359.165, 765.685], "text": "<PERSON><PERSON> - page 3", "pageIndex": 2, "sectionType": "Footer"}], "skills": [{"id": 214053708, "emsiId": "KS122Z073YX9J7V22JY3", "name": "Digital Signage", "lastUsed": "2012-07-01", "numberOfMonths": 49, "type": "hard_skill", "sources": [{"section": "WorkExperience", "position": 4, "workExperienceId": ********}]}, {"id": 214053709, "emsiId": "KS125F678LV2KB3Z5XW0", "name": "Problem Solving", "lastUsed": null, "numberOfMonths": null, "type": "soft_skill", "sources": [{"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}]}, {"id": 214053712, "emsiId": "ESEB1D4619E6E83A061D", "name": "Planning", "lastUsed": "2012-07-01", "numberOfMonths": 49, "type": "soft_skill", "sources": [{"section": "WorkExperience", "position": 4, "workExperienceId": ********}]}, {"id": 214053713, "emsiId": "KS122LN6CLX3P61KWSP2", "name": "Customer Satisfaction", "lastUsed": "2012-07-01", "numberOfMonths": 49, "type": "hard_skill", "sources": [{"section": "WorkExperience", "position": 4, "workExperienceId": ********}]}, {"id": 214053714, "emsiId": "KS1203C6N9B52QGB4H67", "name": "Research", "lastUsed": null, "numberOfMonths": null, "type": "soft_skill", "sources": [{"section": "Training/Certifications", "position": null, "workExperienceId": null}]}, {"id": 214053715, "emsiId": "KS120QQ6ZN003B8B7FK1", "name": "JIRA", "lastUsed": null, "numberOfMonths": null, "type": "hard_skill", "sources": [{"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}]}, {"id": 214053716, "emsiId": "KS1267F6MSPN366LX7ST", "name": "Project Management", "lastUsed": "2023-04-16", "numberOfMonths": 204, "type": "hard_skill", "sources": [{"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}, {"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}, {"section": "WorkExperience", "position": 0, "workExperienceId": 36150033}, {"section": "WorkExperience", "position": 2, "workExperienceId": 36150035}, {"section": "WorkExperience", "position": 3, "workExperienceId": ********}, {"section": "WorkExperience", "position": 0, "workExperienceId": 36150033}, {"section": "WorkExperience", "position": 2, "workExperienceId": 36150035}, {"section": "WorkExperience", "position": 3, "workExperienceId": ********}]}, {"id": 214053718, "emsiId": "KS125716TLTGH6SDHJD1", "name": "Integration", "lastUsed": "2016-09-01", "numberOfMonths": 51, "type": "soft_skill", "sources": [{"section": "WorkExperience", "position": 3, "workExperienceId": ********}]}, {"id": 214053719, "emsiId": "KS1200578T5QCYT0Z98G", "name": "HyperText Markup Language (HTML)", "lastUsed": "2023-04-16", "numberOfMonths": 79, "type": "hard_skill", "sources": [{"section": "WorkExperience", "position": 2, "workExperienceId": 36150035}]}, {"id": 214053720, "emsiId": "KS122W96T2T9J5PZ0VMP", "name": "DevOps", "lastUsed": null, "numberOfMonths": null, "type": "hard_skill", "sources": [{"section": "Training/Certifications", "position": null, "workExperienceId": null}]}, {"id": 214053724, "emsiId": "KS1218W78FGVPVP2KXPX", "name": "Management", "lastUsed": "2023-04-16", "numberOfMonths": 253, "type": "soft_skill", "sources": [{"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}, {"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}, {"section": "WorkExperience", "position": 0, "workExperienceId": 36150033}, {"section": "WorkExperience", "position": 2, "workExperienceId": 36150035}, {"section": "WorkExperience", "position": 3, "workExperienceId": ********}, {"section": "WorkExperience", "position": 4, "workExperienceId": ********}, {"section": "WorkExperience", "position": 0, "workExperienceId": 36150033}, {"section": "WorkExperience", "position": 2, "workExperienceId": 36150035}, {"section": "WorkExperience", "position": 3, "workExperienceId": ********}, {"section": "WorkExperience", "position": 4, "workExperienceId": ********}]}, {"id": 214053727, "emsiId": "KS124FP642Q7P7TBPPZN", "name": "Search Engine Optimization", "lastUsed": null, "numberOfMonths": null, "type": "hard_skill", "sources": [{"section": "Training/Certifications", "position": null, "workExperienceId": null}]}, {"id": 214053729, "emsiId": "ES9C0ADA3C9397DD6AED", "name": "Agile Software Development", "lastUsed": null, "numberOfMonths": null, "type": "hard_skill", "sources": [{"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}]}, {"id": 214053730, "emsiId": "KS1200C5XQWW78VQ5ZYL", "name": "PHP (Scripting Language)", "lastUsed": null, "numberOfMonths": null, "type": "hard_skill", "sources": [{"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}]}, {"id": 214053731, "emsiId": "KS127296VDYS7ZFWVC46", "name": "Node.js", "lastUsed": null, "numberOfMonths": null, "type": "hard_skill", "sources": [{"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}]}, {"id": 214053732, "emsiId": "KS124G66QWSYM012SWS5", "name": "Governance", "lastUsed": "2023-04-16", "numberOfMonths": 74, "type": "soft_skill", "sources": [{"section": "WorkExperience", "position": 0, "workExperienceId": 36150033}]}, {"id": 214053733, "emsiId": "KS123X777H5WFNXQ6BPM", "name": "Sales", "lastUsed": "2016-09-01", "numberOfMonths": 51, "type": "soft_skill", "sources": [{"section": "WorkExperience", "position": 3, "workExperienceId": ********}]}, {"id": 214053735, "emsiId": "KSDJCA4E89LB98JAZ7LZ", "name": "React.js", "lastUsed": null, "numberOfMonths": null, "type": "hard_skill", "sources": [{"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}]}, {"id": 214053736, "emsiId": "KS122BM7899YNC6504SP", "name": "Content Management", "lastUsed": "2016-09-01", "numberOfMonths": 51, "type": "hard_skill", "sources": [{"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}, {"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}, {"section": "WorkExperience", "position": 3, "workExperienceId": ********}, {"section": "WorkExperience", "position": 3, "workExperienceId": ********}]}, {"id": 214053737, "emsiId": "KS120LB6XV5409BCQ0NF", "name": "Technical Support", "lastUsed": "2012-07-01", "numberOfMonths": 49, "type": "hard_skill", "sources": [{"section": "WorkExperience", "position": 4, "workExperienceId": ********}]}, {"id": 214053739, "emsiId": "KS120L96KMYTDJ48NRSH", "name": "Software Development", "lastUsed": "2023-04-16", "numberOfMonths": 110, "type": "hard_skill", "sources": [{"section": "WorkExperience", "position": 1, "workExperienceId": 36150034}, {"section": "WorkExperience", "position": 2, "workExperienceId": 36150035}]}, {"id": 214053740, "emsiId": "KS125KY6XZ7V6QPKZ995", "name": "JavaServer Faces", "lastUsed": null, "numberOfMonths": null, "type": "hard_skill", "sources": [{"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}]}, {"id": 214053741, "emsiId": "KS1200771D9CR9LB4MWW", "name": "JavaScript (Programming Language)", "lastUsed": "2023-04-16", "numberOfMonths": 79, "type": "hard_skill", "sources": [{"section": "WorkExperience", "position": 2, "workExperienceId": 36150035}]}, {"id": 214053710, "emsiId": "KS128B06CRVJ3WXRP7KH", "name": "Queue Management Systems", "lastUsed": "2023-04-16", "numberOfMonths": 79, "type": "hard_skill", "sources": [{"section": "WorkExperience", "position": 2, "workExperienceId": 36150035}]}, {"id": 214053711, "emsiId": "ES38E4DE8DCF54349FAE", "name": "Project Schedules", "lastUsed": "2016-09-01", "numberOfMonths": 51, "type": "hard_skill", "sources": [{"section": "WorkExperience", "position": 3, "workExperienceId": ********}]}, {"id": 214053717, "emsiId": "KS125MP70K0J6WFJSG88", "name": "Keyword Research", "lastUsed": null, "numberOfMonths": null, "type": "hard_skill", "sources": [{"section": "Training/Certifications", "position": null, "workExperienceId": null}]}, {"id": 214053721, "emsiId": "KS1239R78R876ZSVP49M", "name": "Enterprise Content Management", "lastUsed": "2016-09-01", "numberOfMonths": 51, "type": "hard_skill", "sources": [{"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}, {"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}, {"section": "WorkExperience", "position": 3, "workExperienceId": ********}, {"section": "WorkExperience", "position": 3, "workExperienceId": ********}]}, {"id": 214053722, "emsiId": "KS1220H6CP1BQGH9STNK", "name": "Cloud Computing", "lastUsed": "2016-09-01", "numberOfMonths": 51, "type": "hard_skill", "sources": [{"section": "WorkExperience", "position": 3, "workExperienceId": ********}]}, {"id": 214053723, "emsiId": "KS441PL6JPXW200W0GRQ", "name": "User Experience", "lastUsed": null, "numberOfMonths": null, "type": "hard_skill", "sources": [{"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}]}, {"id": 214053725, "emsiId": "KSRX3X8IGTV51CGCKHLD", "name": "Hubspot", "lastUsed": null, "numberOfMonths": null, "type": "hard_skill", "sources": [{"section": "Training/Certifications", "position": null, "workExperienceId": null}]}, {"id": 214053726, "emsiId": "KS1264669X43NJFLT6SN", "name": "Management Systems", "lastUsed": "2023-04-16", "numberOfMonths": 79, "type": "hard_skill", "sources": [{"section": "WorkExperience", "position": 2, "workExperienceId": 36150035}]}, {"id": 214053734, "emsiId": "ES5084E497CF6A499186", "name": "Agile Project Management", "lastUsed": null, "numberOfMonths": null, "type": "hard_skill", "sources": [{"section": "Skills/Interests/Languages", "position": null, "workExperienceId": null}]}, {"id": 214053738, "emsiId": "ESD2D457783B84F78E33", "name": "SEMrush (Software)", "lastUsed": null, "numberOfMonths": null, "type": "hard_skill", "sources": [{"section": "Training/Certifications", "position": null, "workExperienceId": null}]}], "languages": ["Slovak", "English"], "summary": "As a fullstack developer with over 8 years of experience, I have a strong background in Node and React and a proven track record of leading development teams. I have worked on a wide range of projects and have also developed several successful side projects. My skills and experience make me a valuable asset to any team.", "websites": ["linkedin.com/in/jozefprimator"], "linkedin": "linkedin.com/in/jozefprimator", "totalYearsExperience": 16, "profession": "Project Manager - Manager", "workExperience": [{"id": 36150033, "jobTitle": "Co-Founder", "organization": "LabZone", "location": null, "dates": {"startDate": "2017-03-01", "endDate": "2023-04-16", "monthsInPosition": 74, "isCurrent": true, "rawText": "Mar 2017 - Present"}, "jobDescription": "As a project manager at LabZone, I have been responsible for managing several client projects, ensuring that they are delivered on time and to the highest standards. I have also been involved in the development for Saidot, AI governance documentation tool, where I have worked as a fullstack developer. In this role, I have used my expertise in both project management and fullstack development to help drive the success of these projects. ", "occupation": {"jobTitle": "Co-Founder", "jobTitleNormalized": "Founder", "classification": {"socCode": 1259, "title": "Managers and proprietors in other services n.e.c.", "minorGroup": "Managers and Proprietors in Other Services", "subMajorGroup": "OTHER MANAGERS AND PROPRIETORS", "majorGroup": "MANAGERS, DIRECTORS AND SE<PERSON>OR OFFICIALS"}, "managementLevel": "Upper"}}, {"id": 36150034, "jobTitle": "Co-Founder", "organization": "ARify", "location": null, "dates": {"startDate": "2020-09-01", "endDate": "2023-04-16", "monthsInPosition": 31, "isCurrent": true, "rawText": "Sep 2020 - Present"}, "jobDescription": "At ARify, I was responsible for implementing a software product for managing and displaying 3D models in AR. I implemented the product from the provided design, doing the programming and deployment to the servers. My work on this project involved using my skills in software development to deliver a successful product that met the requirements and exceeded the expectations of the client. ", "occupation": {"jobTitle": "Co-Founder", "jobTitleNormalized": "Founder", "classification": {"socCode": 1259, "title": "Managers and proprietors in other services n.e.c.", "minorGroup": "Managers and Proprietors in Other Services", "subMajorGroup": "OTHER MANAGERS AND PROPRIETORS", "majorGroup": "MANAGERS, DIRECTORS AND SE<PERSON>OR OFFICIALS"}, "managementLevel": "Upper"}}, {"id": 36150035, "jobTitle": "Project Manager", "organization": "Q-EX", "location": null, "dates": {"startDate": "2016-10-01", "endDate": "2023-04-16", "monthsInPosition": 79, "isCurrent": true, "rawText": "Oct 2016 - Present"}, "jobDescription": "As a project manager at QEX, I was responsible for delivering queue management systems to clients such as Telecom, Slovak Post, and the Ministry of Interior. In addition to my project management responsibilities, I also worked as a developer, creating plugins and extensions for these systems using HTML, JavaScript, and CSS. Through my work at QEX, I have gained experience in both project management and software development, and have helped to deliver successful projects to a range of ", "occupation": {"jobTitle": "Project Manager", "jobTitleNormalized": "Product/Project Manager", "classification": {"socCode": 3556, "title": "Sales accounts and business development managers", "minorGroup": "Sales, Marketing and Related Associate Professionals", "subMajorGroup": "BUSINESS AND <PERSON><PERSON><PERSON><PERSON> SERVICE ASSOCIATE PROFESSIONALS", "majorGroup": "ASSOCIATE PROFESSIONAL OCCUPATIONS"}, "managementLevel": "Mid"}}, {"id": ********, "jobTitle": "Project Manager", "organization": "Ibm", "location": null, "dates": {"startDate": "2012-06-01", "endDate": "2016-09-01", "monthsInPosition": 51, "isCurrent": false, "rawText": "Jun 2012 - Sep 2016"}, "jobDescription": "As a leader of international teams, I have been responsible for delivering web projects based on customer requirements. This has involved working with stakeholders, development, and management teams to plan project schedules, sprints, and releases. In some projects, I have acted as a ScrumMaster, and in others, I have acted as a Product Owner. My work has involved delivering a range of projects, including the TMS integration with IBM Web Content Manager, the migration of the Cloud Computing site to WCM and redesign with Havas, the migration of more than 30 thousand support documents to Enterprise Content Management, the creation of information about IBM client centers in ECM and feeding it to the sales portal, and managing monthly sprints for ECM common components modules development. I have also worked on the Human Ability and Accessibility project. Through my work, I have gained extensive experience in project management and web development. ", "occupation": {"jobTitle": "Project Manager", "jobTitleNormalized": "Product/Project Manager", "classification": {"socCode": 3556, "title": "Sales accounts and business development managers", "minorGroup": "Sales, Marketing and Related Associate Professionals", "subMajorGroup": "BUSINESS AND <PERSON><PERSON><PERSON><PERSON> SERVICE ASSOCIATE PROFESSIONALS", "majorGroup": "ASSOCIATE PROFESSIONAL OCCUPATIONS"}, "managementLevel": "Mid"}}, {"id": ********, "jobTitle": "IT Project manager / Technical support", "organization": "Q-EX", "location": null, "dates": {"startDate": "2008-06-01", "endDate": "2012-07-01", "monthsInPosition": 49, "isCurrent": false, "rawText": "Jun 2008 - Jul 2012"}, "jobDescription": "Technical support for systems like: \n-Customer Flow Management (CFM) \n-Digital Signage \n-Customer Satisfaction I planning new installations of systems, coordinate human and material resources, staff training and support for customers. Development new systems. ", "occupation": {"jobTitle": "IT Project manager / Technical support", "jobTitleNormalized": "IT Technical Support Manager", "classification": {"socCode": 2132, "title": "IT managers", "minorGroup": "Information Technology Professionals", "subMajorGroup": "SCIENCE, RESEA<PERSON><PERSON>, ENGINEERING AND TECH<PERSON>OLOGY PROFESSIONALS", "majorGroup": "PROFESSIONAL OCCUPATIONS"}, "managementLevel": "Low"}}, {"id": 36150038, "jobTitle": "Electrician", "organization": "EleSol Ltd.", "location": null, "dates": {"startDate": "2006-01-01", "endDate": "2006-12-01", "monthsInPosition": 11, "isCurrent": false, "rawText": "Jan 2006 - Dec 2006"}, "jobDescription": "-wiring for small houses and companies \n-maintenance for Zara, Bershka, and Pull & Bear ", "occupation": {"jobTitle": "Electrician", "jobTitleNormalized": "Electrician", "classification": {"socCode": 5241, "title": "Electricians and electrical fitters", "minorGroup": "Electrical and Electronic Trades", "subMajorGroup": "SKILLED METAL, <PERSON>LECT<PERSON><PERSON><PERSON> AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> TRADES", "majorGroup": "SKILLED TRADES OCCUPATIONS"}, "managementLevel": "Low"}}], "headShot": null, "isResumeProbability": 100, "rawText": "<PERSON><PERSON> Primator <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Slovakia <EMAIL> +421987654321 linkedin.com/in/jozefprimator\nSummary As a fullstack developer with over 8 years of experience, I have a strong background in Node and React and a proven track record of leading development teams. I have worked on a wide range of projects and have also developed several successful side projects. My skills and experience make me a valuable asset to any team.\nExperience Co-Founder LabZone Mar 2017 - Present (5 years 11 months) As a project manager at LabZone, I have been responsible for managing several client projects, ensuring that they are delivered on time and to the highest standards. I have also been involved in the development for Saidot, AI governance documentation tool, where I have worked as a fullstack developer. In this role, I have used my expertise in both project management and fullstack development to help drive the success of these projects. Co-Founder ARify Sep 2020 - Present (2 years 5 months) At ARify, I was responsible for implementing a software product for managing and displaying 3D models in AR. I implemented the product from the provided design, doing the programming and deployment to the servers. My work on this project involved using my skills in software development to deliver a successful product that met the requirements and exceeded the expectations of the client. Project Manager Q-EX Oct 2016 - Present (6 years 4 months) As a project manager at QEX, I was responsible for delivering queue management systems to clients such as Telecom, Slovak Post, and the Ministry of Interior. In addition to my project management responsibilities, I also worked as a developer, creating plugins and extensions for these systems using HTML, JavaScript, and CSS. Through my work at QEX, I have gained experience in both project management and software development, and have helped to deliver successful projects to a range of clien Project Manager IBM Jun 2012 - Sep 2016 (4 years 4 months) As a leader of international teams, I have been responsible for delivering web projects based on customer requirements. This has involved working with stakeholders, development, and management teams to plan project schedules, sprints, and releases. In some projects, I have acted as a\nJozef Primator - page 1\nScrumMaster, and in others, I have acted as a Product Owner. My work has involved delivering a range of projects, including the TMS integration with IBM Web Content Manager, the migration of the Cloud Computing site to WCM and redesign with Havas, the migration of more than 30 thousand support documents to Enterprise Content Management, the creation of information about IBM client centers in ECM and feeding it to the sales portal, and managing monthly sprints for ECM common components modules development. I have also worked on the Human Ability and Accessibility project. Through my work, I have gained extensive experience in project management and web development. IT Project manager / Technical support Q-EX Jun 2008 - Jul 2012 (4 years 2 months) Technical support for systems like: - Customer Flow Management (CFM) - Digital Signage - Customer Satisfaction I planning new installations of systems, coordinate human and material resources, staff training and support for customers. Development new systems. Electrician EleSol Ltd. Jan 2006 - Dec 2006 (1 year) - wiring for small houses and companies - maintenance for Zara, Bershka, and Pull & Bear\nEducation Brno University of Technology Engineer's degree, Telecommunications and Information Technology 2010 - 2012 Trencianska univerzita Alexandra Dubceka v Trencíne bachelor, Mechatronics 2004 - 2008\nLicenses & Certifications Inbound - HubSpot Issued Apr 2020 - Expires May 2022 745eeefd1 be0449eb7a6cefe0a1ec3e3-1586724378624 Základy digitálneho marketingu - Google PN7 P5F M67 SEO Fundamentals - Semrush\nJozef Primator - page 2\nIssued Jul 2021 - Expires Jul 2022 189526ff4e7f601 Keyword Research Course - Semrush Issued Aug 2021 - Expires Aug 2022 #15dbb4365047e95 Introduction to DevOps Course - Codecademy\nSkills Project Management • Enterprise Content Management • Agile Project Management • Problem Solving • PHP • User Experience • JavaServer Faces • JIRA • React.js • Node.js\nJozef Primator - page 3", "redactedText": "****** ******** ******** ************ ******** *************************** ************* ******************************\nSummary As a fullstack developer with over * years of experience, I have a strong background in Node and React and a proven track record of leading development teams. I have worked on a wide range of projects and have also developed several successful side projects. My skills and experience make me a valuable asset to any team.\nExperience Co-Founder LabZone *** **** * ******* (5 years ** ***ths) As a project manager at LabZone, I have been responsible for managing several client projects, ensuring that **** are delivered on time and to the highest standards. I have also been involved in the development for Saidot, AI governance documentation tool, where I have worked as a fullstack developer. In this role, I have used my expertise in both project management and fullstack development to help drive the success of these projects. Co-Founder ARify *** **** * ******* (2 years * ***ths) At ARify, I was responsible for implementing a software product for managing and displaying 3D models in AR. I implemented the product from the provided design, doing the programming and deployment to the servers. My work on this project involved using my skills in software development to deliver a successful product that met the requirements and exceeded the expectations of the client. Project Manager Q-EX *** **** * ******* (6 years * ***ths) As a project manager at QEX, I was responsible for delivering queue management systems to clients such as Telecom, Slovak Post, and the Ministry of Interior. In addition to my project management responsibilities, I also worked as a developer, creating plugins and extensions for these systems using HTML, JavaScript, and CSS. Through my work at QEX, I have gained experience in both project management and software development, and have helped to deliver successful projects to a range of clien Project Manager IBM *** **** * *** **** (4 years * ***ths) As a leader of international teams, I have been responsible for delivering web projects based on customer requirements. This has involved working with stakeholders, development, and management teams to plan project schedules, sprints, and releases. In some projects, I have acted as a\n****** ******** - page 1\nScrumMaster, and in others, I have acted as a Product Owner. My work has involved delivering a range of projects, including the TMS integration with IBM Web Content Manager, the migration of the Cloud Computing site to WCM and redesign with Havas, the migration of more than ** thousand support documents to Enterprise Content Management, the creation of information about IBM client centers in ECM and feeding it to the sales portal, and managing monthly sprints for ECM common components modules development. I have also worked on the Human Ability and Accessibility project. Through my work, I have gained extensive experience in project management and web development. IT Project manager / Technical support Q-EX *** **** * *** **** (4 years * ***ths) Technical support for systems like: - Customer Flow Management (CFM) - Digital Signage - Customer Satisfaction I planning new installations of systems, coordinate human and material resources, staff training and support for customers. Development new systems. Electrician EleSol Ltd. *** **** * *** **** (1 year) - wiring for small houses and companies - maintenance for Zara, Bershka, and Pull & Bear\nEducation Brno University of Technology Engineer's degree, Telecommunications and Information Technology **** * **** Trencianska univerzita Alexandra Dubceka v Trencíne bachelor, Mechatronics **** * ****\nLicenses & Certifications Inbound - HubSpot Issued *** **** * Expires *** **** ***eeefd1 be0449eb7a6cefe0a1ec3e3************** Základy digitálneho marketingu - Google PN7 P5F M67 SEO Fundamentals - Semrush\n****** ******** - page 2\nIssued *** **** * Expires *** **** *****6ff4e7f601 Keyword Research Course - Semrush Issued *** **** * Expires *** **** #15dbb*******e95 Introduction to DevOps Course - Codecademy\nSkills Project Management • Enterprise Content Management • Agile Project Management • Problem Solving • PHP • User Experience • JavaServer Faces • JIRA • ******** • *******\n****** ******** - page 3"}, "meta": {"identifier": "manual_admin-1-1g23RqfRpg", "ready": true, "failed": false, "readyDt": "2023-04-16T18:05:28.261383Z", "fileName": "RRSdcq09kYtrHXo9XuOpkPdbXOlcwtuzytzCwEO5.pdf", "expiryTime": null, "language": "en", "pdf": "https://affinda-api-data-prod-ap1.s3.amazonaws.com/media/documents/RRSdcq09kYtrHXo9XuOpkPdbXOlcwtuzytzCwEO5_6s3FcKt.pdf?AWSAccessKeyId=********************&Signature=wEKf7HLM%2FQKpHTjavBK3vaDUA1I%3D&x-amz-security-token=FwoGZXIvYXdzEEsaDN09HUn9w%2FaLi17SVCKgBD9ksxUGvX%2Bp7iSaQFoPoeIndOFt0o2AdUd4bgSN1aDiT24wmE5t%2FkgAYc5A%2FMj2H44ks9LYvLVbApLRr4au%2FQ1BRYTU7iKgLg6MzNOBKoDngBe8DQATXK2kgkcQiptnzLsiHVTcQ5zTfDp72iryism7ptrvUdq%2FVcUBGN2HdXg9waZEva2Nw6xey%2BrBK8L7K2mUV5oZydthhydHbVTR03wHIfJkp1XJp0Pmp8efp%2Fwemg%2B50u7unAbYd8Y3lATgP2Rmmq%2BVkGFzmU7rKaJzgKZ1awxKQV1TRjioaXzrIvA5Q804cbYU8QvjB57uW2nUXkKEuoXL%2Btxwa%2FcS4kh%2F0zMHQMgKTJSkiZ8tfMw9S8HKNqTwCuy4%2B6GIHHR9hQkkvdUonRQe1%2F6u0JPzKl1xEfketkEXsrj%2FVswItTXpgM6wSZUSCSTqFpVzwhdMi%2FNrwPeJU0iLGRoolX7g5M%2B8wvw7yoCAWU%2Bf4YjlQst3781rQzfF88kfmYh74y7xXjArbimiq65v8%2BIUyL1k1LtHFl4rGqUYBBGyFv15U%2BaKLNbTT5SJEujHX8Jdkgl%2B6iNZRH8qjwsqnp9Yh6wSNfU05wwTbd957%2F8dWb9PcfYyuQgVtk63Zt7F6Z%2Bl23Piq695dg%2BybmKX3Zbh3Ft1rTOevIwVt2z9BM8ZVHITSHRXlo%2FomdgukYdw2mhbW4S6aiVmJ21f%2B5%2Fh7l7920hya6uyjeYowe3woQYyKtQ0HXSEwVMyrqRLZyiMn%2F1lKau2qkOqi81q4auz%2Bfn6agiVVTj2Y90P3w%3D%3D&Expires=1681671928", "parentDocument": null, "childDocuments": [], "pages": [{"id": 20141896, "pageIndex": 0, "image": null, "height": 792, "width": 612, "rotation": 0}, {"id": 20141897, "pageIndex": 1, "image": null, "height": 792, "width": 612, "rotation": 0}, {"id": 20141898, "pageIndex": 2, "image": null, "height": 792, "width": 612, "rotation": 0}], "ocrConfidence": null, "reviewUrl": null, "validatedDt": "2023-04-16T18:05:28.261383Z", "isVerified": true, "exportedDt": null, "isExported": false, "createdDt": "2023-04-16T18:05:24.792539Z", "errorDetail": null, "documentType": "resume", "file": "https://affinda-api-data-prod-ap1.s3.amazonaws.com/media/documents/RRSdcq09kYtrHXo9XuOpkPdbXOlcwtuzytzCwEO5_6s3FcKt.pdf?AWSAccessKeyId=********************&Signature=wEKf7HLM%2FQKpHTjavBK3vaDUA1I%3D&x-amz-security-token=FwoGZXIvYXdzEEsaDN09HUn9w%2FaLi17SVCKgBD9ksxUGvX%2Bp7iSaQFoPoeIndOFt0o2AdUd4bgSN1aDiT24wmE5t%2FkgAYc5A%2FMj2H44ks9LYvLVbApLRr4au%2FQ1BRYTU7iKgLg6MzNOBKoDngBe8DQATXK2kgkcQiptnzLsiHVTcQ5zTfDp72iryism7ptrvUdq%2FVcUBGN2HdXg9waZEva2Nw6xey%2BrBK8L7K2mUV5oZydthhydHbVTR03wHIfJkp1XJp0Pmp8efp%2Fwemg%2B50u7unAbYd8Y3lATgP2Rmmq%2BVkGFzmU7rKaJzgKZ1awxKQV1TRjioaXzrIvA5Q804cbYU8QvjB57uW2nUXkKEuoXL%2Btxwa%2FcS4kh%2F0zMHQMgKTJSkiZ8tfMw9S8HKNqTwCuy4%2B6GIHHR9hQkkvdUonRQe1%2F6u0JPzKl1xEfketkEXsrj%2FVswItTXpgM6wSZUSCSTqFpVzwhdMi%2FNrwPeJU0iLGRoolX7g5M%2B8wvw7yoCAWU%2Bf4YjlQst3781rQzfF88kfmYh74y7xXjArbimiq65v8%2BIUyL1k1LtHFl4rGqUYBBGyFv15U%2BaKLNbTT5SJEujHX8Jdkgl%2B6iNZRH8qjwsqnp9Yh6wSNfU05wwTbd957%2F8dWb9PcfYyuQgVtk63Zt7F6Z%2Bl23Piq695dg%2BybmKX3Zbh3Ft1rTOevIwVt2z9BM8ZVHITSHRXlo%2FomdgukYdw2mhbW4S6aiVmJ21f%2B5%2Fh7l7920hya6uyjeYowe3woQYyKtQ0HXSEwVMyrqRLZyiMn%2F1lKau2qkOqi81q4auz%2Bfn6agiVVTj2Y90P3w%3D%3D&Expires=1681671928"}, "error": {"errorCode": null, "errorDetail": null}}