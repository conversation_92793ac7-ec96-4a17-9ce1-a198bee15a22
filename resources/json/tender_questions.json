[{"question": "What is the project's detailed description?", "id": "project_description", "placeholder": "Development of a vendor management platform using Laravel and Vue.js", "minChars": 100}, {"question": "What technologies or tools are required for the project?", "id": "technologies", "placeholder": "P<PERSON> (Laravel), JavaScript (Vue.js), <PERSON><PERSON>", "recommended": [{"name": "Python", "content": "Python"}, {"name": "Java", "content": "Java"}, {"name": "C# & .NET", "content": "C# & .NET"}, {"name": "Node.js", "content": "Node.js"}, {"name": "React & Vue.js", "content": "React & Vue.js"}, {"name": "Angular", "content": "Angular"}, {"name": "Swift & Kotlin", "content": "Swift & Kotlin"}, {"name": "Go & Rust", "content": "Go & Rust"}, {"name": "AWS, Azure & GCP", "content": "AWS, Azure & GCP"}, {"name": "Kubernetes & Docker", "content": "Kubernetes & Docker"}]}, {"question": "What is the preferred geographic location for vendors?", "id": "location", "placeholder": "European Union", "recommended": [{"name": "EU", "content": "European Union"}, {"name": "USA & Canada", "content": "United States and Canada"}, {"name": "Latin America", "content": "Latin America"}, {"name": "Eastern Europe", "content": "Eastern Europe"}, {"name": "Asia-Pacific", "content": "Asia-Pacific"}]}, {"question": "What specific positions are required for the project?<br><span class=\"text-base\">For each position, provide the following information: Role name, estimated hourly rate, number of candidates needed.</span><br><span class=\"text-sm font-bold italic\">If you can’t provide specific positions for this request, including rates and quantity, our matching engine will not be able to determine the best results.</span>", "id": "positions", "placeholder": "Backend Developer, Hourly Rate: €55, Candidates: 2, Frontend Developer, Hourly Rate: €50, Candidates: 2", "recommended": [{"name": "Backend Developer", "content": "Backend Developer, Hourly Rate: €55, Candidates: 2"}, {"name": "Frontend Developer", "content": "Frontend Developer, Hourly Rate: €50, Candidates: 2"}, {"name": "Data Engineer", "content": "Data Engineer, Hourly Rate: €60, Candidates: 1"}, {"name": "DevOps Engineer", "content": "DevOps Engineer, Hourly Rate: €65, Candidates: 1"}, {"name": "Project Manager", "content": "Project Manager, Hourly Rate: €70, Candidates: 1"}, {"name": "Fullstack Developer", "content": "Fullstack Developer, Hourly Rate: €50, Candidates: 3"}]}]