@layer components {
    :root {
        --nio-blue-800: #0071E3;
        --nio-blue-600-hover: #348DE9;
        --nio-gradient-blue: #0071E3;
        --nio-blue-outline-stroke-400: #BBD0FB;
    }

    .nio-button {
        background: linear-gradient(to right, var(--nio-blue-800, #0071E3), var(--nio-blue-800)),
        linear-gradient(89deg, var(--nio-blue-800) 1.27%, var(--nio-blue-outline-stroke-400) 98.73%);
        background-clip: padding-box, border-box;
        background-origin: padding-box, border-box;
    }

    .nio-button:hover,
    .nio-button:active {
        background: linear-gradient(to right, var(--nio-blue-600-hover), var(--nio-blue-600-hover)),
        linear-gradient(89deg, var(--nio-blue-800) 1.27%, var(--nio-blue-outline-stroke-400) 98.73%);
        background-clip: padding-box, border-box;
        background-origin: padding-box, border-box;
    }
}
