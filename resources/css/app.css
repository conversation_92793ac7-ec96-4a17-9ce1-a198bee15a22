@import "auth.css";

@tailwind base;
@tailwind components;
@tailwind utilities;


@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes rotate-in {
    from {
        transform: rotate(0deg);
        opacity: 1;
    }
    to {
        transform: rotate(45deg);
        opacity: 0;
    }
}

@keyframes rotate-out {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(-45deg);
    }
}

.fade-in {
    animation: fadeIn 0.2s ease-in-out;
}

.fade-out {
    animation: fadeOut 0.2s ease-in-out;
}


.rotate-in {
    animation: rotate-in 0.2s ease-in-out forwards;
}

.rotate-out {
    animation: rotate-out 0.2s ease-in-out forwards;
}
