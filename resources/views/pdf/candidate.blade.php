<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>{{ $candidate->name }} - CV</title>
    <style>
        body {
            font-family: sans-serif;
            color: #2d3748;
            background-color: #f7fafc;
            margin: 0;
            padding: 0;
        }

        .header {
            background: #4299e1;
            padding: 2rem;
            color: white;
            text-align: center;
        }

        .header-content {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .logo {
            height: 6rem;
            width: auto;
            margin-bottom: 1rem;
        }

        h1 {
            font-size: 2.25rem;
            font-weight: bold;
            margin: 0;
        }

        p {
            margin: 0.5rem 0;
        }

        .content {
            padding: 2rem;
        }

        .card {
            background-color: white;
            padding: 1.5rem;
            margin-bottom: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border-radius: 0.5rem;
        }
        .ed-card {
            break-inside: avoid; /* Prevents page break inside this element */
            page-break-inside: avoid; /* For older browsers */
            background-color: white;
            padding: 1.5rem;
            margin-bottom: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            border-radius: 0.5rem;
        }

        h2 {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
            margin-top: 0;
        }

        .experience {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .experience-item {
            break-inside: avoid; /* Prevents page break inside this element */
            page-break-inside: avoid; /* For older browsers */
            margin-bottom: 5px; /* Adds some space between experience items */
            background-color: #f7fafc;
            padding: 0.5rem;
            border: 1px solid #edf2f7;
            border-radius: 0.5rem;
        }

        .experience-item .experience-header {

            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .experience-item .experience-header .title {
            font-weight: bold;
            font-size: 1.125rem;
        }

        .experience-item .experience-header .duration {
            font-size: 0.875rem;
            color: #718096;
        }

        .experience-item .description p {
            font-size: 0.875rem;
            color: #4a5568;
            margin-bottom: 0.5rem;
        }

        .experience-item .tech {
            font-style: italic;
            color: #718096;
            font-size: 0.875rem;
        }

        .education-item .degree {
            font-weight: bold;
        }

        .education-item .duration {
            font-size: 0.875rem;
            color: #718096;
        }

        .footer {
            color: #a0aec0;
            font-size: 0.75rem;
            text-align: center;
            margin-top: 0.5rem;
        }
        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px; /* This adds space between the items */
        }

        .skill-item {
            background-color: #e0f7fa; /* Light blue background for better visibility */
            padding: 4px 6px; /* Add padding inside the skill item */
            font-size: 0.875rem;
            margin: 3px; /* Add margin around the skill item */
            border-radius: 4px; /* Rounded corners */
            display: inline-block;
            white-space: nowrap; /* Prevents line breaks within the skill item */
        }
    </style>
</head>

<body class="font-sans text-gray-800">
<div class="header">
    <div class="header-content">
        <img src="{{ public_path('assets/nordics.svg') }}" class="logo" alt="Nordics.io">
        <h1>{{ $candidate->name }}</h1>
        <p>
            @if($candidate->city) {{ $candidate->city }}, @endif
            {{ $candidate->country->trans() }}
        </p>
        <p>{{ $candidate->last_job_title }}</p>
        <p>{{ $candidate->years_of_experience }} @lang('global.labels.years_of_experience')</p>
        <p>{{ $candidate->seniority->trans() }}</p>
        <p>€{{ format_money($candidate->rateCommission, 1) }}</p>
    </div>
</div>

<div class="content">

    @if ($candidate->skills?->isNotEmpty())
        <div class="card">
            <h2>Skills</h2>
            <div class="skills-list">
                @foreach ($candidate->skills->sortByDesc('years_of_experience') as $skill)
                    <span class="skill-item">
                    {{ $skill->technology->name }}
                        @if ($skill->years_of_experience)
                            ({{ trans_choice('global.lengths.years', $skill->years_of_experience) }})
                        @endif
                </span>
                @endforeach
            </div>
        </div>
    @endif


    @if ($candidate->experiences?->isNotEmpty())
        <div class="card">
            <h2>Professional Experience</h2>

                @foreach ($candidate->experiences->sortBy('order') as $key => $value)
                <div class="experience">
                    <div class="experience-item">
                        <div class="experience-header">
                            <span class="title">{{$value->name}}</span>
                            <span class="duration">{{$value->length}} {{$value->length_type}}</span>
                        </div>
                        <div class="description">
                            <p>{{$value->description}}</p>
                        </div>
                        <div class="tech">
                            @foreach ($value->skills as $skill)
                                {{ $skill->technology->name . ($skill->years_of_experience ? ' (' . trans_choice('global.lengths.years', $skill->years_of_experience) . ')' : '') }} ·
                            @endforeach
                        </div>
                    </div>
                </div>
                @endforeach
        </div>
    @endif

    @if ($candidate->highest_education)
        <div class="ed-card">
            <h2>Education</h2>
            <div>
                <div class="education-item">
                    <div class="degree">{{ $candidate->highest_education->trans() }}</div>
                    <div>{{ $candidate->field_of_study }}</div>
                </div>
            </div>
        </div>
    @endif

    <div class="footer">&copy; 2024 Nordics.io. All rights reserved.</div>
</div>
</body>

</html>
