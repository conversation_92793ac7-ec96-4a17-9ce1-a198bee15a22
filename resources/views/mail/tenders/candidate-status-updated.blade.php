@extends('mail.layouts.robot')

@section('content')
# @lang('mail.hello')

@lang('tenders.candidate_status_updated.message', ['candidate' => $candidate->name, 'position' => $position->name, 'tender' => $tender->name, 'status' => $pivot['status']->trans()])

@component('mail::button', ['url' => $url])
@lang('tenders.candidate_status_updated.action')
@endcomponent

@lang('mail.button_url', ['url' => $url])

@endsection
