<x-auth-layout title="Password recovery">
    <h1 class="text-[32px] font-medium text-center mb-4">
        Password recovery
    </h1>

    <h3 class="text-[18px] font-normal text-[#535353] mb-6 text-center">
        {!! __("Please enter your account's e-mail below. If an acount with such e-mail exists,<br> we'll send you a link to reset your password.") !!}
    </h3>
    <form
        method="POST"
        action="{{ route('forgot-password.store') }}"
        class="grid gap-[12px] [&_input]:py-10px [&_input]:border-[1.2px] [&_input]:border-[#D1D1D6] focus:[&_input]:border-[#D1D1D6] [&_input]:rounded-[15px] [&_input]:outline-none [&_*]:transition-all [&_*]:duration-150"
    >
        @method('POST')
        @csrf
        <div>
            <input
                class="w-[500px] max-w-[90vw] placeholder:text-base placeholder:text-[#8A8A8A] h-12 px-[24px] border-[1.2px] border-[#D1D1D6]"
                type="email"
                name="email"
                placeholder="E-mail"
                autocomplete="on"
            />

            @error('email')
            <div class="text-red-500 text-xs pl-3 pt-1">{{ $message }}</div>
            @enderror
        </div>

        <div class="flex justify-around gap-[15px]">
            <a href="{{ route('login') }}"
               class="w-full py-[18px] bg-[transparent] border-[0.075rem] text-[#535353] font-medium text-[18px] mt-[10px] border-[#535353] rounded-[15px] flex justify-center items-center hover:bg-black hover:text-white transition duration-300 ease-in-out"
            >
                Back
            </a>
            <button type="submit"
                    class="nio-button w-full outline-none px-[18px] py-[18px] text-white text-[18px] mt-[10px] rounded-[15px]"
            >
                Continue
            </button>
        </div>
    </form>
</x-auth-layout>
