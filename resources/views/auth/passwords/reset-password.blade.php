<x-auth-layout title="Password recovery">
    <h1 class="text-[32px] font-medium text-center mb-4">
        Password recovery
    </h1>

    <h3 class="text-[18px] font-normal text-[#535353] mb-6">
        Email: {{ $email }}
    </h3>
    <form
        method="POST"
        action="{{ route('reset-password.store') }}"
        class="grid gap-[12px] [&_input]:py-10px [&_input]:border-[1.2px] [&_input]:border-[#D1D1D6] focus:[&_input]:border-[#D1D1D6] [&_input]:rounded-[15px] [&_input]:outline-none [&_*]:transition-all [&_*]:duration-150"
    >
        @method('POST')
        @csrf

        <input type="hidden" name="token" value="{{ request('token') }}">
        <div>
            <div class="relative w-[500px] max-w-[90vw] bg-white rounded-[15px] border-[1.2px] border-[#D1D1D6]">
                <input
                    id="userPassword"
                    class="w-full placeholder:text-base placeholder:text-[#8A8A8A] h-12 pl-[24px] pr-12 border-none outline-none"
                    type="password"
                    name="password"
                    placeholder="Password"
                    autocomplete="off"
                    required
                />
                <div
                    onclick="togglePasswordVisibility('userPassword'); togglePasswordVisibility('confirmPassword')"
                    class="absolute right-[20px] -translate-y-1/2 top-1/2 p-[3px] z-10 cursor-pointer"
                >
                    <div class="relative w-[25px] h-[25px] flex items-center justify-center">
                        <img
                            class="cursor-pointer show-icon opacity-0 transition ease-in-out delay-150 absolute"
                            src="{{ asset('svg/show-password.svg') }}"
                            alt="Show password"
                        />
                        <img
                            class="cursor-pointer hide-icon transition ease-in-out delay-150 absolute"
                            src="{{ asset('svg/hide-password.svg') }}"
                            alt="Hide password"
                        />
                    </div>
                </div>
            </div>
            @error('password')
            <div class="text-red-500 text-xs pl-3 pt-1">{{ $message }}</div>
            @enderror
        </div>
        <div>
            <div class="relative w-[500px] max-w-[90vw] bg-white rounded-[15px] border-[1.2px] border-[#D1D1D6]">
                <input
                    id="confirmPassword"
                    class="w-full placeholder:text-base placeholder:text-[#8A8A8A] h-12 pl-[24px] pr-12 border-none outline-none"
                    type="password"
                    name="password_confirmation"
                    placeholder="Confirm Password"
                    autocomplete="off"
                    required
                />
                <div
                    onclick="togglePasswordVisibility('userPassword'); togglePasswordVisibility('confirmPassword')"
                    class="absolute right-[20px] -translate-y-1/2 top-1/2 p-[3px] z-10 cursor-pointer"
                >
                    <div class="relative w-[25px] h-[25px] flex items-center justify-center">
                        <img
                            class="cursor-pointer show-icon opacity-0 transition ease-in-out delay-150 absolute"
                            src="{{ asset('svg/show-password.svg') }}"
                            alt="Show password"
                        />
                        <img
                            class="cursor-pointer hide-icon transition ease-in-out delay-150 absolute"
                            src="{{ asset('svg/hide-password.svg') }}"
                            alt="Hide password"
                        />
                    </div>
                </div>
            </div>
            @error('password_confirmation')
            <div class="text-red-500 text-xs pl-3 pt-1">{{ $message }}</div>
            @enderror
        </div>
        <div class="flex justify-around gap-[15px]">
            <button type="submit"
                    class="nio-button w-full outline-none px-[18px] py-[18px] text-white text-[18px] mt-[10px] rounded-[15px]"
            >
                Continue
            </button>
        </div>
    </form>
    @push('scripts')
        <script>
            function togglePasswordVisibility(id) {
                const inputField = document.getElementById(id);
                if (inputField) {
                    const currentType = inputField.getAttribute('type');
                    inputField.setAttribute(
                        'type',
                        currentType === 'password' ? 'text' : 'password'
                    );
                }

                const showIcon = inputField.parentNode.querySelector('.show-icon');
                const hideIcon = inputField.parentNode.querySelector('.hide-icon');
                if (showIcon && hideIcon) {
                    if (showIcon.classList.contains('opacity-0')) {
                        showIcon.classList.remove('rotate-out');
                        showIcon.classList.remove('opacity-0');

                        hideIcon.classList.add('opacity-0');
                        hideIcon.classList.add('rotate-out');
                    } else {
                        showIcon.classList.add('rotate-out');
                        showIcon.classList.add('opacity-0');

                        hideIcon.classList.remove('opacity-0');
                        hideIcon.classList.remove('rotate-out');
                    }
                }
            }
        </script>
    @endpush
</x-auth-layout>
