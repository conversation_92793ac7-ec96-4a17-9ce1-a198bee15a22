<x-auth-layout title="{{__('Authorization')}}" hideHeader=1 hideFooter=1>
    <div class="w-full h-full d-flex min-h-dvh grid justify-items-center items-center pb-20">
      
        <div class="bg-[#f4f4f5] border border-[#bfcff7] pb-4 pr-4 pl-5 pt-5  rounded-[25px] text-[#131313] justify-items-stretch grid min-h-56 max-w-screen-sm fade-in">
          
            <div>
                <div class=" mb-4">
                    <span class="text-xl">{{__('Authorization Reqeust')}}</span>
                </div>
                <p class="text-[#8a8a8a]"><span class="text-[#131313]">{{ $client->name }}</span> {{__('is requesting permission to access your account')}}.</p>
            </div>
    
           <!-- Scope List -->
           {{-- no scopes yet --}}
           {{-- @if (count($scopes) > 0)
                <div class="scopes">
                        <p><strong>This application will be able to:</strong></p>
    
                        <ul>
                            @foreach ($scopes as $scope)
                                <li>{{ $scope->description }}</li>
                            @endforeach
                        </ul>
                </div>
            @endif --}}
           
    
            <div class="flex items-end mt-auto">
                <!-- Authorize  -->
                <form class="ml-auto" method="post" action="{{ route('passport.authorizations.approve') }}">
                    @csrf
                    <input type="hidden" name="state" value="{{ $request->state }}">
                    <input type="hidden" name="client_id" value="{{ $client->getKey() }}">
                    <input type="hidden" name="auth_token" value="{{ $authToken }}">
                    <button type="submit" class="outline-none border border-[#bfcff7] px-6 py-1.5 text-[#426348] bg-[#d0f2d8] mt-[10px] rounded-[13px] ">{{__('Authorize')}}</button>
                </form>
    
                <!-- Cancel  -->
                <form class="ml-2" class="d-flex" method="post" action="{{ route('passport.authorizations.deny') }}">
                    @csrf
                    @method('DELETE')
                    <input type="hidden" name="state" value="{{ $request->state }}">
                    <input type="hidden" name="client_id" value="{{ $client->getKey() }}">
                    <input type="hidden" name="auth_token" value="{{ $authToken }}">
                    <button class="outline-none border border-[#d0d0d0] px-6 py-1.5 bg-[#f4f4f5] mt-[10px] rounded-[13px] ">{{__('Cancel')}}</button>
                </form>
            </div>
        </div>
    </div>
    </x-auth-layout>