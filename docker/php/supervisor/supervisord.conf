[supervisord]
user=www-data
nodaemon=true
minfds=10000
logfile=/dev/null
logfile_maxbytes=0
pidfile=/run/supervisord.pid

[inet_http_server]
port = 127.0.0.1:9001

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=http://127.0.0.1:9001

[program:server]
command=php-fpm -d memory_limit=1G
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
startsecs=1
startretries=300
stopsignal=INT
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0

[program:queue]
process_name=%(program_name)s_%(process_num)02d
command=php /opt/nio/api/artisan queue:work --sleep=3 --tries=3 --max-time=3600
numprocs=2
autostart=true
autorestart=true
stopasgroup=true
killasgroup=true
startsecs=1
startretries=300
stopsignal=INT
stdout_logfile=/dev/stdout
stdout_logfile_maxbytes=0
stderr_logfile=/dev/stderr
stderr_logfile_maxbytes=0
