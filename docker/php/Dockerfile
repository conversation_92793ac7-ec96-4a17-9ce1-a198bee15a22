ARG PHP_VERSION=8.4

FROM php:${PHP_VERSION}-fpm-alpine

# Install dependencies
RUN apk add --update --no-cache \
    linux-headers \
    libxml2-dev \
    libsodium-dev \
    icu-dev \
    oniguruma-dev \
    libzip-dev \
    freetype-dev \
    libjpeg-turbo-dev \
    libpng-dev \
    openssl-dev \
    curl-dev \
    unzip \
    pcre-dev $PHPIZE_DEPS \
    jq \
    git \
    nano \
    npm \
    mysql-client \
    imagemagick-dev \
    tzdata \
    supervisor


#RUN python3 -m venv /opt/venv
#ENV PATH="/opt/venv/bin:$PATH" \
#    PIP_DISABLE_PIP_VERSION_CHECK=1 PYTHONDONTWRITEBYTECODE=1 PYTHONUNBUFFERED=1

# Install extensions
RUN docker-php-ext-install -j$(nproc) opcache
RUN docker-php-ext-install -j$(nproc) pdo pdo_mysql
RUN docker-php-ext-install -j$(nproc) curl
RUN docker-php-ext-install -j$(nproc) intl
RUN docker-php-ext-install -j$(nproc) mbstring
RUN docker-php-ext-install -j$(nproc) xml
RUN docker-php-ext-install -j$(nproc) exif
RUN docker-php-ext-install -j$(nproc) pcntl
RUN docker-php-ext-install -j$(nproc) sockets
RUN docker-php-ext-install -j$(nproc) zip
RUN docker-php-ext-install -j$(nproc) bcmath
RUN docker-php-ext-install -j$(nproc) sodium
RUN docker-php-ext-install fileinfo

# Install & Enable: GD
RUN docker-php-ext-install -j$(nproc) gd
RUN docker-php-ext-configure gd --with-jpeg --with-freetype

# Install & Enable: XDebug
RUN pecl install xdebug \
    && docker-php-ext-enable xdebug

# Install & Enable: Imagick
RUN apk add git --update --no-cache && \
    git clone https://github.com/Imagick/imagick.git --depth=1 --single-branch --branch develop /tmp/imagick && \
    cd /tmp/imagick && \
    phpize && \
    ./configure && \
    make && \
    make install && \
    docker-php-ext-enable imagick

# Install & Enable: Redis
RUN MAKEFLAGS="-j $(nproc)" pecl install -o -f redis \
    && docker-php-ext-enable redis

# Clear
RUN apk del pcre-dev ${PHPIZE_DEPS}
RUN rm -rf /tmp/pear

# Activate Shell
SHELL ["/bin/sh", "-c"]

# Install dockerize tool
ENV DOCKERIZE_VERSION v0.9.3
RUN wget -O - https://github.com/jwilder/dockerize/releases/download/$DOCKERIZE_VERSION/dockerize-linux-amd64-$DOCKERIZE_VERSION.tar.gz | tar xzf - -C /usr/local/bin

# Install composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer
RUN mkdir -p /var/www/.composer

# Supervisor
RUN touch /run/supervisord.pid
RUN chown -R www-data:www-data /run/supervisord.pid

# TODO: Optimize.
# Python deps for file upload extraction.
RUN apk add --no-cache  \
    python3 \
    py3-pip \
    py3-virtualenv \
    libffi-dev \
    libstdc++  \
    clang \
    clang20-libclang \
    && ln -sf python3 /usr/bin/python

RUN python3 -m venv /opt/venv \
    && CLANG_SO="$(find /usr -maxdepth 4 -name 'libclang.so*' | head -n1 || true)" \
    && if [ -n "$CLANG_SO" ] && [ ! -e /usr/lib/libclang.so ]; then ln -s "$CLANG_SO" /usr/lib/libclang.so; fi

ENV PATH="/opt/venv/bin:$PATH"

# Set working directory
WORKDIR /opt/nio/api

# TODO: try removing rust & cargo.
COPY requirements.txt .
RUN apk add --no-cache --virtual .py-build-deps \
        build-base \
        python3-dev \
        musl-dev \
        rust \
        cargo \
      && pip install --no-cache-dir -r requirements.txt \
      && apk del .py-build-deps

RUN chown -R www-data:www-data /opt/nio/api
RUN chown -R www-data:www-data /var/www

# User
USER www-data

EXPOSE 9000/tcp

ENTRYPOINT ["./docker-entrypoint.sh"]
