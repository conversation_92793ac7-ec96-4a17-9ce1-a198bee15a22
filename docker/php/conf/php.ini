upload_max_filesize = 100M
post_max_size = 100M
always_populate_raw_post_data = -1
max_input_vars = 5000
expose_php = 0
variables_order = "GPCS"

[Date]
date.timezone = "Europe/Bratislava"

[Opcache]
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 256M
opcache.use_cwd = 1
opcache.max_file_size = 0
opcache.max_accelerated_files = 32531
opcache.validate_timestamps = 1
opcache.revalidate_freq = 2

[JIT]
; Disable JIT as it cannot be used with Xdebug enabled.
opcache.jit = ${PHP_JIT_MODE}
opcache.jit_buffer_size = 100M

[PHP-FPM]
user = www-data
group = www-data
listen = 9000
listen.allowed_clients = 127.0.0.1

display_errors = On
display_startup_errors = On
error_reporting = E_ALL
log_errors = On
error_log = /var/log/php_errors.log

memory_limit = 512M
max_execution_time = 300

[xdebug]
; Commented out as it is enabled in the Dockerfile.
;zend_extension=xdebug.so
xdebug.mode=${PHP_XDEBUG_MODE}
xdebug.start_with_request=yes
xdebug.client_host=host.docker.internal
xdebug.client_port=9003
xdebug.log=/tmp/xdebug.log
