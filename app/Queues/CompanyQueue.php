<?php

declare(strict_types=1);

namespace App\Queues;

use ReflectionClass;

class CompanyQueue extends BaseQueue
{
    /**
     * @param object $job
     * @throws \ReflectionException
     */
    protected function createPayloadArray($job, $queue, $data = ''): array
    {
        $reflection = new ReflectionClass($job);

        $parameters = $reflection->getConstructor()->getParameters();

        $payload = [];

        foreach ($parameters as $parameter) {
            $payload += [
                $parameter->name => $job->{$parameter->name},
            ];
        }

        return $payload;
    }
}
