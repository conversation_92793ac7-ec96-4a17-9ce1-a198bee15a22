<?php

namespace App\Providers;

use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\ServiceProvider;

class CollectionMacroProvider extends ServiceProvider
{
    public function boot(): void
    {
        Collection::macro('toJsonUtf8', function () {
            /* @var $this Collection */
            return $this->toJson(JSON_UNESCAPED_UNICODE);
        });

        Collection::macro('same', function (Collection $other) {
            /* @var $this Collection */
            return $this->diff($other)->isEmpty()
                && $other->diff($this)->isEmpty();
        });

        Collection::macro('different', function (Collection $other) {
            /* @var $this Collection */
            return ! $this->same($other);
        });

        Collection::macro('sameAssoc', function (Collection $other) {
            /* @var $this Collection */
            return $this->diffAssoc($other)->isEmpty()
                && $other->diffAssoc($this)->isEmpty();
        });

        Collection::macro('differentAssoc', function (Collection $other) {
            /* @var $this Collection */
            return ! $this->sameAssoc($other);
        });

        Collection::macro('isAssoc', function () {
            /* @var $this Collection */
            return Arr::isAssoc($this->take(1)->toArray());
        });

        Collection::macro(
            'recursive',
            function (): Collection {
                /* @var $this Collection */
                return $this->map(
                    static function ($value) {
                        if (is_array($value) || is_object($value)) {
                            return Collection::make($value)->recursive();
                        }

                        return $value;
                    }
                );
            }
        );
    }
}
