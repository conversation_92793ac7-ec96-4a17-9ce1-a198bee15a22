<?php

declare(strict_types=1);

namespace App\Providers;

use App\Services\Enterprise\AssistantApiService;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Support\ServiceProvider;

class AssistantApiServiceProvider extends ServiceProvider implements DeferrableProvider
{
    public function register(): void
    {
        $this->app->singleton(
            AssistantApiService::class,
            fn () => new AssistantApiService(
                config('services.assistant_api.url'),
                config('services.assistant_api.key'),
            )
        );
    }

    /**
     * @return string[]
     */
    public function provides(): array
    {
        return [AssistantApiService::class];
    }
}
