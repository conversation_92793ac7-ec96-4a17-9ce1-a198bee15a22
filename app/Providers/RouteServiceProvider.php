<?php

namespace App\Providers;

use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Http\Request;
use Illuminate\Routing\Middleware\ThrottleRequests;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;
use App\Enums\UserRole;

class RouteServiceProvider extends ServiceProvider
{
    /**
     * Define your route model bindings, pattern filters, etc.
     */
    public function boot(): void
    {
        $this->configureRateLimiting();

        $this->registerRouteFile('routes/auth.php', $this->noMiddlewares());
        $this->registerRouteFile('routes/guest.php', 'guest');
        $this->registerRouteFile('routes/downloadable.php', ['downloadable']);

        $this->routes(function () {
            Route::middleware(['auth:api'])->group(function () {
                $this->registerRouteFile('routes/profile.php');

                Route::middleware([UserRole::Vendor->middleware()])->group(function () {
                    $this->registerRouteFile('routes/tenders.php');
                    $this->registerRouteFile('routes/company.php');
                    $this->registerRouteFile('routes/vendors.php');
                    $this->registerRouteFile('routes/solutions.php');
                    $this->registerRouteFile('routes/bench.php');
                });

                Route::middleware([UserRole::Client->middleware()])->group(function () {
                    $this->registerRouteFile('routes/enterprise.php');
                });

                Route::middleware([UserRole::SuperAdmin->middleware()])->group(function () {
                    $this->registerRouteFile('routes/admin.php');
                });
            });

            $this->registerRouteFile('routes/generic.php', $this->noMiddlewares());
        })->middleware(ThrottleRequests::using('api'));
    }

    /**
     * Configure the rate limiters for the application.
     */
    protected function configureRateLimiting(): void
    {
        RateLimiter::for('api', function (Request $request) {
            return Limit::perMinute(60)->by($request->user()?->id ?: $request->ip());
        });
    }

    /**
     * Registers router file as a group. If not specified otherwise,
     * automatically applies "auth" middleware to the whole group.
     */
    private function registerRouteFile(string $path, array|string|null $middleware = 'auth'): void
    {
        $builder = Route::namespace('');

        if ($middleware) {
            $builder->middleware($middleware);
        }

        $builder->group(base_path($path));
    }

    /**
     * Syntactic sugar for route building
     */
    private function noMiddlewares(): null
    {
        return null;
    }
}
