<?php

namespace App\Providers;

use Carbon\CarbonImmutable;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\ServiceProvider;
use Symfony\Component\HtmlSanitizer\HtmlSanitizer;
use Symfony\Component\HtmlSanitizer\HtmlSanitizerConfig;
use Laravel\Passport\Passport;
use App\Enums\UserRole;
use App\Models\User;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->registerSanitizer();
    }

    public function boot(): void
    {
        $this->configureCommands();
        $this->configureModels();
        $this->configureRoutes();
        $this->configureUrl();
        $this->configureDates();
        $this->configureSuperadmin();
    }

    /**
     * Registers HTML sanitizer with default configuration
     */
    private function registerSanitizer(): void
    {
        $this->app->singleton('sanitizer.default', function () {
            $config = (new HtmlSanitizerConfig)->allowSafeElements();

            return new HtmlSanitizer($config);
        });
    }

    private function configureCommands(): void
    {
        \DB::prohibitDestructiveCommands(
            $this->app->isProduction(),
        );
    }

    private function configureModels(): void
    {
        Schema::defaultStringLength(191);
    }

    private function configureRoutes(): void
    {
        Passport::ignoreRoutes();
    }

    private function configureUrl(): void
    {
        if (App::runningInConsole()) {
            URL::forceRootUrl(config('app.url'));
        }

        $environment = App::environment();
        if (in_array($environment, ['production', 'staging'], true)) {
            URL::forceHttps();
        }
    }

    private function configureDates(): void
    {
        Date::use(CarbonImmutable::class);
    }

    private function configureSuperadmin(): void
    {
        Gate::before(function (User $user, $ability): ?bool {
            return UserRole::SuperAdmin->has($user) ? true : null;
        });
    }
}
