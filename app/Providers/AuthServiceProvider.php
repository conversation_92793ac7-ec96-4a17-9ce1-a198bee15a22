<?php

namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Laravel\Passport\Passport;
use App\Models\Passport\Client;
use App\Models\Rfp;
use App\Policies\RfpPolicy;


class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
        Rfp::class => RfpPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        Passport::useClientModel(Client::class);
        // dostupne scopes
        Passport::tokensCan([
            'enterprise' => 'Access enterprise API',
        ]);

        // defaults
        Passport::setDefaultScope([
            'enterprise',
        ]);

       
    }
}
