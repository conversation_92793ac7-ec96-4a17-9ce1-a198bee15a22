<?php

namespace App\Providers;

use App\Models\BenchSpecialist;
use App\Models\Candidate;
use App\Models\CandidateExperience;
use App\Models\Company;
use App\Models\Solution;
use App\Models\SolutionMedium;
use App\Models\Tender;
use App\Models\TenderPosition;
use App\Models\Vendor;
use App\Observers\PublicIdObserver;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        Tender::observe(PublicIdObserver::class);
        Vendor::observe(PublicIdObserver::class);
        Company::observe(PublicIdObserver::class);
        Solution::observe(PublicIdObserver::class);
        Candidate::observe(PublicIdObserver::class);
        TenderPosition::observe(PublicIdObserver::class);
        SolutionMedium::observe(PublicIdObserver::class);
        BenchSpecialist::observe(PublicIdObserver::class);
        CandidateExperience::observe(PublicIdObserver::class);
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
