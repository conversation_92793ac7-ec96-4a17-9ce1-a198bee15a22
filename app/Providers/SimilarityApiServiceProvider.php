<?php

declare(strict_types=1);

namespace App\Providers;

use App\Services\Enterprise\SimilarityApiService;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Support\ServiceProvider;

class SimilarityApiServiceProvider extends ServiceProvider implements DeferrableProvider
{
    public function register(): void
    {
        $this->app->singleton(
            SimilarityApiService::class,
            fn () => new SimilarityApiService(config('services.similarity_api.url'))
        );
    }

    /**
     * @return string[]
     */
    public function provides(): array
    {
        return [SimilarityApiService::class];
    }
}
