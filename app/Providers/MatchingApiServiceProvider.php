<?php

declare(strict_types=1);

namespace App\Providers;

use App\Services\Enterprise\MatchingApiService;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Support\ServiceProvider;

class MatchingApiServiceProvider extends ServiceProvider implements DeferrableProvider
{
    public function register(): void
    {
        $this->app->singleton(
            MatchingApiService::class,
            fn () => new MatchingApiService(
                config('services.matching_api.url'),
                config('services.matching_api.key')
            )
        );
    }

    /**
     * @return string[]
     */
    public function provides(): array
    {
        return [MatchingApiService::class];
    }
}