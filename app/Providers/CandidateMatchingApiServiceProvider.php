<?php

declare(strict_types=1);

namespace App\Providers;

use App\Services\Enterprise\CandidateMatchingApiService;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Support\ServiceProvider;

class CandidateMatchingApiServiceProvider extends ServiceProvider implements DeferrableProvider
{
    public function register(): void
    {
        $this->app->singleton(
            CandidateMatchingApiService::class,
            fn () => new CandidateMatchingApiService(
                config('services.candidate_matching_api.url'),
                config('services.candidate_matching_api.key')
            )
        );
    }

    /**
     * @return string[]
     */
    public function provides(): array
    {
        return [CandidateMatchingApiService::class];
    }
}
