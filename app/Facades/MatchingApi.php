<?php

declare(strict_types=1);

namespace App\Facades;

use App\Enums\Enterprise\TenderMatchingCompaniesFilter;
use App\Models\Tender;
use App\Services\Enterprise\MatchingApiService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Facade;

/**
 * @method static array requestMatches(Collection $payload, TenderMatchingCompaniesFilter $companiesFilter)
 * @method static Collection createPayload(Tender $tender, TenderMatchingCompaniesFilter $filter, array $companyUuids)
 */
class MatchingApi extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return MatchingApiService::class;
    }
}
