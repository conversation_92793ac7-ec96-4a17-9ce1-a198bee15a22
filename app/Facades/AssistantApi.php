<?php

declare(strict_types=1);

namespace App\Facades;

use App\Services\Enterprise\AssistantApiService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Facade;

/**
 * @method static Collection extractRfp(string $rfpText)
 * @method static array extractResources(string $userText, string $description)
 * @method static array suggestResources(string $userText, string $description, array $existingResources)
 * @method static ?string description (string $userText, string $description)
 */
class Assistant<PERSON>pi extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return AssistantApiService::class;
    }
}
