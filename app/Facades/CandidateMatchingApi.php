<?php

declare(strict_types=1);

namespace App\Facades;

use App\Models\Candidate;
use App\Models\TenderPosition;
use App\Services\Enterprise\CandidateMatchingApiService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Facade;

/**
 * @method static array requestMatch(Collection $payload)
 * @method static Collection createPayload(TenderPosition $tenderPosition, Candidate $candidate)
 */
class CandidateMatchingApi extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return CandidateMatchingApiService::class;
    }
}
