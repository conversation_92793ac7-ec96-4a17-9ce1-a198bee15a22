<?php

declare(strict_types=1);

namespace App\Facades;

use App\Enums\Enterprise\SimilarityApiCollection;
use App\Services\Enterprise\SimilarityApiService;
use Illuminate\Support\Facades\Facade;

/**
 * @method static array search(array $texts, SimilarityApiCollection $collectionName)
 */
class SimilarityApi extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return SimilarityApiService::class;
    }
}
