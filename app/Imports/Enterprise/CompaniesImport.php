<?php

namespace App\Imports\Enterprise;

use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;

class CompaniesImport implements ToCollection
{
    protected $companies;

    public function __construct()
    {
        $this->companies = [];
    }

    public function collection(Collection $rows): void
    {
        foreach ($rows as $row) {
            $companyName = $row[0];

            if ($companyName) {
                $this->companies[] = $companyName;
            }
        }
    }

    // return array of names of vendors
    public function getCompanyNames(): array
    {
        return $this->companies;
    }
}
