<?php

namespace App\Enums;

enum TenderStatus: string
{
    use RichEnumTrait;

    case Incubation = 'incubation';
    case Open = 'open';
    case Reviewing = 'reviewing';
    case Ended = 'ended';

    public static function completedArray(): array
    {
        return [self::Reviewing->value, self::Ended->value];
    }

    public static function notCompletedArray(): array
    {
        return [self::Incubation->value, self::Open->value];
    }

    public function isCompleted(): bool
    {
        return in_array($this->value, self::completedArray());
    }

    public function isNotCompleted(): bool
    {
        return in_array($this->value, self::notCompletedArray());
    }
}
