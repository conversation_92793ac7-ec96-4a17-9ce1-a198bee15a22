<?php

namespace App\Enums;

enum TenderCandidateStatus: string implements HumanReadable
{
    use RichEnumTrait;

    case AwaitingApproval = 'awaiting_approval';
    case Rejected = 'rejected';
    case Approved = 'approved';
    case NotInterested = 'not_interested';
    case Interested = 'interested';
    case InvitedToInterview = 'invited_to_interview';
    case InterviewedNotInterested = 'interviewed_not_interested';
    case InterviewedNotHired = 'interviewed_not_hired';
    case InterviewedInterested = 'interviewed_interested';
    case Hired = 'hired';
    case NotHired = 'not_hired';

    public function trans(): string
    {
        return __("tenders.candidate_states.$this->value");
    }

    public static function protectedArray(): array
    {
        return [
            self::AwaitingApproval->value,
            self::Rejected->value,
            self::Approved->value,
        ];
    }

    public static function unrestrictedArray(): array
    {
        return [
            self::NotInterested->value,
            self::Interested->value,
            self::InvitedToInterview->value,
            self::InterviewedNotInterested->value,
            self::InterviewedNotHired->value,
            self::InterviewedInterested->value,
            self::Hired->value,
            self::NotHired->value,
        ];
    }

    public static function rejectedArray(): array
    {
        return [
            self::Rejected->value,
            self::NotInterested->value,
            self::InterviewedNotInterested->value,
            self::InterviewedNotHired->value,
            self::NotHired->value,
        ];
    }

    public static function rejectedWithMandatoryReasonArray(): array
    {
        return [
            self::Rejected->value,
            self::InterviewedNotHired->value,
            self::NotHired->value,
        ];
    }

    public static function unrestrictedValueList(string $separator = ', '): string
    {
        return implode($separator, self::unrestrictedArray());
    }

    public static function inRuleUnrestricted(): string
    {
        return 'in:'.self::unrestrictedValueList(',');
    }

    public static function rejectedValueList(string $separator = ', '): string
    {
        return implode($separator, self::rejectedArray());
    }

    public static function rejectedWithMandatoryReasonValueList(string $separator = ', '): string
    {
        return implode($separator, self::rejectedWithMandatoryReasonArray());
    }

    public function nextStates(): array
    {
        return match ($this) {
            self::AwaitingApproval => [self::Rejected, self::Approved],
            self::Rejected => [],
            self::Approved => [self::Interested, self::NotInterested],
            self::NotInterested => [self::Interested],
            self::Interested => [self::NotInterested, self::InvitedToInterview],
            self::InvitedToInterview => [self::InterviewedInterested, self::InterviewedNotInterested],
            self::InterviewedNotInterested => [self::InterviewedInterested, self::InterviewedNotHired],
            self::InterviewedNotHired => [],
            self::InterviewedInterested => [self::InterviewedNotInterested, self::Hired, self::NotHired],
            self::Hired => [],
            self::NotHired => [],
        };
    }

    public function isNextStatus(self $status): bool
    {
        return in_array($status, self::nextStates());
    }

    public function isProtected(): bool
    {
        return in_array($this->value, self::protectedArray());
    }

    public function isUnrestricted(): bool
    {
        return in_array($this->value, self::unrestrictedArray());
    }

    public function isRejected(): bool
    {
        return in_array($this->value, self::rejectedArray());
    }

    public function isRejectedWithMandatoryReason(): bool
    {
        return in_array($this->value, self::rejectedWithMandatoryReasonArray());
    }
}
