<?php

namespace App\Enums;

use App\Models\User;

enum Platform: string
{
    case Client = 'client';
    case Vendor = 'vendor';

    public function registerUrl(string $url): ?string
    {
        return match ($this) {
            self::Vendor => $url . '/registration',
            default => null
        };
    }

    public function userCanAccess(User $user): bool
    {
        if ($user->hasRole(UserRole::SuperAdmin)) {
            return true;
        }
        return match ($this) {
            self::Client => $user->hasRole(UserRole::Client),
            self::Vendor => $user->hasRole(UserRole::Vendor),
        };
    }
}
