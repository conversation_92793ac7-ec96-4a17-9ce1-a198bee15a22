<?php

namespace App\Enums;

use App\Drivers\Solutions\Media\ImageMediumDriver;
use App\Drivers\Solutions\Media\MediumDriver;
use App\Drivers\Solutions\Media\StreamableMediumDriver;
use App\Drivers\Solutions\Media\YouTubeMediumDriver;
use App\Rules\StreamableVideoId;
use App\Rules\YouTubeVideoId;

enum SolutionMediumType: string
{
    use RichEnumTrait;

    case Image = 'image';
    case YouTubeVideo = 'video_yt';
    case StreamableVideo = 'video_smbl';

    public function driverClass(): string
    {
        return match ($this) {
            self::Image => ImageMediumDriver::class,
            self::YouTubeVideo => YouTubeMediumDriver::class,
            self::StreamableVideo => StreamableMediumDriver::class,
        };
    }

    public function driver(): MediumDriver
    {
        return app($this->driverClass());
    }

    public function sourceRules(): string|array
    {
        return match ($this) {
            self::Image => 'required|string|exists:resources,public_id,deleted_at,NULL,type,solution_medium_image',
            self::YouTubeVideo => ['required', 'string', 'size:11', 'alpha_dash', new YouTubeVideoId],
            self::StreamableVideo => ['required', 'string', 'between:4,8', 'alpha_num', new StreamableVideoId],
        };
    }
}
