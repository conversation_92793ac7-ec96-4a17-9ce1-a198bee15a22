<?php

namespace App\Enums;

use Countable;

enum LengthType: string implements HumanReadableChoice
{
    use RichEnumTrait;

    case Unspecified = 'unspecified';
    case Months = 'months';
    case Years = 'years';
    case Ongoing = 'ongoing';

    public function transChoice(Countable|int|array|null $number): string
    {
        return trans_choice("global.lengths.$this->value", $number);
    }

    public static function specificArray(): array
    {
        return [self::Months->value, self::Years->value];
    }

    public static function specificValueList(string $separator = ', '): string
    {
        return implode($separator, self::specificArray());
    }

    public static function inRuleSpecific(): string
    {
        return 'in:'.self::specificValueList(',');
    }

    public static function finiteArray(): array
    {
        return [self::Unspecified->value, self::Months->value, self::Years->value];
    }

    public static function finiteValueList(string $separator = ', '): string
    {
        return implode($separator, self::specificArray());
    }

    public static function inRuleFinite(): string
    {
        return 'in:'.self::finiteValueList(',');
    }
}
