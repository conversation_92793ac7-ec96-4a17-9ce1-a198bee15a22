<?php

namespace App\Enums;

use App\Models\Company;
use App\Models\User;
use Spatie\Permission\Models\Role;
use Illuminate\Contracts\Auth\Authenticatable;

enum UserRole: string
{
    use RichEnumTrait;

    case SuperAdmin = 'SuperAdmin';
    case Client = 'Client';
    case Vendor = 'Vendor';

    public function middleware(): string
    {
        return 'role:' . $this->value;
    }

    public function has(Authenticatable|User|null $user = null): bool
    {
        $user ??= auth()->user();

        return (bool) $user?->hasRole(
            $this->value
        );
    }

    public function model(): Role
    {
        return Role::findByName($this->value);
    }

    public static function allowedRoles(?Company $company = null): array
    {
        if ($company && $company->is_vendor) {
            return [UserRole::Vendor];
        } else {
            return [
                UserRole::Client,
                UserRole::Vendor
            ];
        }
    }
}
