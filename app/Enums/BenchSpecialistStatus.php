<?php

namespace App\Enums;

enum BenchSpecialistStatus: string
{
    use RichEnumTrait;

    case InvitedToInterview = 'invited_to_interview';
    case InterviewedAwaitingEvaluation = 'interviewed_awaiting_evaluation';
    case InterviewedInterested = 'interviewed_interested';
    case Hired = 'hired';
    case NotHired = 'not_hired';
    case DeclinedOffer = 'declined_offer';

    public function nextStates(): array
    {
        return match ($this) {
            self::InvitedToInterview => [self::InterviewedAwaitingEvaluation],
            self::InterviewedAwaitingEvaluation => [self::InterviewedInterested, self::NotHired],
            self::InterviewedInterested => [self::Hired, self::NotHired],
            self::Hired => [],
            self::NotHired => [],
            self::DeclinedOffer => [],
        };
    }

    public function isNextStatus(self $status): bool
    {
        return in_array($status, self::nextStates());
    }
}
