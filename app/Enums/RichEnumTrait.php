<?php

namespace App\Enums;

/**
 * Extension methods for enums
 */
trait RichEnumTrait
{
    /**
     * Returns enum values as an array.
     *
     * @return array<string>
     */
    public static function valueArray(): array
    {
        $values = [];

        foreach (self::cases() as $enum) {
            $values[] = $enum->value ?? $enum->name;
        }

        return $values;
    }

    /**
     * Returns enum values as a list.
     */
    public static function valueList(string $separator = ', '): string
    {
        return implode($separator, self::valueArray());
    }

    /**
     * Returns in rule for validator with all values of this enum.
     */
    public static function inRule(): string
    {
        return 'in:'.self::valueList(',');
    }

    /**
     * Casts entire array of string values to array of enums
     *
     * @return array<self>
     */
    public static function fromArray(array $values): array
    {
        return collect($values)->map(fn ($value) => static::from($value))->toArray();
    }
}
