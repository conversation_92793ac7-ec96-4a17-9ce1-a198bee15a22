<?php

namespace App\Enums;

enum Country: string implements HumanReadable
{
    use RichEnumTrait;

    case Afghanistan = 'af';
    case AlandIslands = 'ax';
    case Albania = 'al';
    case Algeria = 'dz';
    case AmericanSamoa = 'as';
    case Andorra = 'ad';
    case Angola = 'ao';
    case Anguilla = 'ai';
    case Antarctica = 'aq';
    case AntiguaAndBarbuda = 'ag';
    case Argentina = 'ar';
    case Armenia = 'am';
    case Aruba = 'aw';
    case Australia = 'au';
    case Austria = 'at';
    case Azerbaijan = 'az';
    case Bahamas = 'bs';
    case Bahrain = 'bh';
    case Bangladesh = 'bd';
    case Barbados = 'bb';
    case Belarus = 'by';
    case Belgium = 'be';
    case Belize = 'bz';
    case Benin = 'bj';
    case Bermuda = 'bm';
    case Bhutan = 'bt';
    case Bolivia = 'bo';
    case BosniaAndHerzegovina = 'ba';
    case Botswana = 'bw';
    case BouvetIsland = 'bv';
    case Brazil = 'br';
    case BritishIndianOceanTerritory = 'io';
    case BritishVirginIslands = 'vg';
    case Brunei = 'bn';
    case Bulgaria = 'bg';
    case BurkinaFaso = 'bf';
    case Burundi = 'bi';
    case Cambodia = 'kh';
    case Cameroon = 'cm';
    case Canada = 'ca';
    case CapeVerde = 'cv';
    case CaribbeanNetherlands = 'bq';
    case CaymanIslands = 'ky';
    case CentralAfricanRepublic = 'cf';
    case Chad = 'td';
    case Chile = 'cl';
    case China = 'cn';
    case ChristmasIsland = 'cx';
    case CocosIslands = 'cc';
    case Colombia = 'co';
    case Comoros = 'km';
    case CongoBrazzaville = 'cg';
    case CongoKinshasa = 'cd';
    case CookIslands = 'ck';
    case CostaRica = 'cr';
    case CoteDIvoire = 'ci';
    case Croatia = 'hr';
    case Cuba = 'cu';
    case Curacao = 'cw';
    case Cyprus = 'cy';
    case Czechia = 'cz';
    case Denmark = 'dk';
    case Djibouti = 'dj';
    case Dominica = 'dm';
    case DominicanRepublic = 'do';
    case Ecuador = 'ec';
    case Egypt = 'eg';
    case ElSalvador = 'sv';
    case EquatorialGuinea = 'gq';
    case Eritrea = 'er';
    case Estonia = 'ee';
    case Eswatini = 'sz';
    case Ethiopia = 'et';
    case FalklandIslands = 'fk';
    case FaroeIslands = 'fo';
    case Fiji = 'fj';
    case Finland = 'fi';
    case France = 'fr';
    case FrenchGuiana = 'gf';
    case FrenchPolynesia = 'pf';
    case FrenchSouthernTerritories = 'tf';
    case Gabon = 'ga';
    case Gambia = 'gm';
    case Georgia = 'ge';
    case Germany = 'de';
    case Ghana = 'gh';
    case Gibraltar = 'gi';
    case Greece = 'gr';
    case Greenland = 'gl';
    case Grenada = 'gd';
    case Guadeloupe = 'gp';
    case Guam = 'gu';
    case Guatemala = 'gt';
    case Guernsey = 'gg';
    case Guinea = 'gn';
    case GuineaBissau = 'gw';
    case Guyana = 'gy';
    case Haiti = 'ht';
    case HeardAndMcDonaldIslands = 'hm';
    case Honduras = 'hn';
    case HongKong = 'hk';
    case Hungary = 'hu';
    case Iceland = 'is';
    case India = 'in';
    case Indonesia = 'id';
    case Iran = 'ir';
    case Iraq = 'iq';
    case Ireland = 'ie';
    case IsleofMan = 'im';
    case Israel = 'il';
    case Italy = 'it';
    case Jamaica = 'jm';
    case Japan = 'jp';
    case Jersey = 'je';
    case Jordan = 'jo';
    case Kazakhstan = 'kz';
    case Kenya = 'ke';
    case Kiribati = 'ki';
    case Kosovo = 'xk';
    case Kuwait = 'kw';
    case Kyrgyzstan = 'kg';
    case Laos = 'la';
    case Latvia = 'lv';
    case Lebanon = 'lb';
    case Lesotho = 'ls';
    case Liberia = 'lr';
    case Libya = 'ly';
    case Liechtenstein = 'li';
    case Lithuania = 'lt';
    case Luxembourg = 'lu';
    case Macao = 'mo';
    case Madagascar = 'mg';
    case Malawi = 'mw';
    case Malaysia = 'my';
    case Maldives = 'mv';
    case Mali = 'ml';
    case Malta = 'mt';
    case MarshallIslands = 'mh';
    case Martinique = 'mq';
    case Mauritania = 'mr';
    case Mauritius = 'mu';
    case Mayotte = 'yt';
    case Mexico = 'mx';
    case Micronesia = 'fm';
    case Moldova = 'md';
    case Monaco = 'mc';
    case Mongolia = 'mn';
    case Montenegro = 'me';
    case Montserrat = 'ms';
    case Morocco = 'ma';
    case Mozambique = 'mz';
    case Myanmar = 'mm';
    case Namibia = 'na';
    case Nauru = 'nr';
    case Nepal = 'np';
    case Netherlands = 'nl';
    case NewCaledonia = 'nc';
    case NewZealand = 'nz';
    case Nicaragua = 'ni';
    case Niger = 'ne';
    case Nigeria = 'ng';
    case Niue = 'nu';
    case NorfolkIsland = 'nf';
    case NorthKorea = 'kp';
    case NorthMacedonia = 'mk';
    case NorthernMarianaIslands = 'mp';
    case Norway = 'no';
    case Oman = 'om';
    case Pakistan = 'pk';
    case Palau = 'pw';
    case PalestinianTerritories = 'ps';
    case Panama = 'pa';
    case PapuaNewGuinea = 'pg';
    case Paraguay = 'py';
    case Peru = 'pe';
    case Philippines = 'ph';
    case PitcairnIslands = 'pn';
    case Poland = 'pl';
    case Portugal = 'pt';
    case PuertoRico = 'pr';
    case Qatar = 'qa';
    case Reunion = 're';
    case Romania = 'ro';
    case Russia = 'ru';
    case Rwanda = 'rw';
    case Samoa = 'ws';
    case SanMarino = 'sm';
    case SaoTomeAndPrincipe = 'st';
    case SaudiArabia = 'sa';
    case Senegal = 'sn';
    case Serbia = 'rs';
    case Seychelles = 'sc';
    case SierraLeone = 'sl';
    case Singapore = 'sg';
    case SintMaarten = 'sx';
    case Slovakia = 'sk';
    case Slovenia = 'si';
    case SolomonIslands = 'sb';
    case Somalia = 'so';
    case SouthAfrica = 'za';
    case SouthGeorgiaAndSouthSandwichIslands = 'gs';
    case SouthKorea = 'kr';
    case SouthSudan = 'ss';
    case Spain = 'es';
    case SriLanka = 'lk';
    case StBarthelemy = 'bl';
    case StHelena = 'sh';
    case StKittsAndNevis = 'kn';
    case StLucia = 'lc';
    case StMartin = 'mf';
    case StPierreAndMiquelon = 'pm';
    case StVincentAndGrenadines = 'vc';
    case Sudan = 'sd';
    case Suriname = 'sr';
    case SvalbardAndJanMayen = 'sj';
    case Sweden = 'se';
    case Switzerland = 'ch';
    case Syria = 'sy';
    case Taiwan = 'tw';
    case Tajikistan = 'tj';
    case Tanzania = 'tz';
    case Thailand = 'th';
    case TimorLeste = 'tl';
    case Togo = 'tg';
    case Tokelau = 'tk';
    case Tonga = 'to';
    case TrinidadAndTobago = 'tt';
    case Tunisia = 'tn';
    case Turkey = 'tr';
    case Turkmenistan = 'tm';
    case TurksAndCaicosIslands = 'tc';
    case Tuvalu = 'tv';
    case USOutlyingIslands = 'um';
    case USVirginIslands = 'vi';
    case Uganda = 'ug';
    case Ukraine = 'ua';
    case UnitedArabEmirates = 'ae';
    case UnitedKingdom = 'gb';
    case UnitedStates = 'us';
    case Uruguay = 'uy';
    case Uzbekistan = 'uz';
    case Vanuatu = 'vu';
    case VaticanCity = 'va';
    case Venezuela = 've';
    case Vietnam = 'vn';
    case WallisAndFutuna = 'wf';
    case WesternSahara = 'eh';
    case Yemen = 'ye';
    case Zambia = 'zm';
    case Zimbabwe = 'zw';

    public function trans(): string
    {
        return __("countries.$this->value");
    }

    public function transWithCity(?string $city): string
    {
        $country = $this->trans();

        return $city ? "$city, $country" : $country;
    }
}
