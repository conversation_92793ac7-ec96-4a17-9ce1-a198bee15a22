<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Libs\Sentinel\PersonalAccessToken;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Ensures that states of published tenders are moved forward.
        // Incubation -> Open; Open -> Reviewing;
        $schedule->command('tenders:update-status')->everyMinute();

        // Prunes all prunable application's models
        $schedule->command('model:prune')->dailyAt('03:00');

        // Manually prunes Sentinel's personal access token,
        // as it won't be automagically detected.
        $schedule->command('model:prune', [
            '--model' => [PersonalAccessToken::class],
        ])->dailyAt('03:00');

        // Clears stale tags from Redis cache
        $schedule->command('cache:prune-stale-tags')->hourly();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
