<?php

namespace App\Console\Commands;

use App\Enums\Enterprise\CompanyDataSource;
use App\Enums\LengthType;
use App\Models\Solution;
use App\Models\Technology;
use App\Models\Vendor;
use App\Models\Company;
use App\Models\Industry;
use App\Enums\Country;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;


class ImportClutchVendors extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vendors:import-clutch';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        $directory = resource_path('json/scraped_vendors');

        if (!File::exists($directory)) {
            $this->error("Directory does not exist: $directory");
            return;
        }

        $files = File::files($directory);

        if (empty($files)) {
            $this->info('No JSON files found.');
            return;
        }

        $bar = $this->output->createProgressBar(count($files));
        $bar->start();

        foreach ($files as $file) {
            $bar->advance();
            if ($file->getExtension() === 'json') {
                $filename = $file->getFilename();

                $content = File::get($file->getPathname());

                try {
                    $data = json_decode($content, true);

                    if (json_last_error() !== JSON_ERROR_NONE) {
                        $this->error("Invalid JSON in file: $filename");
                        continue;
                    }

                    $this->processFile($data, $filename);

                } catch (\Exception $e) {
                    $this->error("Error processing $filename: " . $e->getMessage() . ' line: ' . $e->getLine());
                }
            }
        }

        $bar->finish();

        $this->info('Processing complete.');
    }

    protected function processFile(array $data, string $filename): void
    {
        $this->info($data['url']);

        $company_data = $this->parseCompanyData($data);
        $company = Company::updateOrCreate(
            ['data_source_key' => $company_data['data_source_key']], 
            $company_data
        );

        $vendor_data = $this->parseVendorData($data);
        $vendor = $company->vendor()->updateOrCreate(
            ['company_id' => $company->id], 
            $vendor_data
        );

        $this->upsertVendorToQdrant($vendor);
        $this->upsertVendorFullToQdrant($vendor);

        $this->associateVendorIndustries($vendor, $data);
        $this->createVendorSolutions($vendor, $data, $filename);

    }

    private function upsertVendorToQdrant(Vendor $vendor): void
    {
        try {

            $this->info('upserting to vendor-names: ' . $vendor->company->name . ' vendor id: ' . $vendor->id);
            $response = Http::post('https://similarity-api.mangograss-25f115e1.germanywestcentral.azurecontainerapps.io/insert-data', [
                'collection_name' => 'vendor-names',
                'payload' => [
                    'id' => (string) $vendor->company->id,
                    'original_text' => $vendor->company->name,
                ],
            ]);

            $this->info($response);

        } catch (\Exception $e) {
            $this->info('error when upserting to vendor-names: ' . $vendor->company->name . ' error: '. $e->getMessage());
        }
    }

    private function upsertSolutionDescriptionToQdrant(Solution $solution): void
    {
        try {

            $this->info('upserting to project-descriptions: ' .  $solution->name . '  solution_id: ' . $solution->id);
            $response = Http::post('https://similarity-api.mangograss-25f115e1.germanywestcentral.azurecontainerapps.io/insert-data', [
                'collection_name' => 'project-descriptions',
                'payload' => [
                    'id' => (string) $solution->id,
                    'company_id' => (string) $solution->vendor->id,
                    'original_text' => $solution->about,
                ],
                'process_text' => true
            ]);

            $this->info($response);

        } catch (\Exception $e) {
            $this->info('error when upserting to project-descriptions: ' .  $solution->name . '  company:' . $solution->vendor->company->name . ' error: '. $e->getMessage());
        }
    }

    private function upsertVendorFullToQdrant(Vendor $vendor): void
    {
        try {

            $this->info('upserting to vendors-full: ' . $vendor->company->name . ' vendor id: ' . $vendor->id);
            $response = Http::post('https://similarity-api.mangograss-25f115e1.germanywestcentral.azurecontainerapps.io/insert-data', [
                'collection_name' => 'vendors-full',
                'payload' => [
                    'id' =>  (string) $vendor->id,
                    'original_text' => $vendor->company->name,
                    'company_id' => (string) $vendor->company->id,
                    'country' => $vendor->company->country,
                    'hq' => $vendor->company->hq,
                    'price' => [
                        'rate_junior' => (string) $vendor->rate_junior,
                        'rate_medior' => (string) $vendor->rate_medior,
                        'rate_senior' => (string) $vendor->rate_senior,
                        'rate_lead' => (string) $vendor->rate_lead,
                    ],
                
                ],
            ]);

            $this->info($response);

        } catch (\Exception $e) {
            $this->info('error when upserting to vendors-full: ' . $vendor->company->name . ' error: '. $e->getMessage());
        }
    }

    private function createVendorSolutions(Vendor $vendor, array $data, string $filename): void
    {
        foreach ($data['reviews'] as $review) {
            $solution_data = [
                'vendor_id' => $vendor->id,
                'publish_status' => 'published',
                'name' => $review['project']['name'],
                'description' => $review['project']['name'],
                'about' => $review['project']['description'],
                'country' => $vendor->company->country,
                'main_industry_id' => $this->getSolutionMainIndustry($review['project']['category']),
                'length_type' => LengthType::Months,
                'length' => $this->parseDateLength($review['project']['length'])
            ];

            $solution = Solution::updateOrCreate(
                ['vendor_id' => $vendor->id, 'name' => $review['project']['name'],],
                $solution_data);

            $this->upsertSolutionDescriptionToQdrant($solution);

            $this->associateSolutionIndustries($solution, $review);
            $this->associateSolutionTechnologies($solution, $review, $filename);
        }
    }

    private function associateSolutionTechnologies(Solution $solution, array $review, string $filename): void
    {
        $path = resource_path("json/scraped_vendors_extracted/{$filename}");

        if (!File::exists($path)) {
            $this->info('file tech not exists');
            return;
        }
    
        $file_content = json_decode(File::get($path), true);
        
        if (!isset($file_content['projects'])) {
            $this->info('file tech not projects');
            return;
        }
        
        $technology_names = array_column(array_filter($file_content['projects'], fn($project) => $project['name'] === $solution->name), 'tech')[0] ?? [];

        $technology_ids = [];
        foreach ($technology_names as $technology_name) {
            $technology = Technology::withTrashed()->firstOrCreate(["name" => $technology_name]);

            if ($technology->trashed()) {
                $technology->restore();
            }

            $technology_ids[] = $technology->id;
        }

        $solution->technologies()->syncWithoutDetaching($technology_ids);
    }

    private function parseDateLength($dateString): ?int {
        preg_match_all('/([A-Za-z]+)\.?/', $dateString, $months);
        preg_match_all('/(\d{4})/', $dateString, $years);
        
        // Detect if the string contains "Ongoing"
        $isOngoing = stripos($dateString, 'Ongoing') !== false;
        
        // Ensure we have at least one year OR "Ongoing"
        if (empty($months[1]) || (empty($years) && !$isOngoing)) {
            return null; // Invalid format
        }
    
        $months = array_map('strtolower', $months[1]); // Normalize month names
        $years = array_map('intval', $years[0]); // Convert years to integers
    
        // Month name to number mapping
        $monthMap = [
            'jan' => 1, 'feb' => 2, 'mar' => 3, 'apr' => 4, 'may' => 5, 'jun' => 6,
            'jul' => 7, 'aug' => 8, 'sep' => 9, 'oct' => 10, 'nov' => 11, 'dec' => 12
        ];
    
        // Convert month names to numbers, filtering out invalid names
        $monthNumbers = array_map(fn($m) => $monthMap[substr($m, 0, 3)] ?? null, $months);
        $monthNumbers = array_filter($monthNumbers); // Remove null values
    
        if (empty($monthNumbers)) {
            return null; // No valid months found
        }
    
        // Start date
        $startMonth = $monthNumbers[0];
        $startYear = !empty($years) ? $years[0] : null; // Set null if no year found
    
        if (!$startYear) {
            return null; // Invalid year
        }
    
        $startDate = date_create("$startYear-$startMonth-01");
    
        // Handle "Ongoing" case
        if ($isOngoing) {
            $endDate = new \DateTime(); // Use current date
        } else {
            $endMonth = $monthNumbers[1] ?? 12; // Default to December if missing
            $endYear = $years[1] ?? date('Y');
            $endDate = date_create("$endYear-$endMonth-01");
        }
    
        // Calculate difference in months
        $diff = date_diff($startDate, $endDate);
        $monthsDiff = ($diff->y * 12) + $diff->m;
    
        return $monthsDiff;
    }

    private function associateSolutionIndustries(Solution $solution, array $review): void
    {
        $industry_names = $review['project']['allCategories'];
        $industry_names = array_unique(array_values(array_diff($industry_names, [$solution->main_industry->name])));

        $industry_ids = [];
        foreach ($industry_names as $industry_name) {
            $industry = Industry::withTrashed()->firstOrCreate(["name" => $industry_name]);

            if ($industry->trashed()) {
                $industry->restore();
            }

            $industry_ids[] = $industry->id;

        }

        $solution->industries()->syncWithoutDetaching($industry_ids);
    }

    private function associateVendorIndustries(Vendor $vendor, array $data): void
    {
        $industry_names = [];

        if (!empty($data['focus'])) {
            foreach ($data['focus'] as $focus) {
                if (isset($focus['title'])) {
                    $industry_names[] = $focus['title'];
                }
            }
        }
    
        if (!empty($data['serviceProvided'])) {
            foreach ($data['serviceProvided'] as $service) {
                if (isset($service['name'])) {
                    $industry_names[] = $service['name'];
                }
            }
        }

        $industry_names = array_unique($industry_names);

        $industry_ids = [];

        foreach ($industry_names as $industry_name) {
            $industry = Industry::withTrashed()->firstOrCreate(["name" => $industry_name]);

            if ($industry->trashed()) {
                $industry->restore();
            }

            $industry_ids[] = $industry->id;

        }

        $vendor->industries()->syncWithoutDetaching($industry_ids);
    }   

    private function parseVendorData(array $data): array
    {
        $employees = $this->parseEmployeeCount($data);
        $rates = $this->parseRateValues($data);

        return [
            'main_industry_id' => $this->getVendorMainIndustry($data),
            'employees' => $employees,
            'developers' => $employees,
            'offering_resources' => 0,
            'offering_solutions' => 1,
            'payment_time_and_material' => 1,
            'payment_fixed_price' => 0,
            'notify_irrelevant_offers' => 0,
            'rate_junior' => $rates[0],
            'rate_medior' => $rates[1],
            'rate_senior' => $rates[2],
            'rate_lead' => $rates[3],
        ];
    }

    private function parseRateValues(array $data): array
    {
        $parts = explode('/', $data['summary']['averageHourlyRate']);
        $selected = trim($parts[2] ?? $parts[0]);

        preg_match_all('/\d+/', $selected, $matches);
        $numbers = array_map('intval', $matches[0]);

        $min = $numbers[0] ?? 0;
        $max = $numbers[1] ?? $min;

        $interval = ($max - $min) / 3;
        return [
            (int) round($min),
            (int) round($min + $interval),
            (int) round($min + 2 * $interval),
            (int) round($max)
        ];
    }

    private function parseEmployeeCount(array $data): int
    {
        $values = array_map('intval', explode('-', $data['summary']['employees']));
        return (int) (($values[0] + end($values)) / 2);
    }

    private function getSolutionMainIndustry(string $name): int
    {
        $industry = Industry::withTrashed()->firstOrCreate(["name" => $name]);
        if ($industry->trashed()) {
            $industry->restore();
        }

        return $industry->id;
    }

    private function getVendorMainIndustry(array $data): int
    {
        $industry = Industry::withTrashed()->firstOrCreate(["name" => array_reduce($data['serviceProvided'] ?? [], fn($max, $item) => $item['percent'] > ($max['percent'] ?? 0) ? $item : $max, [])['name'] ?? null]);
        if ($industry->trashed()) {
            $industry->restore();
        }

        return $industry->id;
    }

    private function parseCompanyData(array $data): array
    {
        $name = $data['summary']['name'] ?? basename($data['url'] ?? '');

        return [
            'is_public' => 1,
            'is_vendor' => 1,
            'name' => $name,
            # 'logo_resource_id' => $data['summary']['logo']
            # 'cover_resource_id'
            'owner' => $name,
            'hq' => $data['summary']['addresses'][0]['city'] . ', ' . $data['summary']['addresses'][0]['state'],
            'country' => array_column(Country::cases(), 'value', 'name')[ucfirst(strtolower($data['summary']['addresses'][0]['state'] ?? ''))] ?? 'us',
            'website' => $this->parseWebsite($data['websiteUrl']),
            'linkedin' => current(array_filter($data['summary']['socialLinks'] ?? [], fn(string $link): bool => str_contains($link, 'linkedin'))) ?: " ",
            'founded' => (int) preg_replace('/\D/', '', $data['summary']['founded'] ?? ''),
            'about' => $data['summary']['description'] ?? null,
            # notes
            'data_source_type' => CompanyDataSource::CLutch->value,
            'data_source_key' => isset($data['url']) ? basename($data['url']) : ($data['summary']['name'] ?? null)
        ];
    }

    private function parseWebsite(string $website): string
    {
        $parsedUrl = [];
        parse_str(parse_url($website, PHP_URL_QUERY), $parsedUrl);
        return 'https://' . $parsedUrl['provider_website'] ?? ' ';
    }
    
}
