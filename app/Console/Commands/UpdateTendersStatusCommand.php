<?php

namespace App\Console\Commands;

use App\Enums\TenderStatus;
use App\Models\Tender;
use App\Repositories\TendersRepository;
use Carbon\CarbonInterface as Carbon;
use Illuminate\Console\Command;

class UpdateTendersStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tenders:update-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Updates tenders status to the next stage.';

    /**
     * Create a new command instance.
     */
    public function __construct(
        private TendersRepository $tendersRepository,
    ) {
        parent::__construct();
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $now = now();

        $this->moveFromIncubationToOpen($now);
        $this->moveFromOpenToReviewing($now);

        return self::SUCCESS;
    }

    private function moveFromIncubationToOpen(Carbon $now): void
    {
        $tenders = $this->tendersRepository
            ->published()
            ->incubated()
            ->where('end_of_incubation', '<=', $now)
            ->get();

        if ($tenders) {
            $this->info("Opening [{$tenders->count()}] incubated tenders.");

            $tenders->each(fn (Tender $tender) => $tender->fill(['status' => TenderStatus::Open])->save());
        }
    }

    private function moveFromOpenToReviewing(Carbon $now): void
    {
        $tenders = $this->tendersRepository
            ->published()
            ->open()
            ->where('submissions_deadline', '<=', $now)
            ->get();

        if ($tenders) {
            $this->info("Reviewing [{$tenders->count()}] expired tenders.");

            $tenders->each(fn (Tender $tender) => $tender->fill(['status' => TenderStatus::Reviewing])->save());
        }
    }
}
