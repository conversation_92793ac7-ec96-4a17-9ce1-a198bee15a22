<?php

namespace App\Console\Commands;

use App\Enums\Enterprise\SimilarityApiCollection;
use App\Facades\SimilarityApi;
use App\Models\Company;
use Illuminate\Console\Command;

class SyncCompaniesWithSimilarityApi extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'nio:company:similarity-api-sync
        {--S|save : Save the changes to the database}
        {--A|all : Sync all including the ones synced already}
        {--T|similarity-score-threshold=0.8 : The minimum similarity score to consider a match}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync companies present in the database with the data returned from Similarity API';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $shouldSave = $this->option('save');
        $syncAll = $this->option('all');
        $similarityScoreThreshold = $this->option('similarity-score-threshold');

        if (!is_numeric($similarityScoreThreshold) || $similarityScoreThreshold < 0 || $similarityScoreThreshold > 1) {
            $this->error('The similarity score threshold must be a number between 0 and 1.');
            return;
        }
        $this->line($this->description);
        $this->warn('Saving changes: ' . ($shouldSave ? 'Yes' : 'No'));
        $this->line('Syncing: ' . ($syncAll ? 'All' : 'Unsynced only'));
        $this->line("Similarity score threshold: $similarityScoreThreshold");

        $baseQuery = Company::whereNull('deleted_at')
            ->whereNull('merge_into_id');

        $companies = $syncAll
            ? $baseQuery->get()
            : $baseQuery->whereNull('uuid')->get();

        $totalCompanies = count($companies);
        $syncCount = 0;

        $this->withProgressBar($companies, function (Company $company) use (
            &$syncCount,
            $shouldSave,
            $similarityScoreThreshold
        ) {
            $this->newLine();
            $this->line("Syncing company \"{$company->name}\"");

            $result = SimilarityApi::search([$company->name], SimilarityApiCollection::Companies);

            if (empty($result) || empty($result[0]) || empty($result[0][0])) {
                $this->warn("No results found for company \"{$company->name}\".");
                return;
            }

            $similarCompanies = $result[0];
            $firstMatch = $similarCompanies[0];

            if (empty($firstMatch['id']) || empty($firstMatch['payload'])) {
                $this->warn("Unexpected match result structure received for company {$company->name}.");
                return;
            }

            $this->line("Match: \"{$firstMatch['payload']['name']}\", Score: {$firstMatch['score']}");

            if ($firstMatch['score'] < $similarityScoreThreshold) {
                $this->warn("Similarity score for company \"{$company->name}\" is below threshold.");
                return;
            }

            if ($shouldSave) {
                $company->uuid = $firstMatch['id'];
                $company->name = $firstMatch['payload']['name'];
                $company->country = $firstMatch['payload']['country'];
                $company->hq = $firstMatch['payload']['hq'];
                $company->save();
            }

            $syncCount++;
            $this->info("Company \"{$company->name}\" synced successfully.");
        });

        $this->newLine();
        $this->info("Sync complete, synced $syncCount/$totalCompanies companies.");
    }
}
