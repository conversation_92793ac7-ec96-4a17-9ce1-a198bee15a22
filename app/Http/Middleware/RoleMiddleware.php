<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Gate;
use Spatie\Permission\Middleware\RoleMiddleware as SpatieRoleMiddleware;

class RoleMiddleware
{
    protected $roleMiddleware;

    public function __construct(SpatieRoleMiddleware $roleMiddleware)
    {
        $this->roleMiddleware = $roleMiddleware;
    }

    public function handle(Request $request, Closure $next, $role): mixed
    {
        // Check if Gate::before allows access
        if (Gate::allows('any')) {
            return $next($request);
        }

        // Fallback to <PERSON><PERSON>'s RoleMiddleware
        return $this->roleMiddleware->handle($request, $next, $role);
    }
}
