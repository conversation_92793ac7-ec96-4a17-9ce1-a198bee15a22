<?php

namespace App\Http\Middleware;

use App\Repositories\UsersRepository;
use Closure;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Contracts\Auth\Factory as Auth;
use Illuminate\Encryption\Encrypter;
use Illuminate\Http\Request;

class DownloadableFile extends Authenticate
{
    private const TOKEN_TTL_MINUTES = 5;

    public function __construct(
        private Encrypter $encrypter,
        private UsersRepository $usersRepository,
        Auth $auth,
    ) {
        parent::__construct($auth);
    }

    /**
     * Handle an incoming request.
     *
     * @param  Request  $request
     * @param  string[]  ...$guards
     *
     * @throws AuthenticationException
     */
    public function handle($request, Closure $next, ...$guards): mixed
    {
        $token = $request->query('token');

        if (! $token) {
            $this->authenticate($request, [...$guards, 'api']);

            return response()->json([
                'data' => [
                    'token' => $this->generateAccessToken($request),
                ],
            ]);
        }

        $this->validateToken($request, $guards, $token);

        return $next($request);
    }

    private function generateAccessToken(Request $request): string
    {
        $expireAt = now()->addMinutes(self::TOKEN_TTL_MINUTES)->timestamp;
        $userId = $request->user()->getAuthIdentifier();
        $payload = "$userId:$expireAt";

        return $this->encrypter->encryptString($payload);
    }

    private function validateToken(Request $request, array $guards, string $token): void
    {
        $payload = rescue(fn () => $this->encrypter->decryptString($token), fn () => $this->unauthenticated($request, $guards));
        [$userId, $timestamp] = explode(':', $payload);
        $expireAt = now()->setTimestamp((int) $timestamp);

        if ($expireAt->isPast()) {
            $this->unauthenticated($request, $guards);
        }

        // Todo(Matus Makatura): will not work unfortunately for multiguard configurations, only for default guard
        $this->auth->guard()->setUser($this->usersRepository->findById($userId));
    }
}
