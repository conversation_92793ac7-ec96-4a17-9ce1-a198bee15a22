<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CaptureAuthParams
{
    /**
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Store the client id in the session after the request is handled as it might have invalidated the session.
        if ($request->has('client_id')) {
            session(['authClient' => $request->get('client_id')]);
        }

        return $response;
    }
}
