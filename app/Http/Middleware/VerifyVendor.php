<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class VerifyVendor
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string ...$types): mixed
    {
        $user = $request->user();

        // Sanity check, this middleware cannot work if user is not authenticated
        if (! $user) {
            return response()->noContent(401);
        }

        // If user is not associated with company that is a vendor,
        // we will forbid them to access requested resource
        if (! $user->company_id || ! $user->company->is_vendor) {
            return response()->json([
                'error' => [
                    'message' => __('auth.forbidden'),
                ],
            ], 403);
        }

        return $next($request);
    }
}
