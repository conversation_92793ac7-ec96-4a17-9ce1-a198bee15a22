<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class PreventPageCaching
{
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        return $response->withHeaders([
            'Cache-Control' => 'no-cache, no-store, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0',
        ]);
    }
} 