<?php

namespace App\Http\Factories\Resources\Forms;

use App\Enums\ComponentType;

readonly class ComponentFactory
{
    /**
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public static function create(
        string        $name,
        ComponentType $type,
        string        $payloadKey,
        mixed         $value,
        mixed         $defaultValue = null,
        array         $extraOptions = []
    ): array
    {
        return [
            'name' => $name,
            'component' => [
                'id' => $type,
                'payload_key' => $payloadKey
            ] + ($defaultValue !== null ? [
                'default_value' => $defaultValue
             ] : []) + $extraOptions,
            'value' => $value
        ];
    }
}
