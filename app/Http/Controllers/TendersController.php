<?php

namespace App\Http\Controllers;

use App\Http\Filtering\Filterer;
use App\Http\Filtering\Filters\TendersCategoryFilter;
use App\Http\Resources\TenderIndexResource;
use App\Http\Resources\TenderShowResource;
use App\Repositories\Enterprise\TenderInvitationsRepository;
use App\Repositories\TendersRepository;
use Illuminate\Http\JsonResponse;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Subgroup;

#[Group("platform")]
#[Subgroup("tenders")]
class TendersController extends Controller
{
    public function __construct(
        private TendersRepository $tendersRepository,
        private TenderInvitationsRepository $tenderInvitationsRepository,
        private Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $tenders = $this->filterer->filterAndPaginate(
            $this->tendersRepository
                ->publishedOrOwn()
                ->with('company', 'project.main_industry', 'positions.employee_position'),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(TenderIndexResource::collection($tenders));
    }

    public function show(string $slug): JsonResponse
    {
        $tender = $this->tendersRepository
            ->publishedOrOwn()
            ->with(['positions' => fn ($subQuery) => $subQuery->withCount('own_candidates')])
            ->findBySlugOrFail($slug);

        $this->tenderInvitationsRepository->markAsViewedIfInvited($tender, auth()->user());

        return $this->ok(TenderShowResource::make($tender));
    }

    private function filters(): array
    {
        return [
            new TendersCategoryFilter,
            $this->filterer->filterBy('name')->operator('like'),
            $this->filterer->filterBy('service_type'),
            $this->filterer->filterBy('publish_status'),
            $this->filterer->filterBy('status'),
            $this->filterer->filterBy('technologies')->column('project.technologies.id', 'positions.technologies.id')->array(),
            $this->filterer->filterBy('industries')->column('project.main_industry_id', 'project.industries.id')->array(),
            $this->filterer->filterBy('countries')->column('country')->array(),
            $this->filterer->filterBy('company_id')->column('company.public_id'),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('submissions_deadline')->default('desc'),
            $this->filterer->sortBy('status')->array(),
            $this->filterer->sortBy('publish_status')->array(),
        ];
    }
}
