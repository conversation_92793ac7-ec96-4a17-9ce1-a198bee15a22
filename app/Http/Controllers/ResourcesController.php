<?php

namespace App\Http\Controllers;

use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON>s\Scribe\Attributes\Subgroup;
use App\Http\Requests\ResourceRequest;
use Illuminate\Http\JsonResponse;
use Libs\Warehouse\Warehouse;

#[Group("platform")]
#[Subgroup("resources")]
class ResourcesController extends Controller
{
    public function __construct(
        private Warehouse $warehouse,
    ) {}

    public function store(ResourceRequest $request): JsonResponse
    {
        $resource = $this->warehouse->store($request->input('type'), $request->file('content'));

        return $this->created($this->warehouse->jsonResource($resource));
    }
}
