<?php

namespace App\Http\Controllers;

use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Filtering\Filterer;
use App\Http\Resources\IndustryIndexResource;
use App\Repositories\IndustriesRepository;
use Illuminate\Http\JsonResponse;

#[Group("platform")]
#[Subgroup("industries")]
class IndustriesController extends Controller
{
    public function __construct(
        private IndustriesRepository $industriesRepository,
        private Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $industries = $this->filterer->filter(
            $this->industriesRepository->query(),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(IndustryIndexResource::collection($industries));
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('parent_id')->array(),
            $this->filterer->filterBy('featured'),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('featured')->default('desc'),
            $this->filterer->sortBy('name')->default(),
        ];
    }
}
