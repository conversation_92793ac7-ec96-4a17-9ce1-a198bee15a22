<?php

namespace App\Http\Controllers\Users\Profile;

use <PERSON>nu<PERSON>s\Scribe\Attributes\Group;
use <PERSON>nu<PERSON>s\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Requests\Users\Profile\ProfileRequest;
use App\Http\Resources\Users\Profile\ProfileResource;
use App\Repositories\UsersRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

#[Group("platform")]
#[Subgroup("profile")]
class ProfileController extends Controller
{
    public function __construct(
        private UsersRepository $usersRepository,
    ) {}

    public function show(): JsonResponse
    {
        $user = auth()->user();
        $user->load([
            'company',
            'roles' => fn ($query) => $query->where('guard_name', 'web'),
            'workspaces'
        ]);

        return $this->ok(ProfileResource::make($user));
    }

    public function update(ProfileRequest $request): Response
    {
        $data = $request->validated();
        $this->usersRepository->updateOwnProfile($data);

        return $this->noContent();
    }
}
