<?php

namespace App\Http\Controllers\Enterprise;

use App\Http\Controllers\Controller;
use App\Http\Requests\Enterprise\CompaniesImportRequest;
use App\Http\Requests\Enterprise\CompanyImportShowRequest;
use App\Http\Requests\Enterprise\CompanyImportUpdateRequest;
use App\Http\Requests\Enterprise\WorkspaceCompaniesRequest;
use App\Http\Resources\Enterprise\CompaniesImportResource;
use App\Http\Resources\Enterprise\Workspace\WorkspaceCompanyFiltersResource;
use App\Http\Resources\Enterprise\Workspace\WorkspaceCompanyResource;
use App\Models\Company;
use App\Repositories\CompaniesRepository;
use App\Repositories\Enterprise\CompaniesImportRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response as HttpResponse;
use Illuminate\Support\Facades\Log;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Response;
use Knuckles\Scribe\Attributes\ResponseFromFile;
use Knuckles\Scribe\Attributes\Subgroup;

#[Group('Enterprise')]
#[Subgroup('Workspace Companies')]
class WorkspaceCompaniesController extends Controller
{
    public function __construct(
        private readonly CompaniesImportRepository $companiesImportRepository,
        protected readonly CompaniesRepository $companiesRepository,
    ) {}

    public function index(WorkspaceCompaniesRequest $request): JsonResponse
    {
        $workspace = $request->user()->workspaces()->first();

        $companies = $this->companiesRepository
            ->findCompaniesByWorkspace($workspace)
            ->paginate(app()->environment('testing') ? 2 : 20);

        return $this->ok(WorkspaceCompanyResource::collection($companies));
    }

    public function filterOptions(Request $request): JsonResponse
    {
        $workspace = $request->user()->workspaces()->first();

        $companies = $this->companiesRepository
            ->findCompaniesByWorkspace($workspace)
            ->get();

        $technologies = $companies->pluck('vendor.technologies')->flatten()->unique('id')->values();
        $industries = $companies->pluck('vendor.industries')->flatten()->unique('id')->values();
        $countries = $companies->pluck('country')->unique()->values();

        return $this->ok(WorkspaceCompanyFiltersResource::make([
            'technologies' => $technologies,
            'industries' => $industries,
            'countries' => $countries,
        ]));
    }

    #[Endpoint(
        'Process Company Import File',
        "Upload a file containing a list of company names.
        The file must be a CSV, XLS, or XLSX format and contain a 'name' column."
    )]
    #[ResponseFromFile('responses/Companies/import.json', HttpResponse::HTTP_OK)]
    #[ResponseFromFile('responses/Companies/error_422.json', HttpResponse::HTTP_UNPROCESSABLE_ENTITY)]
    public function import(CompaniesImportRequest $request): JsonResponse|HttpResponse
    {
        $companyImportDtos = $this->companiesImportRepository->processFile($request->file('file'));

        return $this->ok(
            CompaniesImportResource::collection(collect($companyImportDtos))
        );
    }

    #[Endpoint(
        'Check Company Import Status',
        'Retrieve the status of company import operations by their IDs.'
    )]
    #[Response(status: HttpResponse::HTTP_OK)]
    public function showImportStatus(CompanyImportShowRequest $request): JsonResponse|HttpResponse
    {
        $companyImportDtos = collect($request->input('import_ids'))
            ->map(fn (string $id) => cache()->get("company-imports.$id"))
            ->filter();

        return $this->ok($companyImportDtos);
    }

    #[Endpoint(
        'Update Company During Import',
        "Update company's name or status."
    )]
    #[Response(null, HttpResponse::HTTP_OK)]
    #[ResponseFromFile('responses/Companies/error_422.json', HttpResponse::HTTP_UNPROCESSABLE_ENTITY)]
    public function updateCompanyImport(CompanyImportUpdateRequest $request): JsonResponse|HttpResponse
    {
        $this->companiesImportRepository->updateCompanyImport($request);

        return $this->ok();
    }

    #[Endpoint('Remove company from workspace')]
    #[Response(status: HttpResponse::HTTP_NO_CONTENT)]
    public function destroy(Company $company): HttpResponse
    {
        $user = auth()->user();
        $workspace = $user->workspaces()->first();

        if ($workspace) {
            $result = $workspace->companies()->detach($company);

            if (! $result) {
                Log::warning('Attempted to remove company from workspace which was not attached to it', [
                    'company_id' => $company->public_id,
                    'workspace_id' => $workspace->id,
                ]);
            }
        }

        return $this->noContent();
    }
}
