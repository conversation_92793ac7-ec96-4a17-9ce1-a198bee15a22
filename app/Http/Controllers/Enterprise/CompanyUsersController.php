<?php

namespace App\Http\Controllers\Enterprise;

use App\Http\Controllers\Controller;
use App\Http\Resources\Enterprise\UserResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response as HttpResponse;
use <PERSON><PERSON><PERSON>s\Scribe\Attributes\Endpoint;
use <PERSON>nu<PERSON>s\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Response;
use Knuckles\Scribe\Attributes\Subgroup;

#[Group("Enterprise")]
#[Subgroup("Company Users")]
class CompanyUsersController extends Controller
{
    #[Endpoint('Get the company users')]
    #[Response(HttpResponse::HTTP_OK)]
    public function index(Request $request): JsonResponse
    {
        return $this->ok(UserResource::collection($request->user()->company->users));
    }
}
