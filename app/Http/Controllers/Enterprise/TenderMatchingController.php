<?php

namespace App\Http\Controllers\Enterprise;

use App\Enums\Enterprise\TenderMatchingCompaniesFilter;
use App\Http\Controllers\Controller;
use App\Http\Requests\Enterprise\TenderMatchIndexRequest;
use App\Http\Resources\Enterprise\TenderMatchResource;
use App\Jobs\Enterprise\RunTenderMatching;
use App\Models\Tender;
use App\Models\TenderMatch;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\QueryParam;
use Knuckles\Scribe\Attributes\Subgroup;

#[Group('Enterprise')]
#[Subgroup('Tenders')]
class TenderMatchingController extends Controller
{
    #[Endpoint('Tender matching list', 'List matching runs for a tender')]
    #[QueryParam('companies_filter', 'string', 'Filter by type', required: false, enum: TenderMatchingCompaniesFilter::class)]
    public function index(TenderMatchIndexRequest $request, Tender $tender): JsonResponse
    {
        $companiesFilter = $request->query('companies_filter', TenderMatchingCompaniesFilter::Selection);
        return $this->ok(
            TenderMatchResource::collection(
                $tender->matches()
                    ->with([
                        'tender',
                        'matchedCompanies' => function (BelongsToMany $query): void {
                            $query->limit(100);
                        },
                        'matchedCompanies.vendor.industries',
                        'matchedCompanies.vendor.clients',
                        'matchedCompanies.vendor.technologies',
                        'matchedCompanies.vendor.employee_positions',
                    ])
                    ->where('companies_filter', $companiesFilter)
                    ->latest()
                    ->simplePaginate(3)
            )
        );
    }

    #[Endpoint('Run tender matching', 'Run matching for tender.')]
    public function runMatching(Tender $tender, Request $request): JsonResponse
    {
        $tender->load([
            'positions',
            'rfp.locations'
        ]);

        $workspace = $request->user()->workspaces()->firstOrFail();
        RunTenderMatching::dispatch($tender, $workspace, TenderMatchingCompaniesFilter::Selection);
        RunTenderMatching::dispatch($tender, $workspace, TenderMatchingCompaniesFilter::Marketplace);

        return $this->accepted([
            'status' => 'success',
            'message' => 'Tender matching is in progress.',
        ]);

    }

    #[Endpoint('Get single tender matching', 'Get company matching data for tender.')]
    public function show(TenderMatch $tenderMatch): JsonResponse
    {
        $tenderMatch->load([
            'tender',
            'matchedCompanies' => function (BelongsToMany $query): void {
                $query->limit(100);
            },
            'matchedCompanies.vendor.industries',
            'matchedCompanies.vendor.clients',
            'matchedCompanies.vendor.technologies',
            'matchedCompanies.vendor.employee_positions',
        ]);
        return $this->ok(
            TenderMatchResource::make($tenderMatch)
        );
    }
}
