<?php

namespace App\Http\Controllers\Enterprise;

use App\Http\Controllers\Controller;
use App\Http\Resources\Enterprise\TenderCompanyInvitationResource;
use App\Models\Company;
use App\Models\Tender;
use App\Repositories\Enterprise\TenderInvitationsRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response as HttpResponse;

class TenderInvitationsController extends Controller
{
    function __construct(
        private TenderInvitationsRepository $tenderInvitationsRepository,
    ) {}

    public function index(Request $request, Tender $tender): JsonResponse
    {
        return $this->ok(
            TenderCompanyInvitationResource::collection(
                $this->tenderInvitationsRepository->getInvitedCompanies($tender, $request->user())
            )
        );
    }

    public function send(Tender $tender): HttpResponse
    {
        $this->tenderInvitationsRepository->sendInvitations($tender);
        return $this->accepted();
    }

    public function addCompany(Request $request, Tender $tender, Company $company): JsonResponse
    {
        $tender->invitedCompanies()->syncWithoutDetaching($company->id);

        return $this->created(
            TenderCompanyInvitationResource::make(
                $this->tenderInvitationsRepository->getSingleInvitedCompany($tender, $request->user(), $company->id)
            )
        );
    }

    public function removeCompany(Tender $tender, Company $company): HttpResponse
    {
        $tender->invitedCompanies()->detach($company->id);
        return $this->noContent();
    }
}
