<?php

namespace App\Http\Controllers\Enterprise;

use App\Http\Controllers\Controller;
use App\Models\Company;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response as HttpResponse;
use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Subgroup;
use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Endpoint;
use <PERSON>nu<PERSON>s\Scribe\Attributes\Response;
use Knuckles\Scribe\Attributes\ResponseFromFile;
use App\Models\Workspace;
use App\Http\Requests\Enterprise\WorkspaceUpdateRequest;
use App\Http\Resources\Enterprise\Workspace\WorkspaceShowResource;
use App\Repositories\CompaniesRepository;

#[Group("Enterprise")]
#[Subgroup("Workspace")]
class WorkspaceController extends Controller
{
    public function __construct(
        protected readonly CompaniesRepository $companiesRepository,
    ) {}

    #[Endpoint(
        "Show workspace",
        "Display companies from user's default workspace"
    )]
    #[ResponseFromFile('responses/Workspaces/show.json', HttpResponse::HTTP_OK)]
    public function show(Request $request): JsonResponse
    {
        return $this->ok(WorkspaceShowResource::make($request->user()->workspaces()->first()));
    }

    #[Endpoint(
        "Store user's default workspace",
        "Create user's default workspace if not created yet and add companies to it."
    )]
    #[Response(status: HttpResponse::HTTP_CREATED, description: "Companies successfully added to the workspace.")]
    public function store(WorkspaceUpdateRequest $request): JsonResponse
    {
        $user = $request->user();
        $workspace = $user->workspaces()->first();

        if (!$workspace) {
            $workspace = Workspace::create();
            $user->workspaces()->attach($workspace);
        }

        $companyIds = Company::whereIn('public_id', $request->input('company_ids'))->pluck('id');
        $workspace->companies()->syncWithoutDetaching($companyIds);

        return response()->json(["message" => "Companies successfully added to the workspace."], 201);
    }
}
