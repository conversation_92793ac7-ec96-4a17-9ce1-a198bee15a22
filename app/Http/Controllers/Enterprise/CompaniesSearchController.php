<?php

namespace App\Http\Controllers\Enterprise;

use App\Http\Controllers\Controller;
use App\Http\Requests\Enterprise\CompaniesSearchRequest;
use App\Repositories\Enterprise\CompaniesSearchRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response as HttpResponse;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Endpoint;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\ResponseFromFile;
use Knuckles\Scribe\Attributes\Subgroup;

#[Group("Enterprise")]
#[Subgroup("Companies Search")]
class CompaniesSearchController extends Controller
{
    public function __construct(
        private readonly CompaniesSearchRepository $companiesSearchRepository
    ) {}

    /**
     * @throws \Exception
     */
    #[Endpoint("Search for Companies", "Find vendors by company name.")]
    #[ResponseFromFile('responses/Companies/search.json', HttpResponse::HTTP_OK)]
    #[ResponseFromFile('responses/Companies/error_422.json', HttpResponse::HTTP_UNPROCESSABLE_ENTITY)]
    public function search(CompaniesSearchRequest $request): JsonResponse|HttpResponse
    {
        return $this->ok(
            $this->companiesSearchRepository->search(
                (string)$request->string('payload')
            )
        );
    }

    #[Endpoint("Get Recommended Companies")]
    #[ResponseFromFile('responses/Companies/search.json', HttpResponse::HTTP_OK)]
    public function recommended(): JsonResponse
    {
        return $this->ok(
            $this->companiesSearchRepository->recommended()
        );
    }
}
