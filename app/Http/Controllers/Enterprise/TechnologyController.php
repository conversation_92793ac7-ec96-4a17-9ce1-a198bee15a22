<?php

namespace App\Http\Controllers\Enterprise;

use App\Enums\Enterprise\TechnologyType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Enterprise\TechnologyIndexRequest;
use App\Http\Resources\Enterprise\TechnologyResource;
use App\Models\Technology;
use Illuminate\Http\JsonResponse;
use <PERSON>nuckles\Scribe\Attributes\Endpoint;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\QueryParam;
use Knuckles\Scribe\Attributes\ResponseFromFile;
use Knuckles\Scribe\Attributes\Subgroup;

#[Group("Enterprise")]
#[Subgroup("Technologies")]
class TechnologyController extends Controller
{
    #[Endpoint('Technologies list', 'List technologies and tools')]
    #[QueryParam('type', 'string', 'Filter by type', required: false, enum: TechnologyType::class)]
    #[QueryParam('search', 'string', 'Filter by text search', required: false)]
    #[ResponseFromFile('responses/Technology/index.json', JsonResponse::HTTP_OK)]
    public function index(TechnologyIndexRequest $request): JsonResponse
    {
        $technologies = Technology::query()->where('type', $request->query('type', TechnologyType::Technology));

        $searchFilter = $request->query('search');
        if ($searchFilter) {
            $technologies->whereLike('name', "%{$searchFilter}%");
        }

        return $this->ok(
            TechnologyResource::collection(
                $technologies->orderBy('name')->simplePaginate(50)
            )
        );
    }
}
