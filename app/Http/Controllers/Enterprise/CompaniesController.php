<?php

namespace App\Http\Controllers\Enterprise;

use App\Models\Company;
use App\Http\Controllers\Controller;
use App\Http\Resources\Enterprise\CompanyResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response as HttpResponse;
use <PERSON>nuckles\Scribe\Attributes\Endpoint;
use <PERSON>nuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\ResponseFromFile;
use Knuckles\Scribe\Attributes\Subgroup;

#[Group("Enterprise")]
#[Subgroup("Companies")]
class CompaniesController extends Controller
{
    #[Endpoint("Show Company")]
    #[ResponseFromFile('responses/Companies/show.json', HttpResponse::HTTP_OK)]
    public function show(Company $company): JsonResponse
    {
        $company->load([
            'vendor',
            'vendor.main_industry',
            'vendor.industries',
            'vendor.technologies',
            'vendor.clients.client',
            'vendor.employee_positions'
        ]);

        return $this->ok(CompanyResource::make($company));
    }
}
