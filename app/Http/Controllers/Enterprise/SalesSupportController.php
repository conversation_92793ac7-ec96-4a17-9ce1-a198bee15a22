<?php

namespace App\Http\Controllers\Enterprise;

use Illuminate\View\View;
use <PERSON>nuckles\Scribe\Attributes\Group;
use <PERSON>nuckles\Scribe\Attributes\Subgroup;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Response;
use Illuminate\Http\Response as HttpResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;


class SalesSupportController extends Controller
{
    public function lastLogin(): View
    {
        $days = \now()->subDays(30);

        $data = DB::table('oauth_access_tokens')
            ->join('users', 'oauth_access_tokens.user_id', '=', 'users.id')
            ->where('oauth_access_tokens.updated_at', '>=', $days)
            ->select(
                'users.name',
                'users.surname',
                'users.email',
                'oauth_access_tokens.created_at',
                'oauth_access_tokens.updated_at',
                'oauth_access_tokens.revoked'
            )
            ->whereNotIn('users.email', ['<EMAIL>', '<EMAIL>', '<EMAIL>'])
            ->orderBy('oauth_access_tokens.created_at', 'desc')
            ->get();

        return view('sales-support.last-login', ['data' => $data]);
    }
}
