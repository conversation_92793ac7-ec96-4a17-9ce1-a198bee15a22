<?php

namespace App\Http\Controllers\Enterprise;

use App\Http\Controllers\Controller;
use App\Http\Requests\Enterprise\CandidateAssignmentUpdateRequest;
use App\Http\Requests\Enterprise\FilterableDataRequest;
use App\Http\Resources\Enterprise\Candidate\AssignedCandidateFiltersResource;
use App\Http\Resources\Enterprise\Candidate\BenchCandidateFiltersResource;
use App\Http\Resources\Enterprise\Candidate\CandidateShowResource;
use App\Http\Resources\Enterprise\CandidateResource;
use App\Models\Candidate;
use App\Repositories\CandidatesRepository;
use Illuminate\Http\JsonResponse;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Subgroup;

#[Group('Enterprise')]
#[Subgroup('Candidates')]
class CandidatesController extends Controller
{
    protected const PAGINATE_PER_PAGE = 100;

    public function __construct(
        protected readonly CandidatesRepository $candidatesRepository,
    ) {}

    public function bench(): JsonResponse
    {
        return $this->ok(
            CandidateResource::collection(
                $this->candidatesRepository->activeBench()->paginate(self::PAGINATE_PER_PAGE)
            )
        );
    }

    public function assigned(FilterableDataRequest $request): JsonResponse
    {
        $candidates = $this->candidatesRepository
            ->assignedToCompany(auth()->user()?->company_id)
            ->paginate(self::PAGINATE_PER_PAGE);

        return $this->ok(
            CandidateResource::collection($candidates)
        );
    }

    public function benchFilterOptions(): JsonResponse
    {
        $candidates = $this->candidatesRepository->activeBench()
            ->get();

        $companies = $candidates->pluck('company')->unique('id')->values();
        $technologies = $candidates->pluck('skills')->flatten()->pluck('technology')->unique('id')->values();
        $countries = $candidates->pluck('country')->unique()->values();

        return $this->ok(BenchCandidateFiltersResource::make([
            'companies' => $companies,
            'technologies' => $technologies,
            'countries' => $countries,
        ]));
    }

    public function assignedFilterOptions(): JsonResponse
    {
        $candidates = $this->candidatesRepository
            ->assignedToCompany(auth()->user()?->company_id)
            ->get();

        $companies = $candidates->pluck('company')->unique('id')->values();
        $technologies = $candidates->pluck('skills')->flatten()->pluck('technology')->unique('id')->values();
        $tenders = $candidates->pluck('activeAssignment.tender')->filter()->unique('id')->values();
        $managers = $candidates->pluck('activeAssignment.manager')->filter()->unique('id')->values();
        $countries = $candidates->pluck('country')->unique()->values();

        return $this->ok(AssignedCandidateFiltersResource::make([
            'companies' => $companies,
            'technologies' => $technologies,
            'tenders' => $tenders,
            'managers' => $managers,
            'countries' => $countries,
        ]));
    }

    public function updateAssignment(CandidateAssignmentUpdateRequest $request, Candidate $candidate): JsonResponse
    {
        if (!$candidate->activeAssignment) {
            return $this->unprocessable('Candidate does not have an active assignment.');
        }
        $candidate->activeAssignment->update([
            'manager_id' => $request->input('manager_id'),
        ]);

        $candidate->refresh();
        $candidate->load([
            'company',
            'activeAssignment.company',
            'activeAssignment.tender',
            'activeAssignment.tenderPosition',
            'activeAssignment.manager',
        ]);

        return $this->ok(CandidateResource::make($candidate));
    }

    public function show(Candidate $candidate): JsonResponse
    {
        $candidate->loadMissing([
            'vendor.company',
            'skills.experiences',
            'experiences',
        ]);

        return $this->ok(CandidateShowResource::make($candidate));
    }
}
