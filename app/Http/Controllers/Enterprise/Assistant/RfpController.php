<?php

namespace App\Http\Controllers\Enterprise\Assistant;

use App\Enums\Enterprise\RfpFilterType;
use App\Enums\Enterprise\TenderMatchingCompaniesFilter;
use App\Exceptions\Enterprise\TenderExistsForRfpException as TenderExistsForRfpAppException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Enterprise\Assistant\RfpIndexRequest;
use App\Http\Requests\Enterprise\Assistant\RfpSetActiveStepRequest;
use App\Http\Requests\Enterprise\Assistant\RfpUploadRequest;
use App\Http\Requests\Enterprise\Assistant\RfpUpdateRequest;
use App\Http\Resources\Enterprise\Assistant\RfpResource;
use App\Http\Resources\Enterprise\Assistant\RfpInfoFormResource;
use App\Http\Resources\Enterprise\Assistant\RfpResourcesFormResource;
use App\Http\Resources\Enterprise\Assistant\RfpSuggestedPositionResource;
use App\Http\Resources\Enterprise\Assistant\RfpTenderResource;
use App\Jobs\Enterprise\RunTenderMatching;
use App\Models\Rfp;
use App\Repositories\Enterprise\Assistant\Exceptions\TenderExistsForRfpException;
use App\Repositories\Enterprise\Assistant\RfpCreateRepository;
use App\Repositories\Enterprise\Assistant\RfpDeleteRepository;
use App\Repositories\Enterprise\Assistant\RfpTenderRepository;
use App\Repositories\Enterprise\Assistant\RfpUploadRepository;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response as HttpResponse;
use Illuminate\Http\Request;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Header;
use Knuckles\Scribe\Attributes\QueryParam;
use Knuckles\Scribe\Attributes\Response;
use Knuckles\Scribe\Attributes\Subgroup;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\ResponseFromFile;


#[Group("Enterprise")]
#[Subgroup("Assistant")]
class RfpController extends Controller
{
    public function __construct(
        private RfpUploadRepository $rfpUploadRepository,
        private RfpCreateRepository $rfpCreateRepository,
        private RfpDeleteRepository $rfpDeleteRepository,
        private RfpTenderRepository $rfpTenderRepository
    )
    {
    }

    #[Endpoint("Store Rfp", "Upload and store Rfp")]
    #[ResponseFromFile('responses/Assistant/Rfp/store.json', HttpResponse::HTTP_CREATED)]
    #[ResponseFromFile('responses/Companies/error_422.json', HttpResponse::HTTP_UNPROCESSABLE_ENTITY)]
    public function store(RfpUploadRequest $request): JsonResponse
    {
        $rfp = $this->rfpUploadRepository->storeRfp($request);

        return $this->created(
            new RfpResource($rfp)
        );
    }

    #[Endpoint("List Rfp", "List user's Rfps")]
    #[QueryParam('type', 'string', 'Filter by type', required: false, enum: RfpFilterType::class)]
    #[QueryParam('search', 'string', 'Filter by text search', required: false)]
    #[ResponseFromFile('responses/Assistant/Rfp/index.json', HttpResponse::HTTP_OK)]
    public function index(RfpIndexRequest $request): JsonResponse
    {
        $user = $request->user();
        $select = Rfp::query()
            ->with('tender')
            ->where('author_id', $user->id);

        $typeFilter = $request->enum('type', RfpFilterType::class);
        if ($typeFilter === RfpFilterType::Draft) {
            $select->whereNull('tender_id');
        } else if ($typeFilter === RfpFilterType::WithTender) {
            $select->whereNotNull('tender_id');
        }

        $searchFilter = $request->query('search');
        if ($searchFilter) {
            $select->whereLike('title', "%{$searchFilter}%");
        }

        return $this->ok(
            RfpResource::collection(
                $select->latest()->simplePaginate(25)
            )
        );
    }

    #[Endpoint("Show Rfp", "Show Rfp data")]
    #[ResponseFromFile('responses/Assistant/Rfp/show.json', HttpResponse::HTTP_OK)]
    public function show(Rfp $rfp): JsonResponse
    {
        $rfp->load('tender');
        return $this->ok(
            new RfpResource($rfp)
        );
    }

    #[Endpoint("Update Rfp", "Update Rfp data")]
    #[Header('Nio-Bypass-Validation', '1')]
    #[ResponseFromFile('responses/Assistant/Rfp/update.json', HttpResponse::HTTP_OK)]
    public function update(Rfp $rfp, RfpUpdateRequest $request): JsonResponse|HttpResponse
    {
        try {
            return $this->ok($this->rfpUploadRepository->updateRfp($request, $rfp));
        } catch (TenderExistsForRfpException) {
            throw new TenderExistsForRfpAppException($rfp);
        }
    }

    #[Endpoint("Delete Rfp")]
    #[Response('Rfp deleted', HttpResponse::HTTP_NO_CONTENT)]
    public function delete(Rfp $rfp): HttpResponse
    {
        try {
            $this->rfpDeleteRepository->delete($rfp);
        } catch (TenderExistsForRfpException) {
            throw new TenderExistsForRfpAppException($rfp);
        }
        return $this->noContent();
    }

    /**
     * @throws RequestException
     * @throws ConnectionException
     */
    #[Endpoint("Rfp Info", "Get Rfp Info form and estimates")]
    #[ResponseFromFile('responses/Assistant/Rfp/info.json', HttpResponse::HTTP_OK)]
    public function info(Rfp $rfp): JsonResponse
    {
        $this->rfpCreateRepository->extractInfo($rfp);
        $rfp->load(['locations', 'primaryIndustry']);

        return $this->ok(RfpInfoFormResource::make($rfp));
    }

    #[Endpoint("Rfp Resources", "Get Rfp Resources form")]
    #[ResponseFromFile('responses/Assistant/Rfp/resources.json', HttpResponse::HTTP_OK)]
    public function resources(Rfp $rfp): JsonResponse
    {
        $this->rfpCreateRepository->extractResources($rfp);
        $rfp->load(['rfpPositions.languages', 'rfpPositions.technologies', 'rfpPositions.tools']);

        return $this->ok(RfpResourcesFormResource::make($rfp));
    }

    #[Endpoint("Rfp Suggested Resources", "Get suggested resources for a given RFP")]
    #[ResponseFromFile('responses/Assistant/Rfp/resources.json', HttpResponse::HTTP_OK)]
    public function suggestedResources(Rfp $rfp): JsonResponse
    {
        $this->rfpCreateRepository->suggestResources($rfp);

        return $this->ok(
            RfpSuggestedPositionResource::collection(
                $rfp->resources_suggest_api_response['resources'] ?? []
            )
        );
    }

    #[Endpoint("Set Active Step", "Set which step is currently active for the Rfp")]
    #[Response('Currently active step successfully updated', HttpResponse::HTTP_OK)]
    #[Response('Step not allowed for the given Rfp', HttpResponse::HTTP_BAD_REQUEST)]
    public function setActiveStep(RfpSetActiveStepRequest $request, Rfp $rfp): HttpResponse
    {
        $rfp->update(['step' => $request->input('step')]);

        return $this->noContent();
    }

    #[Endpoint("Create tender from Rfp", "Create a new tender from Rfp")]
    #[ResponseFromFile('responses/Assistant/Rfp/Tender/store.json', HttpResponse::HTTP_OK)]
    public function createTender(Rfp $rfp, Request $request): JsonResponse
    {
        try {
            $tender = $this->rfpTenderRepository->createTenderFromRfp($rfp);
        } catch (TenderExistsForRfpException) {
            throw new TenderExistsForRfpAppException($rfp);
        }

        $workspace = $request->user()->workspaces()->firstOrFail();
        RunTenderMatching::dispatch($tender, $workspace, TenderMatchingCompaniesFilter::Selection);
        RunTenderMatching::dispatch($tender, $workspace, TenderMatchingCompaniesFilter::Marketplace);

        $rfp->load('tender');

        return $this->created(RfpTenderResource::make($rfp->tender));
    }
}
