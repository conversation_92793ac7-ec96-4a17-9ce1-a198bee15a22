<?php

namespace App\Http\Controllers\Enterprise;

use App\Http\Controllers\Controller;
use App\Http\Requests\Enterprise\CandidateApplicationUpdateRequest;
use App\Http\Resources\Enterprise\TenderCandidateResource;
use App\Models\Candidate;
use App\Models\Tender;
use App\Repositories\CandidatesRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response as HttpResponse;

class TenderCandidatesController extends Controller
{
    public function __construct(
        protected readonly CandidatesRepository $candidatesRepository,
    ) {}

    public function index(Tender $tender): JsonResponse
    {
        $candidates = $this->candidatesRepository
            ->activeForTender($tender)
            ->with(['vendor.company', 'positions'])
            ->get()
            ->sortBy('name')
            ->sortByDesc(function ($candidate) {
                $position = $candidate->positions->first();
                return optional(optional($position->pivot->matching_api_response)->recursive())->get('score') ?? 0;
            })
            ->values();

        return $this->ok(
            TenderCandidateResource::collection($candidates),
        );
    }

    public function updateApplication(
        Tender $tender,
        Candidate $candidate,
        CandidateApplicationUpdateRequest $request
    ): HttpResponse {
        $application = $candidate->positions()
            ->where('tender_positions.tender_id', $tender->id)
            ->first()?->pivot;

        if (!$application) {
            abort(404, 'Candidate has not applied to this tender');
        }

        $data = $request->validated();
        $application->update($data);

        return $this->noContent();
    }
}
