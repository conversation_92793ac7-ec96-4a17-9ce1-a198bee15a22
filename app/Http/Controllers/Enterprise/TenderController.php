<?php

namespace App\Http\Controllers\Enterprise;

use App\Enums\TenderCandidateStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Enterprise\FilterableDataRequest;
use App\Http\Requests\Enterprise\TenderUpdateRequest;
use App\Http\Resources\Enterprise\TenderFiltersResource;
use App\Http\Resources\Enterprise\TenderResource;
use App\Models\Tender;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\ResponseFromFile;
use Knuckles\Scribe\Attributes\Subgroup;

#[Group('Enterprise')]
#[Subgroup('Tenders')]
class TenderController extends Controller
{
    #[Endpoint('Tenders')]
    #[ResponseFromFile('responses/Tenders/index.json', JsonResponse::HTTP_OK)]
    public function index(FilterableDataRequest $request): AnonymousResourceCollection
    {
        /** @var User $user */
        $user = $request->user();
        $filters = $request->input('filters', []);

        $query = Tender::query()
            ->with([
                'rfp.locations',
                'company',
                'creator',
            ])
            ->withCount([
                'invitedCompanies',
                'invitedCompanies as viewed_companies_count' => function (Builder $query): void {
                    $query->whereNotNull('tender_company_invitations.first_viewed_at');
                }
            ])
            ->when(
                (bool) $user->company_id,
                fn (Builder $query): Builder => $query->where('company_id', $user->company_id),
                fn (Builder $query): Builder => $query->where('created_by', $user->id),
            );

        // Apply filters
        if (!empty($filters['search'])) {
            $query->where(function (Builder $q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }

        if (!empty($filters['status'])) {
            $query->whereIn('status', $filters['status']);
        }

        if (!empty($filters['deadline_from'])) {
            $query->whereDate('submissions_deadline', '>=', $filters['deadline_from']);
        }

        if (!empty($filters['deadline_to'])) {
            $query->whereDate('submissions_deadline', '<=', $filters['deadline_to']);
        }

        return TenderResource::collection(
            $query->latest()->simplePaginate(50)
        );
    }

    #[Endpoint('Tender filter options')]
    public function filterOptions(): JsonResponse
    {
        return $this->ok(TenderFiltersResource::make([]));
    }

    #[Endpoint('Tender show')]
    #[ResponseFromFile('responses/Tenders/show.json', JsonResponse::HTTP_OK)]
    public function show(Tender $tender): JsonResponse
    {
        $tender->load('matches.matchedCompanies');

        return $this->ok(TenderResource::make($tender));
    }

    #[Endpoint('Update tender')]
    public function update(Tender $tender, TenderUpdateRequest $request): JsonResponse
    {
        $data = $request->validated();
        $tender->update($data);

        $tender->refresh();
        $tender->load('matches.matchedCompanies');

        return $this->ok(TenderResource::make($tender));
    }
}
