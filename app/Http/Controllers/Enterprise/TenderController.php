<?php

namespace App\Http\Controllers\Enterprise;

use App\Enums\TenderCandidateStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Enterprise\TenderUpdateRequest;
use App\Http\Resources\Enterprise\TenderResource;
use App\Models\Tender;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Knuckles\Scribe\Attributes\Endpoint;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\ResponseFromFile;
use Knuckles\Scribe\Attributes\Subgroup;

#[Group('Enterprise')]
#[Subgroup('Tenders')]
class TenderController extends Controller
{
    #[Endpoint('Tenders')]
    #[ResponseFromFile('responses/Tenders/index.json', JsonResponse::HTTP_OK)]
    public function index(Request $request): AnonymousResourceCollection
    {
        /** @var User $user */
        $user = $request->user();

        return TenderResource::collection(
            Tender::query()
                ->with([
                    'rfp.locations',
                    'company',
                    'creator',
                ])
                ->withCount([
                    'invitedCompanies',
                    'invitedCompanies as viewed_companies_count' => function (Builder $query): void {
                        $query->whereNotNull('tender_company_invitations.first_viewed_at');
                    }
                ])
                ->when(
                    (bool) $user->company_id,
                    fn (Builder $query): Builder => $query->where('company_id', $user->company_id),
                    fn (Builder $query): Builder => $query->where('created_by', $user->id),
                )
                ->latest()
                ->simplePaginate(50)
        );
    }

    #[Endpoint('Tender show')]
    #[ResponseFromFile('responses/Tenders/show.json', JsonResponse::HTTP_OK)]
    public function show(Tender $tender): JsonResponse
    {
        $tender->load('matches.matchedCompanies');

        return $this->ok(TenderResource::make($tender));
    }

    #[Endpoint('Update tender')]
    public function update(Tender $tender, TenderUpdateRequest $request): JsonResponse
    {
        $data = $request->validated();
        $tender->update($data);

        $tender->refresh();
        $tender->load('matches.matchedCompanies');

        return $this->ok(TenderResource::make($tender));
    }
}
