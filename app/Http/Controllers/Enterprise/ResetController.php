<?php

namespace App\Http\Controllers\Enterprise;

use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Subgroup;
use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Endpoint;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Response;
use Illuminate\Http\Response as HttpResponse;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Workspace;


#[Group("Enterprise")]
class ResetController extends Controller
{
    #[Endpoint("Destroy Workspaces")]
    #[Response('Workspaces and WelcomeScreen record deleted', HttpResponse::HTTP_NO_CONTENT)]
    public function __invoke(Request $request): HttpResponse
    {
        $user = $request->user();

        $user->welcomeScreen()?->delete();

        in_transaction(static function () use ($user): void {
            $user->load('workspaces.users');
            $workspaces = $user->workspaces;

            $user->workspaces()->detach($workspaces->pluck('id'));

            $workspaces->filter(static function (Workspace $workspace) use ($user): bool {
                return $workspace->users->count() === 1 && $workspace->users->first()->is($user);
            })->each(static function (Workspace $workspace): void {
                $workspace->delete();
            });

        });

        return $this->noContent();
    }
}
