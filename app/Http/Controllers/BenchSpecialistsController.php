<?php

namespace App\Http\Controllers;

use App\Exceptions\BenchSpecialistEnlistmentExpiredException;
use App\Http\Requests\Admin\BenchSpecialistStoreRequest;
use App\Http\Requests\Admin\BenchSpecialistUpdateRequest;
use Illuminate\Http\Response;
use <PERSON>nu<PERSON>s\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Subgroup;
use App\Http\Filtering\Filterer;
use App\Http\Resources\BenchSpecialistIndexResource;
use App\Http\Resources\BenchSpecialistShowResource;
use App\Repositories\BenchSpecialistsRepository;
use Illuminate\Http\JsonResponse;

#[Group("platform")]
#[Subgroup("bench")]
class BenchSpecialistsController extends Controller
{
    public function __construct(
        private readonly BenchSpecialistsRepository $benchSpecialistsRepository,
        private readonly Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $specialists = $this->filterer->filterAndPaginate(
            $this->benchSpecialistsRepository->own(),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(BenchSpecialistIndexResource::collection($specialists));
    }

    public function store(BenchSpecialistStoreRequest $request): Response
    {
        $vendor = $request->user()?->vendor;
        abort_if($vendor === null, 403, 'You must be a vendor to enlist specialists.');

        $data = $request->validated();
        $this->benchSpecialistsRepository->enlist(
            $data,
            $vendor
        );

        return $this->created();
    }

    public function show(string $slug): JsonResponse
    {
        $specialist = $this->benchSpecialistsRepository->own()->findBySlugOrFail($slug);

        return $this->ok(BenchSpecialistShowResource::make($specialist));
    }

    /**
     * @throws BenchSpecialistEnlistmentExpiredException
     */
    public function update(string $slug, BenchSpecialistUpdateRequest $request): Response
    {
        $data = $request->validated();
        // TODO(Martin Majernicek): Add own() condition
        $this->benchSpecialistsRepository->update($slug, $data);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->benchSpecialistsRepository->unlistOwn($slug);

        return $this->noContent();
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('profession')->column('candidate.profession')->operator('like'),
            $this->filterer->filterBy('seniority')->column('candidate.seniority')->array(),
            $this->filterer->filterBy('available_from')->operator('>='),
            $this->filterer->filterBy('available_to')->operator('<='),
            $this->filterer->filterBy('min_rate')->column('candidate.rate')->operator('>='),
            $this->filterer->filterBy('max_rate')->column('candidate.rate')->operator('<='),
            $this->filterer->filterBy('technologies')->column('candidate.skills.technology_id')->array(),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('years_of_experience')->column('candidate.years_of_experience')->default('DESC'),
            $this->filterer->sortBy('available_from')->default(),
        ];
    }
}
