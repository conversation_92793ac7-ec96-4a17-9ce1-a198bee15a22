<?php

namespace App\Http\Controllers;

use App\Http\Resources\Company\CompanyResource;
use App\Models\Company;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use <PERSON>nuckles\Scribe\Attributes\Group;
use <PERSON>nuckles\Scribe\Attributes\Subgroup;

#[Group("platform")]
#[Subgroup("company")]
class CompanyClientsController extends Controller
{
    public function index(): JsonResponse
    {
        $companies = Company::query()
            ->whereHas('users', static function (Builder $userQuery): void {
                $userQuery->whereHas('workspaces', static function (Builder $workspaceQuery): void {
                    $workspaceQuery->whereHas('companies', static function (Builder $companyQuery): void {
                        $companyQuery->where('company_id', auth()->user()->company_id);
                    });
                });
            })
            ->get();

        return $this->ok(CompanyResource::collection($companies));
    }
}
