<?php

namespace App\Http\Controllers;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Filtering\Filterer;
use App\Http\Resources\VendorIndexResource;
use App\Http\Resources\VendorShowResource;
use App\Repositories\VendorsRepository;
use Illuminate\Http\JsonResponse;

#[Group("platform")]
#[Subgroup("vendors")]
class VendorsController extends Controller
{
    public function __construct(
        private VendorsRepository $vendorsRepository,
        private Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $featuredVendors = $this->filterer->filterAndPaginate(
            $this->vendorsRepository
                ->publishedPublicOrOwn()
                ->with('company', 'main_industry', 'published_solutions.client.client'),
            $this->filters(),
            $this->sorters(),
        );

        $totalVendorsCount = $this->filterer->filterQuery(
            $this->vendorsRepository->published(),
            $this->filters(),
            $this->sorters(),
        )->count();

        $resource = VendorIndexResource::collection($featuredVendors);
        $resource->additional([
            'meta' => [
                'extra' => [
                    'totalVendorsCount' => $totalVendorsCount,
                ],
            ],
        ]);

        return $this->ok($resource);
    }

    public function show(string $slug): JsonResponse
    {
        $vendor = $this->vendorsRepository
            ->publishedPublicOrOwn()
            ->with(
                'company', 'main_industry', 'industries', 'technologies',
                'clients.client', 'employee_positions', 'published_solutions.client.client',
            )
            ->findBySlugOrFail($slug);

        return $this->ok(VendorShowResource::make($vendor));
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('countries')->column('company.country')->array(),
            $this->filterer->filterBy('technologies')->column('technologies.id')->array(),
            $this->filterer->filterBy('industries')->column('main_industry_id', 'industries.id')->array(),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('public_id')->default(),
        ];
    }
}
