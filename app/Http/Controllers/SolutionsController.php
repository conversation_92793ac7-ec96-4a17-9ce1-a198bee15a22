<?php

namespace App\Http\Controllers;

use Knuckles\Scribe\Attributes\Group;
use App\Http\Filtering\Filterer;
use App\Http\Filtering\Filters\SolutionsCategoryFilter;
use App\Http\Requests\Solutions\SolutionContactRequest;
use App\Http\Requests\Solutions\SolutionRequest;
use App\Http\Resources\SolutionIndexResource;
use App\Http\Resources\SolutionShowResource;
use App\Repositories\SolutionsRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Knuckles\Scribe\Attributes\Subgroup;
use Libs\Overseer\ReviewStatusResource;

#[Group("platform")]
#[Subgroup("solutions")]
class SolutionsController extends Controller
{
    public function __construct(
        private readonly SolutionsRepository $solutionsRepository,
        private readonly Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $solutions = $this->filterer->filterAndPaginate(
            $this->solutionsRepository
                ->publishedOrOwn()
                ->with('cover_resource', 'main_industry', 'vendor.company', 'client.client'),
            $this->filters(),
        );

        return $this->ok(SolutionIndexResource::collection($solutions));
    }

    public function store(SolutionRequest $request): JsonResponse
    {
        $data = $request->validated();
        $solution = $this->solutionsRepository->storeOwn($data);

        return $this->created(SolutionShowResource::make($solution));
    }

    public function show(string $slug): JsonResponse
    {
        $solution = $this->solutionsRepository
            ->publishedOrOwn()
            ->with(
                'cover_resource', 'main_industry', 'industries', 'technologies', 'client',
                'media.resource', 'vendor.company',
            )
            ->findBySlugOrFail($slug);

        return $this->ok(SolutionShowResource::make($solution));
    }

    public function requestContact(SolutionContactRequest $request, string $slug): Response
    {
        $message = $request->string('message');
        $this->solutionsRepository->contactAsCurrentCompany($slug, $message);

        return $this->noContent();
    }

    public function update(SolutionRequest $request, string $slug): JsonResponse
    {
        $data = $request->validated();
        $status = $this->solutionsRepository->updateOwn($slug, $data);

        return $this->accepted(ReviewStatusResource::make($status));
    }

    public function discardUnapprovedChange(string $slug): Response
    {
        $this->solutionsRepository->discardOwnUnapprovedChange($slug);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->solutionsRepository->destroyOwn($slug);

        return $this->noContent();
    }

    private function filters(): array
    {
        return [
            new SolutionsCategoryFilter,
            $this->filterer->filterBy('name')->fulltextSorted(),
        ];
    }
}
