<?php

namespace App\Http\Controllers;

use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Filtering\Filterer;
use App\Http\Resources\TechnologyIndexResource;
use App\Repositories\TechnologiesRepository;
use Illuminate\Http\JsonResponse;

#[Group("platform")]
#[Subgroup("technologies")]
class TechnologiesController extends Controller
{
    public function __construct(
        private TechnologiesRepository $technologiesRepository,
        private Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $technologies = $this->filterer->filter(
            $this->technologiesRepository->query(),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(TechnologyIndexResource::collection($technologies));
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('parent_id')->array(),
            $this->filterer->filterBy('featured'),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('featured')->default('desc'),
            $this->filterer->sortBy('name')->default(),
        ];
    }
}
