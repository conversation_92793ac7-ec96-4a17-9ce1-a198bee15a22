<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Requests\Admin\ClientRequest;
use App\Http\Resources\Admin\ClientIndexResource;
use App\Http\Resources\Admin\ClientShowResource;
use App\Repositories\ClientsRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

#[Group("admin")]
#[Subgroup('clients')]
class ClientsController extends Controller
{
    public function __construct(
        private ClientsRepository $clientsRepository,
        private Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $technologies = $this->filterer->filterAndPaginate(
            $this->clientsRepository->query(),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(ClientIndexResource::collection($technologies));
    }

    public function store(ClientRequest $request): JsonResponse
    {
        $data = $request->validated();
        $technology = $this->clientsRepository->store($data);

        return $this->created(ClientShowResource::make($technology));
    }

    public function show(string $slug): JsonResponse
    {
        $technology = $this->clientsRepository->findBySlugOrFail($slug);

        return $this->ok(ClientShowResource::make($technology));
    }

    public function update(string $slug, ClientRequest $request): Response
    {
        $data = $request->validated();
        $this->clientsRepository->update($slug, $data);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->clientsRepository->destroy($slug);

        return $this->noContent();
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('name')->operator('like'),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('name')->default(),
        ];
    }
}
