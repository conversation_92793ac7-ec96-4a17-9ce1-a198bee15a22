<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use Knu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Requests\Admin\EmployeePositionRequest;
use App\Http\Resources\Admin\EmployeePositionIndexResource;
use App\Http\Resources\Admin\EmployeePositionShowResource;
use App\Repositories\EmployeePositionTypesRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

#[Group("admin")]
#[Subgroup("employee-positions")]
class EmployeePositionsController extends Controller
{
    public function __construct(
        private EmployeePositionTypesRepository $typesRepository,
        private Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $positions = $this->filterer->filterAndPaginate(
            $this->typesRepository->query(),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(EmployeePositionIndexResource::collection($positions));
    }

    public function store(EmployeePositionRequest $request): JsonResponse
    {
        $data = $request->validated();
        $position = $this->typesRepository->store($data);

        return $this->created(EmployeePositionShowResource::make($position));
    }

    public function show(string $slug): JsonResponse
    {
        $position = $this->typesRepository->findBySlugOrFail($slug);

        return $this->ok(EmployeePositionShowResource::make($position));
    }

    public function update(string $slug, EmployeePositionRequest $request): Response
    {
        $data = $request->validated();
        $this->typesRepository->update($slug, $data);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->typesRepository->destroy($slug);

        return $this->noContent();
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('name')->column('name', 'name_plural')->operator('like'),
            $this->filterer->filterBy('hireable'),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('name')->default(),
        ];
    }
}
