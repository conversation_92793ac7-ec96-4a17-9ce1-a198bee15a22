<?php

namespace App\Http\Controllers\Admin;

use Knu<PERSON>s\Scribe\Attributes\Group;
use App\Http\Controllers\Controller;
use App\Repositories\VendorsRepository;
use Barryvdh\DomPDF\PDF;
use Illuminate\Http\Response;
use Knuckles\Scribe\Attributes\Subgroup;

#[Group("admin")]
#[Subgroup("companies/export")]
class VendorExportController extends Controller
{
    public function __construct(
        private VendorsRepository $vendorsRepository,
        private PDF $pdf,
    ) {}

    public function __invoke(string $slug): Response
    {
        $vendor = $this->vendorsRepository->findByCompanySlugOrFail($slug);
        $filename = str_slug($vendor->company->name).'.pdf';

        return $this->pdf
            ->loadView('pdf.vendor', compact('vendor'))
            ->stream($filename);
    }
}
