<?php

namespace App\Http\Controllers\Admin\Traits;

use App\Http\Filtering\Filterer;
use App\Http\Filtering\Filters\AllowedTenderVendorsFilter;
use App\Http\Filtering\Filters\VendorServiceTypeFilter;

/**
 * @property Filterer $filterer
 */
trait TenderVendorsFilterTrait
{
    private function filters(string $slug): array
    {
        return [
            $this->filterer->filterBy('name')->operator('like'),
            $this->filterer->filterBy('client')->column('company.name')->operator('like'),
            new VendorServiceTypeFilter,
            $this->filterer->filterBy('countries')->column('country')->array(),
            $this->filterer->filterBy('industries')->column('main_industry_id', 'industries.id')->array(),
            $this->filterer->filterBy('technologies')->column('technologies.id')->array(),
            new AllowedTenderVendorsFilter($slug),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('company.name')->default(),
        ];
    }
}
