<?php

namespace App\Http\Controllers\Admin\Traits;

use App\Http\Filtering\Filterer;
use App\Http\Filtering\Filters\VendorServiceTypeFilter;
use Libs\Overseer\AwaitingApprovalFilter;

/**
 * @property Filterer $filterer
 */
trait CompaniesFilterTrait
{
    protected function filters(): array
    {
        return [
            $this->filterer->filterBy('name')->operator('like'),
            $this->filterer->filterBy('is_public'),
            $this->filterer->filterBy('is_vendor'),
            $this->filterer->filterBy('publish_status')->column('vendor.publish_status'),
            $this->filterer->filterBy('country'),
            $this->filterer->filterBy('main_industries')->column('vendor.main_industry_id')->array(),
            $this->filterer->filterBy('industries')->column('vendor.main_industry_id', 'vendor.industries.id')->array(),
            $this->filterer->filterBy('technologies')->column('vendor.technologies.id')->array(),
            $this->filterer->filterBy('hq')->operator('like'),
            $this->filterer->filterBy('countries')->column('country')->array(),
            $this->filterer->filterBy('employee_positions')->column('vendor.employee_positions.id')->array(),
            (new AwaitingApprovalFilter)->withRelationship('vendor'),
            new VendorServiceTypeFilter('vendor'),
        ];
    }

    protected function sorters(): array
    {
        return [
            $this->filterer->sortBy('name')->default(),
            $this->filterer->sortBy('created_at'),
        ];
    }
}
