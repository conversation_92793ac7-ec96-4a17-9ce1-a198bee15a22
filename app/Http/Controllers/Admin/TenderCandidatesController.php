<?php

namespace App\Http\Controllers\Admin;

use Knuckles\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Endpoint;
use <PERSON>nuckles\Scribe\Attributes\Subgroup;
use <PERSON>nuckle<PERSON>\Scribe\Attributes\UrlParam;
use Knuckles\Scribe\Attributes\QueryParam;
use Knuckles\Scribe\Attributes\BodyParam;
use Knuckles\Scribe\Attributes\Response as ScribeResponse;
use App\Enums\TenderCandidateStatus;
use App\Exceptions\InvalidTenderCandidateNextStatusException;
use App\Exports\Admin\CandidatesExport;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Filtering\Filters\TenderCandidatesStatusFilter;
use App\Http\Requests\Admin\CandidateRequest;
use App\Http\Requests\Admin\TenderCandidateNotifyRequest;
use App\Http\Requests\Admin\TenderCandidateStatusRequest;
use App\Http\Resources\Admin\TenderCandidateIndexResource;
use App\Http\Resources\Admin\TenderCandidateShowResource;
use App\Models\Candidate;
use App\Repositories\CandidatesRepository;
use App\Repositories\TenderPositionsRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Excel;
use STS\ZipStream\Builder as ZipStream;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

#[Group("admin")]
#[Subgroup("tenders/candidates")]
class TenderCandidatesController extends Controller
{
    public function __construct(
        private CandidatesRepository $candidatesRepository,
        private TenderPositionsRepository $tenderPositionsRepository,
        private Filterer $filterer,
        private Excel $excel,
        private ZipStream $zipStream,
    ) {}

    public function index(string $tenderSlug): JsonResponse
    {
        $candidates = $this->filterer->filterAndPaginate(
            $this->candidatesQuery($tenderSlug),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(TenderCandidateIndexResource::collection($candidates));
    }

    // FIXME: move to a separate controller
    #[Endpoint("Export")]
    public function export(string $tenderSlug): BinaryFileResponse
    {
        $candidates = $this->filterer->filter(
            $this->candidatesQuery($tenderSlug)->with('skills.technology'),
            $this->filters(),
            $this->sorters(),
        );

        // TODO: name of the tender in the file name
        return $this->excel->download(CandidatesExport::make($candidates), 'candidates.xlsx');
    }

    // FIXME: this is just a quick way of downloading zips
    //  create custom services on top of this to move this
    //  from controller to layer below, as well as handle
    //  different sources of the files - we now assume
    //  public disk with local storage
    #[Endpoint("ExportCvs")]
    public function exportCvs(string $tenderSlug): ZipStream
    {
        $candidates = $this->filterer->filter(
            $this->candidatesQuery($tenderSlug)
                ->with('cv_resource')
                ->whereHas('cv_resource'),
            $this->filters(),
            $this->sorters(),
        );

        $zip = $this->zipStream->create('candidates_cvs.zip');

        $candidates->each(fn (Candidate $candidate) => $zip->add(
            storage_path("app/public/{$candidate->cv_resource->path}"),
            $candidate->positions->first()->name.' - '.$candidate->name." ($candidate->public_id).".Str::afterLast($candidate->cv_resource->filename, '.'),
        ));

        return $zip;
    }

    public function show(string $tenderSlug, string $slug, Request $request): JsonResponse
    {
        $technology = $request->boolean('with_unfinished')
            ? $this->candidatesRepository->clonedOfTender($tenderSlug)->with('positions')->findBySlugOrFail($slug)
            : $this->candidatesRepository->clonedFinishedOfTender($tenderSlug)->with('positions')->findBySlugOrFail($slug);

        return $this->ok(TenderCandidateShowResource::make($technology));
    }

    public function update(string $tenderSlug, string $slug, CandidateRequest $request): Response
    {
        $data = $request->validated();
        $this->candidatesRepository->updateOfTender($tenderSlug, $slug, $data);

        return $this->noContent();
    }

    /**
     * @throws InvalidTenderCandidateNextStatusException
     */
    public function updateStatus(string $tenderSlug, string $slug, TenderCandidateStatusRequest $request): Response
    {
        $status = TenderCandidateStatus::from($request->string('status'));
        $rejectionReason = $request->string('rejection_reason');
        $this->tenderPositionsRepository->updateCandidateStatus($tenderSlug, $slug, $status, rejectionReason: $rejectionReason);

        return $this->noContent();
    }

    public function destroy(string $tenderSlug, string $slug): Response
    {
        $this->candidatesRepository->destroyOfTender($tenderSlug, $slug);

        return $this->noContent();
    }

    private function candidatesQuery(string $tenderSlug): Builder
    {
        return $this->candidatesRepository
            ->clonedFinishedOfTender($tenderSlug)
            ->with('vendor.company')
            ->with('positions', fn (BelongsToMany $positionQuery) => $positionQuery->whereHas('tender', fn (Builder $tenderQuery) => $tenderQuery->slug($tenderSlug)),
            );
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('internal_name')->operator('like'),
            $this->filterer->filterBy('profession')->operator('like'),
            $this->filterer->filterBy('min_rate')->column('rate')->operator('>='),
            $this->filterer->filterBy('max_rate')->column('rate')->operator('<='),
            $this->filterer->filterBy('seniority')->array(),
            $this->filterer->filterBy('technologies')->column('skills.technology_id')->array(),
            $this->filterer->filterBy('vendor_id')->column('vendor.public_id'),
            $this->filterer->filterBy('position_id')->column('positions.public_id')->array(),
            new TenderCandidatesStatusFilter,
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('name')->default(),
        ];
    }

    #[Endpoint("Notify selected candidates.", "This endpoint sends an email message to specific candidates associated with a tender.")]
    #[UrlParam("tender", "string", "The slug of the tender associated with the notification.", required: true, example: "4d9E3z")]
    #[QueryParam("filters", "string", "Filters from /admin/tender/{tender}/candidates endpoint to select a subset of all candidates associated with tender.", required: false, example:"?filters%5Bseniority%5D=lead&filters%5Bvendor_id%5D=M7l3VJ")]
    #[BodyParam("message", "string", "The message to be sent to candidates", required: true, example:"Your tender submission has been reviewed.")]
    #[BodyParam("candidate_public_ids", "array", "The array of public_id of candidates to notify. If omitted, notifications will be sent to all candidates associated with the tender.", required: false, example:["7q8w9e", "1a2s3d"])]
    #[ScribeResponse(204, description:"OK")]
    #[ScribeResponse(404, description:"Tender not found")]
    #[ScribeResponse(422, description:"Validation error")]
    public function notify(string $tenderSlug, TenderCandidateNotifyRequest $request): Response
    {
        $candidateIds = $request->input('candidate_public_ids', []);
        if (!empty($candidateIds)) {
            $candidates = Candidate::with(['vendor.company.users'])
                ->whereIn('public_id', $candidateIds)
                ->get();

        } else {
            $candidates = $this->filterer->filterAndPaginate(
                $this->candidatesQuery($tenderSlug)->with(['vendor.company.users']),
                $this->filters(),
                $this->sorters(),
            );
        }
        $users = $candidates->flatMap(fn (Candidate $candidate): Collection => $candidate->vendor->company->users)->all();
        $this->candidatesRepository->notifyUsers($users, $tenderSlug,  $request->string('message'));

        return $this->noContent();
    }
}
