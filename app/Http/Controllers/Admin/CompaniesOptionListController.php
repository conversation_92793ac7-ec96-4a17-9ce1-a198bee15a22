<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use Knu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\CompanyOptionResource;
use App\Repositories\CompaniesRepository;
use Illuminate\Http\JsonResponse;

#[Group("admin")]
#[Subgroup("companies")]
class CompaniesOptionListController extends Controller
{
    public function __construct(
        private CompaniesRepository $companiesRepository,
    ) {}

    public function __invoke(): JsonResponse
    {
        $vendorCompanies = $this->companiesRepository->all();

        return $this->json(CompanyOptionResource::collection($vendorCompanies));
    }
}
