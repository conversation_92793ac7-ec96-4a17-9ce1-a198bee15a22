<?php

namespace App\Http\Controllers\Admin;

use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Requests\Admin\IndustryRequest;
use App\Http\Resources\Admin\IndustryIndexResource;
use App\Http\Resources\Admin\IndustryShowResource;
use App\Repositories\IndustriesRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

#[Group("admin")]
#[Subgroup("industries")]
class IndustriesController extends Controller
{
    public function __construct(
        private IndustriesRepository $industriesRepository,
        private Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $industries = $this->filterer->filterAndPaginate(
            $this->industriesRepository->query(),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(IndustryIndexResource::collection($industries));
    }

    public function store(IndustryRequest $request): JsonResponse
    {
        $data = $request->validated();
        $industry = $this->industriesRepository->store($data);

        return $this->created(IndustryShowResource::make($industry));
    }

    public function show(string $slug): JsonResponse
    {
        $industry = $this->industriesRepository->findBySlugOrFail($slug);

        return $this->ok(IndustryShowResource::make($industry));
    }

    public function update(string $slug, IndustryRequest $request): Response
    {
        $data = $request->validated();
        $this->industriesRepository->update($slug, $data);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->industriesRepository->destroy($slug);

        return $this->noContent();
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('name')->operator('like'),
            $this->filterer->filterBy('parent_id')->array(),
            $this->filterer->filterBy('featured'),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('featured')->default('desc'),
            $this->filterer->sortBy('name')->default(),
        ];
    }
}
