<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\Admin\BenchSpecialistUpdateRequest;
use <PERSON>nuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Subgroup;
use App\Exceptions\BenchSpecialistEnlistmentExpiredException;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Requests\Admin\BenchSpecialistStoreRequest;
use App\Http\Resources\Admin\BenchSpecialistIndexResource;
use App\Http\Resources\Admin\BenchSpecialistShowResource;
use App\Repositories\BenchSpecialistsRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

#[Group("admin")]
#[Subgroup("bench")]
class BenchSpecialistsController extends Controller
{
    public function __construct(
        private readonly BenchSpecialistsRepository $benchSpecialistsRepository,
        private readonly Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $specialists = $this->filterer->filterAndPaginate(
            $this->benchSpecialistsRepository->query(),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(BenchSpecialistIndexResource::collection($specialists));
    }

    public function store(BenchSpecialistStoreRequest $request): Response
    {
        $data = $request->validated();
        $this->benchSpecialistsRepository->enlist($data);

        return $this->created();
    }

    public function show(string $slug): JsonResponse
    {
        $specialist = $this->benchSpecialistsRepository->findBySlugOrFail($slug);

        return $this->ok(BenchSpecialistShowResource::make($specialist));
    }

    /**
     * @throws BenchSpecialistEnlistmentExpiredException
     */
    public function update(string $slug, BenchSpecialistUpdateRequest $request): Response
    {
        $data = $request->validated();
        $this->benchSpecialistsRepository->update($slug, $data);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->benchSpecialistsRepository->destroy($slug);

        return $this->noContent();
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('internal_name')->column('candidate.internal_name')->operator('like'),
            $this->filterer->filterBy('profession')->column('candidate.profession')->operator('like'),
            $this->filterer->filterBy('min_rate')->column('candidate.rate')->operator('>='),
            $this->filterer->filterBy('max_rate')->column('candidate.rate')->operator('<='),
            $this->filterer->filterBy('seniority')->column('candidate.seniority')->array(),
            $this->filterer->filterBy('technologies')->column('candidate.skills.technology_id')->array(),
            $this->filterer->filterBy('vendor_id')->column('candidate.vendor.public_id'),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('name')->column('candidate.name')->default(),
        ];
    }
}
