<?php

namespace App\Http\Controllers\Admin;

use Knu<PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Exceptions\CandidateAlreadyAppliedToTenderPositionException;
use App\Exceptions\CannotApplyToOwnTenderException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\TenderCandidateRequest;
use App\Http\Requests\Admin\TenderPositionRequest;
use App\Http\Resources\Admin\TenderPositionShowResource;
use App\Repositories\TenderPositionsRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

#[Group("admin")]
#[Subgroup("tenders/positions")]
class TenderPositionsController extends Controller
{
    public function __construct(
        private TenderPositionsRepository $tenderPositionsRepository,
    ) {}

    public function show(string $tenderSlug, string $slug): JsonResponse
    {
        $position = $this->tenderPositionsRepository->ofTender($tenderSlug)->findBySlugOrFail($slug);

        return $this->ok(TenderPositionShowResource::make($position));
    }

    public function store(string $tenderSlug, TenderPositionRequest $request): JsonResponse
    {
        $data = $request->validated();
        $position = $this->tenderPositionsRepository->store($tenderSlug, $data);

        return $this->created(TenderPositionShowResource::make($position));
    }

    public function update(string $tenderSlug, string $slug, TenderPositionRequest $request): Response
    {
        $data = $request->validated();
        $this->tenderPositionsRepository->update($tenderSlug, $slug, $data);

        return $this->noContent();
    }

    /**
     * @throws CandidateAlreadyAppliedToTenderPositionException
     * @throws CannotApplyToOwnTenderException
     */
    public function apply(string $tenderSlug, string $slug, TenderCandidateRequest $request): Response
    {
        $data = $request->validated();
        $this->tenderPositionsRepository->apply($tenderSlug, $slug, $data);

        return $this->noContent();
    }

    public function destroy(string $tenderSlug, string $slug): Response
    {
        $this->tenderPositionsRepository->destroy($tenderSlug, $slug);

        return $this->noContent();
    }
}
