<?php

namespace App\Http\Controllers\Admin;

use Knuckles\Scribe\Attributes\Group;
use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Endpoint;
use <PERSON>nuckle<PERSON>\Scribe\Attributes\Subgroup;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\UrlParam;
use Knu<PERSON>s\Scribe\Attributes\QueryParam;
use Knu<PERSON>s\Scribe\Attributes\BodyParam;
use Knuckles\Scribe\Attributes\Response as ScribeResponse;
use App\Http\Controllers\Admin\Traits\TenderVendorsFilterTrait;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Requests\Admin\TenderVendorAllowedRequest;
use App\Http\Requests\Admin\TenderVendorNotifyRequest;
use App\Http\Resources\Admin\TenderVendorIndexResource;
use App\Repositories\TenderVendorsRepository;
use App\Models\Vendor;
use Illuminate\Support\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

#[Group("admin")]
#[Subgroup('tenders/vendors')]
class TenderVendorsController extends Controller
{
    use TenderVendorsFilterTrait;

    public function __construct(
        private TenderVendorsRepository $tenderVendorsRepository,
        private Filterer $filterer,
    ) {}

    public function index(string $slug): JsonResponse
    {
        $vendors = $this->filterer->filterAndPaginate(
            $this->tenderVendorsRepository->vendorsQuery($slug),
            $this->filters($slug),
            $this->sorters(),
        );

        return $this->ok(TenderVendorIndexResource::collection($vendors));
    }

    public function updateAllowed(string $tenderSlug, string $vendorSlug, TenderVendorAllowedRequest $request): Response
    {
        $this->tenderVendorsRepository->updateAllowed($tenderSlug, $vendorSlug, $request->boolean('allowed'));

        return $this->noContent();
    }

    public function bulkUpdateAllowed(string $slug, TenderVendorAllowedRequest $request): Response
    {
        $vendorIds = $this->filterer->filterQuery(
            $this->tenderVendorsRepository->vendorsQuery($slug),
            $this->filters($slug),
            $this->sorters(),
        )->pluck('id');

        $this->tenderVendorsRepository->bulkUpdateAllowed($slug, $vendorIds, $request->boolean('allowed'));

        return $this->noContent();
    }

    #[Endpoint("Notify selected vendors.", "This endpoint sends an email message to specific vendors associated with a tender.")]
    #[UrlParam("tender", "string", "The slug of the tender to associate with this notification.", required: true, example: "4d9E3z")]
    #[QueryParam("filters", "string", "Filters from /admin/tender/{tender}/vendors endpoint to select a subset of all vendors associated with tender.", required: false, example:"?filters[industries]=1&filters[countries]=ro")]
    #[BodyParam("message", "string", "The message to be sent to vendors", required: true, example:"Your tender submission has been reviewed.")]
    #[BodyParam("vendor_public_ids", "array", "The array of public_id of the vendors to notify. If left empty, the notification will be sent to all vendors associated with the tender.", required: false, example:["7q8w9e", "1a2s3d"])]
    #[ScribeResponse(204, description:"OK")]
    #[ScribeResponse(404, description:"Tender not found")]
    #[ScribeResponse(422, description:"Validation error")]
    public function notify(string $tenderSlug, TenderVendorNotifyRequest $request): Response
    {
        $vendorIds = $request->input('vendor_public_ids', []);
        if (!empty($vendorIds)) {
            $vendors = Vendor::with(['company.users'])
                ->whereIn('public_id', $vendorIds)
                ->get();

        } else {
            $vendors = $this->filterer->filterAndPaginate(
                $this->tenderVendorsRepository->vendorsQuery($tenderSlug)->with(['company.users']),
                $this->filters($tenderSlug),
                $this->sorters(),
        );}

        $users = $vendors->flatMap(fn (Vendor $vendor):Collection => $vendor->company->users)->all();
        
        $this->tenderVendorsRepository->notifyUsers($users, $tenderSlug, $request->string('message'));

        return $this->noContent();
    }
}
