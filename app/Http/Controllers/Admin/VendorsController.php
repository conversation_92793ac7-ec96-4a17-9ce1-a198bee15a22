<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use Knu<PERSON>s\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\VendorRequest;
use App\Http\Resources\Admin\VendorShowResource;
use App\Repositories\VendorsRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

#[Group("admin")]
#[Subgroup("companies/vendors")]
class VendorsController extends Controller
{
    public function __construct(
        private VendorsRepository $vendorsRepository,
    ) {}

    public function show(string $slug): JsonResponse
    {
        $vendor = $this->vendorsRepository->findByCompanySlugOrFail($slug);

        return $this->ok(VendorShowResource::make($vendor));
    }

    public function update(string $slug, VendorRequest $request): Response
    {
        $data = $request->validated();
        $this->vendorsRepository->update($slug, $data);

        return $this->noContent();
    }
}
