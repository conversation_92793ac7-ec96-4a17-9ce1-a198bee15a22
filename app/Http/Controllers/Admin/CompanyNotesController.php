<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Requests\Admin\CompanyNoteRequest;
use App\Http\Resources\Admin\CompanyNoteIndexResource;
use App\Repositories\CompanyNotesRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

#[Group("admin")]
#[Subgroup("companies/notes")]
class CompanyNotesController extends Controller
{
    public function __construct(
        private CompanyNotesRepository $companyNotesRepository,
        private Filterer $filterer,
    ) {}

    public function index(string $slug): JsonResponse
    {
        $companies = $this->filterer->paginate(
            $this->companyNotesRepository->query()
                ->with('user')
                ->ofCompany($slug)
                ->latest()
        );

        return $this->ok(CompanyNoteIndexResource::collection($companies));
    }

    public function store(string $slug, CompanyNoteRequest $request): JsonResponse
    {
        $data = $request->validated();
        $company = $this->companyNotesRepository->store($slug, $data);

        return $this->created(CompanyNoteIndexResource::make($company));
    }

    public function update(string $slug, int $id, CompanyNoteRequest $request): Response
    {
        $data = $request->validated();
        $this->companyNotesRepository->update($slug, $id, $data);

        return $this->noContent();
    }

    public function destroy(string $slug, int $id): Response
    {
        $this->companyNotesRepository->destroy($slug, $id);

        return $this->noContent();
    }
}
