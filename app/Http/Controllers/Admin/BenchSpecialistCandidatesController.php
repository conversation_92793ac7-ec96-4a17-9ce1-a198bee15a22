<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CandidateRequest;
use App\Http\Resources\Admin\CandidateShowResource;
use App\Repositories\CandidatesRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

#[Group("admin")]
#[Subgroup("bench")]
class BenchSpecialistCandidatesController extends Controller
{
    public function __construct(
        private readonly CandidatesRepository $candidatesRepository,
    ) {}

    public function show(string $specialistSlug, string $slug, Request $request): JsonResponse
    {
        $technology = $request->boolean('with_unfinished')
            ? $this->candidatesRepository->clonedOfBenchSpecialist($specialistSlug)->findBySlugOrFail($slug)
            : $this->candidatesRepository->clonedFinishedOfBenchSpecialist($specialistSlug)->findBySlugOrFail($slug);

        return $this->ok(CandidateShowResource::make($technology));
    }

    public function update(string $specialistSlug, string $slug, CandidateRequest $request): Response
    {
        $data = $request->validated();
        $this->candidatesRepository->updateOfBenchSpecialist($specialistSlug, $slug, $data);

        return $this->noContent();
    }
}
