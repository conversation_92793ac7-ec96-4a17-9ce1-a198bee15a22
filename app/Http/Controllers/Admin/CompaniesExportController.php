<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Exports\Admin\CompaniesExport;
use App\Http\Controllers\Admin\Traits\CompaniesFilterTrait;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Repositories\CompaniesRepository;
use Maatwebsite\Excel\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

#[Group("admin")]
#[Subgroup("companies/export")]
class CompaniesExportController extends Controller
{
    use CompaniesFilterTrait;

    public function __construct(
        private CompaniesRepository $companiesRepository,
        private Filterer $filterer,
        private Excel $excel,
    ) {}

    public function __invoke(): BinaryFileResponse
    {
        $companies = $this->filterer->filter(
            $this->companiesRepository->query()
                ->with('users'), // FIXME: used to get contact person, but may be expensive in the future
            $this->filters(),
            $this->sorters(),
        );

        return $this->excel->download(CompaniesExport::make($companies), 'companies.xlsx');
    }
}
