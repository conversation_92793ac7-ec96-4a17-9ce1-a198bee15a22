<?php

namespace App\Http\Controllers\Admin;

use Knuckle<PERSON>\Scribe\Attributes\Group;
use Knu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Exceptions\CannotMakeCompanyVendorWithoutProfileException;
use App\Http\Controllers\Admin\Traits\CompaniesFilterTrait;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Requests\Admin\CompanyIsVendorRequest;
use App\Http\Requests\Admin\CompanyNotesRequest;
use App\Http\Requests\Admin\CompanyRequest;
use App\Http\Resources\Admin\CompanyIndexResource;
use App\Http\Resources\Admin\CompanyShowResource;
use App\Repositories\CompaniesRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

#[Group("admin")]
#[Subgroup("companies")]
class CompaniesController extends Controller
{
    use CompaniesFilterTrait;

    public function __construct(
        private CompaniesRepository $companiesRepository,
        private Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $companies = $this->filterer->filterAndPaginate(
            $this->companiesRepository->query()
                ->with(['vendor' => fn ($query) => $query->withExists('unapproved_change')])
                ->withExists(['unapproved_change']),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(CompanyIndexResource::collection($companies));
    }

    public function store(CompanyRequest $request): JsonResponse
    {
        $data = $request->validated();
        $company = $this->companiesRepository->store($data);

        return $this->created(CompanyIndexResource::make($company));
    }

    public function show(string $slug): JsonResponse
    {
        $company = $this->companiesRepository->findBySlugOrFail($slug);

        return $this->ok(CompanyShowResource::make($company));
    }

    public function update(string $slug, CompanyRequest $request): Response
    {
        $data = $request->validated();
        $this->companiesRepository->update($slug, $data);

        return $this->noContent();
    }

    /**
     * @throws CannotMakeCompanyVendorWithoutProfileException
     */
    public function updateIsVendor(string $slug, CompanyIsVendorRequest $request): Response
    {
        $isVendor = $request->validated()['is_vendor'];
        $this->companiesRepository->updateIsVendor($slug, $isVendor);

        return $this->noContent();
    }

    public function updateNotes(string $slug, CompanyNotesRequest $request): Response
    {
        $notes = $request->validated()['notes'];
        $this->companiesRepository->updateNotes($slug, $notes);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->companiesRepository->destroy($slug);

        return $this->noContent();
    }
}
