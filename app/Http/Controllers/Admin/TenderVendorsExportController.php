<?php

namespace App\Http\Controllers\Admin;

use Knuckles\Scribe\Attributes\Group;
use App\Exports\Admin\TenderVendorsExport;
use App\Http\Controllers\Admin\Traits\TenderVendorsFilterTrait;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Repositories\TenderVendorsRepository;
use Knuckles\Scribe\Attributes\Subgroup;
use Maatwebsite\Excel\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

#[Group("admin")]
#[Subgroup("tenders/export")]
class TenderVendorsExportController extends Controller
{
    use TenderVendorsFilterTrait;

    public function __construct(
        private TenderVendorsRepository $tenderVendorsRepository,
        private Filterer $filterer,
        private Excel $excel,
    ) {}

    public function __invoke(string $slug): BinaryFileResponse
    {
        $vendors = $this->filterer->filter(
            $this->tenderVendorsRepository->vendorsQuery($slug)
                ->with('company.users'), // FIXME: used to get contact person, but may be expensive in the future,
            $this->filters($slug),
            $this->sorters(),
        );

        return $this->excel->download(TenderVendorsExport::make($vendors), 'vendors.xlsx');
    }
}
