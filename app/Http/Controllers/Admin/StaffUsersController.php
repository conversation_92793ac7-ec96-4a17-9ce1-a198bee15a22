<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Requests\Admin\StaffUserRequest;
use App\Http\Resources\Admin\StaffUserIndexResource;
use App\Http\Resources\Admin\StaffUserShowResource;
use App\Repositories\UsersRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

#[Group("admin")]
#[Subgroup("users/staff")]
class StaffUsersController extends Controller
{
    public function __construct(
        private UsersRepository $usersRepository,
        private Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $superAdmins = $this->filterer->filterAndPaginate(
            $this->usersRepository->superAdmins(),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(StaffUserIndexResource::collection($superAdmins));
    }

    public function store(StaffUserRequest $request): JsonResponse
    {
        $data = $request->validated();
        $superAdmin = $this->usersRepository->storeSuperAdmin($data);

        return $this->created(StaffUserShowResource::make($superAdmin));
    }

    public function show(string $slug): JsonResponse
    {
        $superAdmin = $this->usersRepository->superAdmins()->findBySlugOrFail($slug);

        return $this->ok(StaffUserShowResource::make($superAdmin));
    }

    public function update(string $slug, StaffUserRequest $request): Response
    {
        $data = $request->validated();
        $this->usersRepository->updateSuperAdmin($slug, $data);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->usersRepository->destroySuperAdmin($slug);

        return $this->noContent();
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('email')->operator('like'),
            $this->filterer->filterBy('name')->column('name', 'surname')->operator('like'),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('name')->default(),
            $this->filterer->sortBy('email'),
        ];
    }
}
