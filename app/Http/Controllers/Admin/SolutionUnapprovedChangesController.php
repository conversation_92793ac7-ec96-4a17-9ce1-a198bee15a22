<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\SolutionUnapprovedChangeRequest;
use App\Repositories\SolutionsRepository;
use Illuminate\Http\Response;
use Libs\Overseer\NoPendingChangesException;

#[Group("admin")]
#[Subgroup("solutions")]
class SolutionUnapprovedChangesController extends Controller
{
    public function __construct(
        private SolutionsRepository $solutionsRepository,
    ) {}

    /**
     * @throws NoPendingChangesException
     */
    public function approve(string $slug, SolutionUnapprovedChangeRequest $request): Response
    {
        $data = $request->validated();
        $this->solutionsRepository->approveChange($slug, $data);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->solutionsRepository->rejectUnapprovedChange($slug);

        return $this->noContent();
    }
}
