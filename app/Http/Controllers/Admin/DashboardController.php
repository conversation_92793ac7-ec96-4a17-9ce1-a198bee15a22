<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\DashboardResource;
use App\Services\ReviewingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

#[Group("admin")]
#[Subgroup("dashboard")]
class DashboardController extends Controller
{
    public function __construct(
        private ReviewingService $reviewingService,
    ) {}

    public function __invoke(Request $request): JsonResponse
    {
        $dashboard = (object) [
            'awaiting_approval_count' => $this->reviewingService->getAwaitingApprovalsCount(),
        ];

        return $this->ok(DashboardResource::make($dashboard));
    }
}
