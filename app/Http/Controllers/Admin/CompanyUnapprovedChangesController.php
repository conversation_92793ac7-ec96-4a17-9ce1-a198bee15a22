<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use Knu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CompanyRequest;
use App\Repositories\CompaniesRepository;
use Illuminate\Http\Response;
use Libs\Overseer\NoPendingChangesException;

#[Group("admin")]
#[Subgroup("companies")]
class CompanyUnapprovedChangesController extends Controller
{
    public function __construct(
        private CompaniesRepository $companiesRepository,
    ) {}

    /**
     * @throws NoPendingChangesException
     */
    public function approve(string $slug, CompanyRequest $request): Response
    {
        $data = $request->validated();
        $this->companiesRepository->approveChange($slug, $data);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->companiesRepository->rejectUnapprovedChange($slug);

        return $this->noContent();
    }
}
