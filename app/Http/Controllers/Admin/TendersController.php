<?php

namespace App\Http\Controllers\Admin;

use Knuckles\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Requests\Admin\TenderRequest;
use App\Http\Resources\Admin\TenderIndexResource;
use App\Http\Resources\Admin\TenderShowResource;
use App\Repositories\TendersRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

#[Group("admin")]
#[Subgroup("tenders")]
class TendersController extends Controller
{
    public function __construct(
        private readonly TendersRepository $tendersRepository,
        private readonly Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $tenders = $this->filterer->filterAndPaginate(
            $this->tendersRepository->query(),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(TenderIndexResource::collection($tenders));
    }

    public function store(TenderRequest $request): JsonResponse
    {
        $data = $request->validated();
        $tender = $this->tendersRepository->store($data);

        return $this->created(TenderShowResource::make($tender));
    }

    public function show(string $slug): JsonResponse
    {
        $tender = $this->tendersRepository->findBySlugOrFail($slug);

        return $this->ok(TenderShowResource::make($tender));
    }

    public function update(string $slug, TenderRequest $request): Response
    {
        $data = $request->validated();
        $this->tendersRepository->update($slug, $data);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->tendersRepository->destroy($slug);

        return $this->noContent();
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('name')->operator('like'),
            $this->filterer->filterBy('client')->column('company.name')->operator('like'),
            $this->filterer->filterBy('service_type'),
            $this->filterer->filterBy('publish_status'),
            $this->filterer->filterBy('status'),
            $this->filterer->filterBy('industries')->column('project.main_industry_id', 'project.industries.id')->array(),
            $this->filterer->filterBy('technologies')->column('project.technologies.id', 'positions.technologies.id')->array(),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('submissions_deadline')->default('desc'),
            $this->filterer->sortBy('status')->array(),
            $this->filterer->sortBy('publish_status')->array(),
            $this->filterer->sortBy('created_at')->array(),
        ];
    }
}
