<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON>s\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Filtering\Filters\AvailableCandidatesForBenchVendorFilter;
use App\Http\Resources\Admin\CandidateOptionResource;
use App\Repositories\CandidatesRepository;
use Illuminate\Http\JsonResponse;

#[Group("admin")]
#[Subgroup("candidates")]
class CandidatesOptionListController extends Controller
{
    public function __construct(
        private readonly CandidatesRepository $candidatesRepository,
        private readonly Filterer $filterer,
    ) {}

    public function __invoke(): JsonResponse
    {
        $candidates = $this->filterer->filter(
            $this->candidatesRepository->finishedOriginal(),
            $this->filters(),
        );

        return $this->json(CandidateOptionResource::collection($candidates));
    }

    private function filters(): array
    {
        return [
            new AvailableCandidatesForBenchVendorFilter,
        ];
    }
}
