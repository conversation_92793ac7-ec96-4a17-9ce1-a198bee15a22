<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\ReviewResource;
use App\Services\ReviewingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;


#[Group("admin")]
#[Subgroup("reviews")]
class ReviewsController extends Controller
{
    public function __construct(
        private ReviewingService $reviewingService,
    ) {}

    public function __invoke(Request $request): JsonResponse
    {
        $reviews = $this->reviewingService->selectAndPaginateAwaitingApprovals();

        return $this->ok(ReviewResource::collection($reviews));
    }
}
