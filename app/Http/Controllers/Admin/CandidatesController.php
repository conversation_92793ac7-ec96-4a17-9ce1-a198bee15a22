<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use Knu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Filtering\Filters\AvailableCandidatesForTenderPositionFilter;
use App\Http\Requests\Admin\CandidateCvRequest;
use App\Http\Requests\Admin\CandidateRequest;
use App\Http\Resources\Admin\CandidateIndexResource;
use App\Http\Resources\Admin\CandidateParseResultResource;
use App\Http\Resources\Admin\CandidateShowResource;
use App\Repositories\CandidatesRepository;
use App\Repositories\TenderPositionsRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

#[Group("admin")]
#[Subgroup("candidates")]
class CandidatesController extends Controller
{
    public function __construct(
        private CandidatesRepository $candidatesRepository,
        private TenderPositionsRepository $tenderPositionsRepository,
        private Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $candidates = $this->filterer->filterAndPaginate(
            $this->candidatesRepository->finishedOriginal(),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(CandidateIndexResource::collection($candidates));
    }

    public function store(CandidateRequest $request): JsonResponse
    {
        $data = $request->validated();
        $technology = $this->candidatesRepository->store($data);

        return $this->created(CandidateShowResource::make($technology));
    }

    public function parse(CandidateCvRequest $request): JsonResponse
    {
        $data = $request->validated();
        $result = $this->candidatesRepository->parse($data);

        $resource = CandidateParseResultResource::make($result);

        return $result->nothingParsed()
            ? $this->ok($resource)
            : $this->created($resource);
    }

    public function show(string $slug, Request $request): JsonResponse
    {
        $technology = $request->boolean('with_unfinished')
            ? $this->candidatesRepository->findOriginalBySlugOrFail($slug)
            : $this->candidatesRepository->findFinishedOriginalBySlugOrFail($slug);

        return $this->ok(CandidateShowResource::make($technology));
    }

    public function update(string $slug, CandidateRequest $request): Response
    {
        $data = $request->validated();
        $this->candidatesRepository->update($slug, $data);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->candidatesRepository->destroy($slug);

        return $this->noContent();
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('internal_name')->operator('like'),
            $this->filterer->filterBy('profession')->operator('like'),
            $this->filterer->filterBy('min_rate')->column('rate')->operator('>='),
            $this->filterer->filterBy('max_rate')->column('rate')->operator('<='),
            $this->filterer->filterBy('seniority')->array(),
            $this->filterer->filterBy('technologies')->column('skills.technology_id')->array(),
            $this->filterer->filterBy('vendor_id')->column('vendor.public_id'),
            new AvailableCandidatesForTenderPositionFilter($this->tenderPositionsRepository),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('name')->default(),
        ];
    }
}
