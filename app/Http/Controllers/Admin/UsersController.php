<?php

namespace App\Http\Controllers\Admin;

use App\Enums\UserRole;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Requests\Admin\UserRequest;
use App\Http\Resources\Admin\UserIndexResource;
use App\Http\Resources\Admin\UserShowResource;
use App\Repositories\UsersRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Subgroup;

#[Group("admin")]
#[Subgroup("users/vendor")]
class UsersController extends Controller
{
    public function __construct(
        private UsersRepository $usersRepository,
        private Filterer        $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $users = $this->filterer->filterAndPaginate(
            $this->usersRepository->users(),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(UserIndexResource::collection($users));
    }

    public function store(UserRequest $request): JsonResponse
    {
        $data = $request->validated();
        $user = $this->usersRepository->storeUser($data['company_id'], $data);

        return $this->created(UserShowResource::make($user));
    }

    public function show(string $slug): JsonResponse
    {
        $user = $this->usersRepository->users()->findBySlugOrFail($slug);

        return $this->ok(UserShowResource::make($user));
    }

    public function update(string $slug, UserRequest $request): Response
    {
        $data = $request->validated();
        $this->usersRepository->updateUser($slug, $data);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->usersRepository->destroyUser($slug);

        return $this->noContent();
    }

    public function roles(): JsonResponse
    {
        return $this->ok(
            collect(UserRole::cases())
                ->reject(fn (UserRole $role): bool => $role === UserRole::SuperAdmin)
                ->values()
        );
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('email')->operator('like'),
            $this->filterer->filterBy('phone')->operator('like'),
            $this->filterer->filterBy('position')->operator('like'),
            $this->filterer->filterBy('name')->column('name', 'surname')->operator('like'),
            $this->filterer->filterBy('company_id')->column('company.public_id'),
            $this->filterer->filterBy('role')->column('roles.name'),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('name')->default(),
            $this->filterer->sortBy('email'),
        ];
    }
}
