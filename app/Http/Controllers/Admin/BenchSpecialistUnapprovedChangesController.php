<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use Knu<PERSON>s\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Repositories\BenchSpecialistsRepository;
use Illuminate\Http\Response;
use Libs\Overseer\NoPendingChangesException;

#[Group("admin")]
#[Subgroup("bench")]
class BenchSpecialistUnapprovedChangesController extends Controller
{
    public function __construct(
        private readonly BenchSpecialistsRepository $benchSpecialistsRepository,
    ) {}

    /**
     * @throws NoPendingChangesException
     */
    public function approve(string $slug): Response
    {
        $this->benchSpecialistsRepository->approveChange($slug);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->benchSpecialistsRepository->rejectUnapprovedChange($slug);

        return $this->noContent();
    }
}
