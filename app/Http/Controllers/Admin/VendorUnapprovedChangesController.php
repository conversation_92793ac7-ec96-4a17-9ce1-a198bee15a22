<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\VendorRequest;
use App\Repositories\VendorsRepository;
use Illuminate\Http\Response;
use Libs\Overseer\NoPendingChangesException;

#[Group("admin")]
#[Subgroup("companies/vendors")]
class VendorUnapprovedChangesController extends Controller
{
    public function __construct(
        private VendorsRepository $vendorsRepository,
    ) {}

    /**
     * @throws NoPendingChangesException
     */
    public function approve(string $slug, VendorRequest $request): Response
    {
        $data = $request->validated();
        $this->vendorsRepository->approveChange($slug, $data);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->vendorsRepository->rejectUnapprovedChange($slug);

        return $this->noContent();
    }
}
