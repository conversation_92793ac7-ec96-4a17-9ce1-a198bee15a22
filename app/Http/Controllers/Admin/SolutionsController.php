<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Requests\Admin\SolutionRequest;
use App\Http\Resources\Admin\SolutionIndexResource;
use App\Http\Resources\Admin\SolutionShowResource;
use App\Repositories\SolutionsRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Libs\Overseer\AwaitingApprovalFilter;

#[Group("admin")]
#[Subgroup("solutions")]
class SolutionsController extends Controller
{
    public function __construct(
        private SolutionsRepository $solutionsRepository,
        private Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $solutions = $this->filterer->filterAndPaginate(
            $this->solutionsRepository->query()
                ->with('vendor.company')
                ->withExists(['unapproved_change']),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(SolutionIndexResource::collection($solutions));
    }

    public function store(SolutionRequest $request): JsonResponse
    {
        $data = $request->validated();
        $solution = $this->solutionsRepository->store($data);

        return $this->created(SolutionShowResource::make($solution));
    }

    public function show(string $slug): JsonResponse
    {
        $solution = $this->solutionsRepository->findBySlugOrFail($slug);

        return $this->ok(SolutionShowResource::make($solution));
    }

    public function update(string $slug, SolutionRequest $request): Response
    {
        $data = $request->validated();
        $this->solutionsRepository->update($slug, $data);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->solutionsRepository->destroy($slug);

        return $this->noContent();
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('name')->operator('like'),
            $this->filterer->filterBy('vendor_id')->column('vendor.public_id'),
            $this->filterer->filterBy('vendor')->column('vendor.company.name')->operator('like'),
            $this->filterer->filterBy('industries')->column('main_industry_id', 'industries.id')->array(),
            $this->filterer->filterBy('technologies')->column('technologies.id')->array(),
            $this->filterer->filterBy('publish_status'),
            new AwaitingApprovalFilter,
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('name')->default(),
            $this->filterer->sortBy('publish_status')->array(),
        ];
    }
}
