<?php

namespace App\Http\Controllers\Admin;

use <PERSON>nuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Requests\Admin\TechnologyRequest;
use App\Http\Resources\Admin\TechnologyIndexResource;
use App\Http\Resources\Admin\TechnologyShowResource;
use App\Repositories\TechnologiesRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

#[Group("admin")]
#[Subgroup("technologies")]
class TechnologiesController extends Controller
{
    public function __construct(
        private TechnologiesRepository $technologiesRepository,
        private Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $technologies = $this->filterer->filterAndPaginate(
            $this->technologiesRepository->query(),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(TechnologyIndexResource::collection($technologies));
    }

    public function store(TechnologyRequest $request): JsonResponse
    {
        $data = $request->validated();
        $technology = $this->technologiesRepository->store($data);

        return $this->created(TechnologyShowResource::make($technology));
    }

    public function show(string $slug): JsonResponse
    {
        $technology = $this->technologiesRepository->findBySlugOrFail($slug);

        return $this->ok(TechnologyShowResource::make($technology));
    }

    public function update(string $slug, TechnologyRequest $request): Response
    {
        $data = $request->validated();
        $this->technologiesRepository->update($slug, $data);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->technologiesRepository->destroy($slug);

        return $this->noContent();
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('name')->operator('like'),
            $this->filterer->filterBy('emsi_id')->operator('like'),
            $this->filterer->filterBy('parent_id')->array(),
            $this->filterer->filterBy('featured'),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('featured')->default('desc'),
            $this->filterer->sortBy('name')->default(),
        ];
    }
}
