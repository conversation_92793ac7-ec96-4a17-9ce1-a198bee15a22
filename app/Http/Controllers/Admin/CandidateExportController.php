<?php

namespace App\Http\Controllers\Admin;

use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Subgroup;
use <PERSON>nuckles\Scribe\Attributes\Endpoint;
use App\Http\Controllers\Controller;
use App\Repositories\CandidatesRepository;
use Barryvdh\DomPDF\PDF;
use Illuminate\Http\Response;

#[Group("admin")]
#[Subgroup("candidates")]
class CandidateExportController extends Controller
{
    public function __construct(
        private CandidatesRepository $candidatesRepository,
        private PDF $pdf,
    ) {}

    #[Endpoint("Export")]
    public function __invoke(string $slug): Response
    {
        $candidate = $this->candidatesRepository
            ->finished()
            ->with('skills.technology')
            ->findBySlugOrFail($slug);
        $filename = str_slug($candidate->name).'.pdf';

        return $this->pdf
            ->loadView('pdf.candidate', compact('candidate'))
            ->stream($filename);
    }
}
