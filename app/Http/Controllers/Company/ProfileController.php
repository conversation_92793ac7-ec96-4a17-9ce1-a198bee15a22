<?php

namespace App\Http\Controllers\Company;

use <PERSON>nu<PERSON>s\Scribe\Attributes\Group;
use App\Http\Controllers\Controller;
use Knuckles\Scribe\Attributes\Subgroup;
use App\Http\Requests\Company\ProfileRequest;
use App\Http\Resources\Company\ProfileResource;
use App\Repositories\CompaniesRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Libs\Overseer\ReviewStatusResource;

#[Group("platform")]
#[Subgroup("company/profile")]
class ProfileController extends Controller
{
    public function __construct(
        private CompaniesRepository $companiesRepository,
    ) {}

    public function show(): JsonResponse
    {
        $company = $this->companiesRepository->findUsersCompany();

        return $this->ok(ProfileResource::make($company));
    }

    public function update(ProfileRequest $request): JsonResponse
    {
        $data = $request->validated();
        $status = $this->companiesRepository->updateOwn($data);

        return $this->accepted(ReviewStatusResource::make($status));
    }

    public function discardUnapprovedChange(): Response
    {
        $this->companiesRepository->discardOwnUnapprovedChange();

        return $this->noContent();
    }
}
