<?php

namespace App\Http\Controllers\Company;

use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Requests\Company\VendorProfileRequest;
use App\Http\Resources\Company\VendorProfileResource;
use App\Repositories\CompaniesRepository;
use App\Repositories\VendorsRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Libs\Overseer\ReviewStatusResource;

#[Group("platform")]
#[Subgroup("company/vendor/profile")]
class VendorProfileController extends Controller
{
    public function __construct(
        private CompaniesRepository $companiesRepository,
        private VendorsRepository $vendorsRepository,
    ) {}

    public function show(): JsonResponse
    {
        $company = $this->companiesRepository->findUsersCompanyWithVendor();

        return $this->ok(VendorProfileResource::make($company));
    }

    public function update(VendorProfileRequest $request): JsonResponse
    {
        $data = $request->validated();
        $status = $this->vendorsRepository->updateOwn($data);

        return $this->accepted(ReviewStatusResource::make($status));
    }

    public function discardUnapprovedChange(): Response
    {
        $this->vendorsRepository->discardOwnUnapprovedChange();

        return $this->noContent();
    }
}
