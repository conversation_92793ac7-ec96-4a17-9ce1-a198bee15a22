<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Http\Filtering\Filterer;
use App\Http\Filtering\Filters\AvailableCandidatesForTenderPositionFilter;
use App\Http\Requests\Company\BenchSpecialistRequest;
use App\Http\Requests\Company\CandidateCvRequest;
use App\Http\Requests\Company\CandidateRequest;
use App\Http\Resources\BenchSpecialistShowResource;
use App\Http\Resources\Company\CandidateIndexResource;
use App\Http\Resources\Company\CandidateParseResultResource;
use App\Http\Resources\Company\CandidateShowResource;
use App\Repositories\BenchSpecialistsRepository;
use App\Repositories\CandidatesRepository;
use App\Repositories\TenderPositionsRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Subgroup;

#[Group("platform")]
#[Subgroup("company/candidates")]
class CandidatesController extends Controller
{
    public function __construct(
        private CandidatesRepository $candidatesRepository,
        private BenchSpecialistsRepository $benchSpecialistsRepository,
        private TenderPositionsRepository $tenderPositionsRepository,
        private Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $candidates = $this->filterer->filterAndPaginate(
            $this->candidatesRepository->ownFinishedOriginal()
                ->with([
                    'activeBenchSpecialist',
                    'activeAssignment.company',
                    'activeAssignment.tender',
                    'activeAssignment.tenderPosition',
                ]),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(CandidateIndexResource::collection($candidates));
    }

    public function parse(CandidateCvRequest $request): JsonResponse
    {
        $data = $request->validated();
        $result = $this->candidatesRepository->parseOwn($data);

        $resource = CandidateParseResultResource::make($result);

        return $result->nothingParsed()
            ? $this->ok($resource)
            : $this->created($resource);
    }

    public function show(string $slug, Request $request): JsonResponse
    {
        $candidate = $request->boolean('with_unfinished')
            ? $this->candidatesRepository->findOwnOriginalBySlugOrFail($slug)
            : $this->candidatesRepository->findOwnFinishedOriginalBySlugOrFail($slug);

        $candidate->loadMissing([
            'activeAssignment.company',
            'activeAssignment.tender',
            'activeAssignment.tenderPosition',
        ]);

        return $this->ok(CandidateShowResource::make($candidate));
    }

    public function enlistToBench(string $slug, BenchSpecialistRequest $request): JsonResponse
    {
        $specialist = $this->benchSpecialistsRepository->enlistOwn($slug, $request->validated());

        return $this->created(BenchSpecialistShowResource::make($specialist));
    }

    public function update(string $slug, CandidateRequest $request): Response
    {
        $data = $request->validated();
        $this->candidatesRepository->updateOwn($slug, $data);

        return $this->noContent();
    }

    public function destroy(string $slug): Response
    {
        $this->candidatesRepository->destroyOwn($slug);

        return $this->noContent();
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('internal_name')->operator('like'),
            $this->filterer->filterBy('profession')->operator('like'),
            $this->filterer->filterBy('min_rate')->column('rate')->operator('>='),
            $this->filterer->filterBy('max_rate')->column('rate')->operator('<='),
            $this->filterer->filterBy('seniority')->array(),
            $this->filterer->filterBy('technologies')->column('skills.technology_id')->array(),
            new AvailableCandidatesForTenderPositionFilter($this->tenderPositionsRepository),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('name')->default(),
        ];
    }
}
