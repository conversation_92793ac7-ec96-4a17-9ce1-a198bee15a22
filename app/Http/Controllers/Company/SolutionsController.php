<?php

namespace App\Http\Controllers\Company;

use <PERSON>nu<PERSON>s\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Controllers\Controller;
use App\Http\Resources\Company\SolutionIndexResource;
use App\Repositories\SolutionsRepository;
use Illuminate\Http\JsonResponse;

#[Group("platform")]
#[Subgroup("company/vendor/solutions")]
class SolutionsController extends Controller
{
    public function __construct(
        private SolutionsRepository $solutionsRepository,
    ) {}

    public function index(): JsonResponse
    {
        $solutions = $this->solutionsRepository
            ->own()
            ->withExists(['unapproved_change'])
            ->orderBy('name')
            ->get();

        return $this->ok(SolutionIndexResource::collection($solutions));
    }
}
