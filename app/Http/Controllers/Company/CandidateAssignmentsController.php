<?php

namespace App\Http\Controllers\Company;

use App\Http\Controllers\Controller;
use App\Http\Requests\Company\CandidateAssignRequest;
use App\Models\Candidate;
use App\Repositories\CandidatesRepository;
use Illuminate\Http\Response;
use <PERSON>nuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Subgroup;

#[Group("platform")]
#[Subgroup("company/candidates/assignments")]
class CandidateAssignmentsController extends Controller
{
    public function __construct(
        private readonly CandidatesRepository $candidatesRepository,
    ) {}

    public function store(Candidate $candidate, CandidateAssignRequest $request): Response
    {
        $this->candidatesRepository->assignOwn(
            $candidate,
            $request->validated(),
        );
        return $this->created();
    }

    public function destroy(Candidate $candidate): Response
    {
        $this->candidatesRepository->unassignOwn($candidate);
        return $this->noContent();
    }
}
