<?php

namespace App\Http\Controllers;

use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Filtering\Filterer;
use App\Http\Resources\ClientIndexResource;
use App\Repositories\ClientsRepository;
use Illuminate\Http\JsonResponse;

#[Group("platform")]
#[Subgroup("clients")]
class ClientsController extends Controller
{
    public function __construct(
        private ClientsRepository $clientsRepository,
        private Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $clients = $this->filterer->filter(
            $this->clientsRepository->query(),
            [],
            $this->sorters(),
        );

        return $this->ok(ClientIndexResource::collection($clients));
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('name'),
        ];
    }
}
