<?php

namespace App\Http\Controllers;

use <PERSON>nu<PERSON>s\Scribe\Attributes\Group;
use <PERSON>nuckles\Scribe\Attributes\Subgroup;
use Illuminate\Http\JsonResponse;

#[Group("platform")]
#[Subgroup("health-check")]

class HealthCheckController extends Controller
{
    public function __invoke(): JsonResponse
    {
        return $this->ok([
            'status' => 'Up and running!',
        ]);
    }
}
