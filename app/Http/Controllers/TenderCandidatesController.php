<?php

namespace App\Http\Controllers;

use Knuckles\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Subgroup;
use App\Enums\TenderCandidateStatus;
use App\Exceptions\InvalidTenderCandidateNextStatusException;
use App\Exceptions\TenderHasEndedException;
use App\Http\Filtering\Filterer;
use App\Http\Filtering\Filters\TenderCandidatesStatusFilter;
use App\Http\Requests\Tenders\TenderCandidateNoteRequest;
use App\Http\Requests\Tenders\TenderCandidateStatusRequest;
use App\Http\Resources\TenderCandidateClientIndexResource;
use App\Http\Resources\TenderCandidateVendorIndexResource;
use App\Models\Tender;
use App\Repositories\CandidatesRepository;
use App\Repositories\TenderPositionsRepository;
use App\Repositories\TendersRepository;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

#[Group("platform")]
#[Subgroup("tenders/candidates")]
class TenderCandidatesController extends Controller
{
    public function __construct(
        private TendersRepository $tendersRepository,
        private TenderPositionsRepository $tenderPositionsRepository,
        private CandidatesRepository $candidatesRepository,
        private Filterer $filterer,
    ) {}

    public function index(string $tenderSlug): JsonResponse
    {
        $tender = $this->tendersRepository->publishedOrOwn()->findBySlugOrFail($tenderSlug);
        $isTenderOwner = $tender->company->id === auth()->user()->company_id;

        $candidates = $this->filterer->filterAndPaginate(
            $this->candidatesQuery($tender, $isTenderOwner),
            $this->filters($isTenderOwner),
            $this->sorters(),
        );

        $data = $isTenderOwner
            ? TenderCandidateClientIndexResource::collection($candidates)
            : TenderCandidateVendorIndexResource::collection($candidates);

        return $this->ok($data);
    }

    /**
     * @throws InvalidTenderCandidateNextStatusException
     * @throws TenderHasEndedException
     */
    public function updateStatus(string $tenderSlug, string $slug, TenderCandidateStatusRequest $request): Response
    {
        $status = TenderCandidateStatus::from($request->string('status'));
        $rejectionReason = $request->string('rejection_reason');
        $this->tenderPositionsRepository->updateOwnTenderCandidateStatus($tenderSlug, $slug, $status, $rejectionReason);

        return $this->noContent();
    }

    public function updateNote(string $tenderSlug, string $slug, TenderCandidateNoteRequest $request): Response
    {
        $note = $request->string('note');
        $this->tenderPositionsRepository->updateOwnTenderCandidateNote($tenderSlug, $slug, $note);

        return $this->noContent();
    }

    private function candidatesQuery(Tender $tender, bool $isTenderOwner): Builder
    {
        $positionsRelationshipScope = function (BelongsToMany|Builder $positionQuery) use ($tender, $isTenderOwner) {
            $positionQuery->whereHas('tender', fn (Builder $tenderQuery) => $tenderQuery->slug($tender->slug));

            if ($isTenderOwner) {
                $positionQuery->whereNotIn('status', [TenderCandidateStatus::AwaitingApproval, TenderCandidateStatus::Rejected]);
            }

            return $positionQuery;
        };

        $query = $this->candidatesRepository
            ->clonedFinishedOfTender($tender->slug)
            ->with('vendor.company')
            ->with('positions', $positionsRelationshipScope)
            ->whereHas('positions', $positionsRelationshipScope);

        if (! $isTenderOwner) {
            $query->whereRelation('vendor.company', 'id', auth()->user()->company_id);
        }

        return $query;
    }

    private function filters(bool $isTenderOwner): array
    {
        return [
            $this->filterer->filterBy($isTenderOwner ? 'name' : 'internal_name')->operator('like'),
            $this->filterer->filterBy('min_rate')->column('rate')->operator('>='),
            $this->filterer->filterBy('max_rate')->column('rate')->operator('<='),
            $this->filterer->filterBy('seniority')->array(),
            $this->filterer->filterBy('technologies')->column('skills.technology_id')->array(),
            $this->filterer->filterBy('position_id')->column('positions.public_id')->array(),
            new TenderCandidatesStatusFilter,
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('name')->default(),
        ];
    }
}
