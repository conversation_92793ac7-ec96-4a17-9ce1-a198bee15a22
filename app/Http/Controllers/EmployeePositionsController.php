<?php

namespace App\Http\Controllers;

use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Group;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Subgroup;
use App\Http\Filtering\Filterer;
use App\Http\Resources\EmployeePositionIndexResource;
use App\Repositories\EmployeePositionTypesRepository;
use Illuminate\Http\JsonResponse;

#[Group("platform")]
#[Subgroup("employee-positions")]
class EmployeePositionsController extends Controller
{
    public function __construct(
        private EmployeePositionTypesRepository $typesRepository,
        private Filterer $filterer,
    ) {}

    public function index(): JsonResponse
    {
        $types = $this->filterer->filter(
            $this->typesRepository->query(),
            $this->filters(),
            $this->sorters(),
        );

        return $this->ok(EmployeePositionIndexResource::collection($types));
    }

    private function filters(): array
    {
        return [
            $this->filterer->filterBy('hireable'),
        ];
    }

    private function sorters(): array
    {
        return [
            $this->filterer->sortBy('name')->default(),
        ];
    }
}
