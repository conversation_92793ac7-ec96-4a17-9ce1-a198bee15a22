<?php

namespace App\Http\Controllers;

use <PERSON>nuckle<PERSON>\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Subgroup;
use App\Http\Resources\TenderDashboardResource;
use App\Repositories\TendersRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;

#[Group("platform")]
#[Subgroup("tenders/dashboard")]
class TendersDashboardController extends Controller
{
    public function __construct(
        private TendersRepository $tendersRepository,
    ) {}

    public function __invoke(): JsonResponse
    {
        $stats = $this->getCurrentCompanyStats();
        $companyTenders = $this->getCurrentCompanyTenders();
        $openTenders = $this->getOpenTenders();
        $completedTenders = $this->getCompletedTenders();

        $data = (object) [
            'stats' => $stats,
            'companyTenders' => $companyTenders,
            'openTenders' => $openTenders,
            'completedTenders' => $completedTenders,
        ];

        return $this->ok(TenderDashboardResource::make($data));
    }

    private function getCurrentCompanyStats(): ?Collection
    {
        if ($companyId = $this->currentUserCompanyId()) {
            return $this->tendersRepository->companyStats($companyId);
        }

        return null;
    }

    private function getCurrentCompanyTenders(): ?Collection
    {
        if ($companyId = $this->currentUserCompanyId()) {
            return $this->tendersRepository
                ->query()
                ->notClosed()
                ->ofCompany($companyId)
                ->orderBy('updated_at', 'desc')
                ->take(3)
                ->get();
        }

        return null;
    }

    private function getOpenTenders(): Collection
    {
        return $this->tendersRepository
            ->published()
            ->notClosed()
            ->notOfCompany($this->currentUserCompanyId())
            ->orderBy('updated_at', 'desc')
            ->take(3)
            ->get();
    }

    private function getCompletedTenders(): Collection
    {
        return $this->tendersRepository
            ->published()
            ->completed()
            ->notOfCompany($this->currentUserCompanyId())
            ->orderBy('updated_at', 'desc')
            ->take(4)
            ->get();
    }

    private function currentUserCompanyId(): ?int
    {
        return auth()->user()->company_id;
    }
}
