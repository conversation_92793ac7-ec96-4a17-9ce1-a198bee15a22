<?php

namespace App\Http\Controllers;

use <PERSON>nuckle<PERSON>\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Subgroup;
use App\Exceptions\CandidateAlreadyAppliedToTenderPositionException;
use App\Exceptions\CannotApplyToOwnTenderException;
use App\Exceptions\TenderNotOpenException;
use App\Http\Requests\Tenders\TenderCandidateRequest;
use App\Http\Resources\TenderPositionShowResource;
use App\Repositories\TenderPositionsRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;

#[Group("platform")]
#[Subgroup("tenders/positions")]
class TenderPositionsController extends Controller
{
    public function __construct(
        private TenderPositionsRepository $tenderPositionsRepository,
    ) {}

    public function show(string $tenderSlug, string $slug): JsonResponse
    {
        $position = $this->tenderPositionsRepository
            ->ofPublishedOrOwnTender($tenderSlug)
            ->findBySlugOrFail($slug);

        return $this->ok(TenderPositionShowResource::make($position));
    }

    /**
     * @throws CandidateAlreadyAppliedToTenderPositionException
     * @throws CannotApplyToOwnTenderException
     * @throws TenderNotOpenException
     */
    public function apply(string $tenderSlug, string $slug, TenderCandidateRequest $request): Response
    {
        $data = $request->validated();
        $this->tenderPositionsRepository->applyAsVendor($tenderSlug, $slug, $data);

        return $this->noContent();
    }
}
