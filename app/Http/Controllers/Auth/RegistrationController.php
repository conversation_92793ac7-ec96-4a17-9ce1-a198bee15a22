<?php

namespace App\Http\Controllers\Auth;

use <PERSON><PERSON><PERSON><PERSON>\Scribe\Attributes\Group;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\RegistrationRequest;
use App\Services\RegistrationService;
use Illuminate\Http\Response;

#[Group("auth")]
class RegistrationController extends Controller
{
    public function __construct(
        private RegistrationService $registrationService,
    ) {}

    public function __invoke(RegistrationRequest $request): Response
    {
        $this->registrationService->register(
            $request->input('company'),
            $request->file('company_documents'),
            $request->input('user')
        );

        return $this->accepted();
    }
}
