<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\EmailRequest;
use App\Repositories\UsersRepository;

class ForgotPasswordController extends Controller
{
    public function __construct(
        private readonly UsersRepository $usersRepository,
    ) {
        //
    }

    public static function show()
    {
        return view('auth.passwords.forgot-password');
    }

    public function store(EmailRequest $request)
    {
        $this->usersRepository->sendPasswordResetEmail($request->input('email'));

        return view('auth.passwords.forgot-password-store');
    }
}
