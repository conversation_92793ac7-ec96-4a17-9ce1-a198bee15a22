<?php

namespace App\Http\Controllers\Auth;

use Illuminate\Support\Facades\Auth;
use <PERSON>nu<PERSON><PERSON>\Scribe\Attributes\Group;
use Knuckles\Scribe\Attributes\Response;
use Knuckles\Scribe\Attributes\Endpoint;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response as HttpResponse;

#[Group("auth")]
class LogoutController extends Controller
{
    #[Endpoint(
        "Logout",
    )]
    #[Response(status: HttpResponse::HTTP_OK)]
    public function __invoke(): HttpResponse | JsonResponse
    {
        try {
            $user = Auth::user()->token();
            $user->revoke();
            return $this->ok();

        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while logging out',
            ], 422);
        }
    }
}
