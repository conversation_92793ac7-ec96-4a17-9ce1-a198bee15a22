<?php

namespace App\Http\Controllers\Auth;

use App\Enums\Platform as PlatformEnum;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use App\Models\Passport\Platform;
use App\Models\User;
use App\Repositories\UsersRepository;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Knuckles\Scribe\Attributes\Group;

#[Group('auth')]
class LoginController extends Controller
{
    public function __construct(
        private readonly UsersRepository $usersRepository,
    ) {
        //
    }

    public function __invoke(): View|RedirectResponse
    {
        if (!session()->has('authClient')) {
            return redirect()->route('welcome');
        }

        if (Auth::check() && session()->has('intended')) {
            return redirect(session('intended'));
        }

        $platform = Platform::query()->where('oauth_client_id', session('authClient'))->firstOrFail();

        return view(
            'auth.login',
            [
                'showRegister' => $platform->platform === PlatformEnum::Vendor,
                'registerUrl' => $platform->platform->registerUrl($platform->url),
            ]
        );
    }

    public function login(LoginRequest $request): RedirectResponse
    {
        try {
            $user = $this->usersRepository->findByEmail($request->input('email'));
        } catch (ModelNotFoundException) {
            User::fakePasswordHashing();

            return $this->unauthorized();
        }

        if (!$user->checkPassword($request->input('password'))) {
            return $this->unauthorized();
        }

        if ($user->company_id && !$user->company?->is_published) {
            return $this->unauthorized(__('auth.unapproved'));
        }

        $platform = Platform::query()->where('oauth_client_id', session('authClient'))->firstOrFail();
        if (!$platform->platform->userCanAccess($user)) {
            return $this->unauthorized(__('auth.forbidden'));
        }

        if (Auth::guard('web')->attempt($request->only('email', 'password'))) {
            session()->forget('authClient');
            session(['intended' => redirect()->intended()->getTargetUrl()]);

            return redirect(session('intended'));
        }

        return $this->unauthorized('The provided credentials do not match our records.');
    }

    private function unauthorized(?string $message = null): RedirectResponse
    {
        return back()->withErrors([
            'email' => $message ?: __('auth.failed'),
        ]);
    }
}
