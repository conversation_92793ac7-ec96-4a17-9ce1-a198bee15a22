<?php

namespace App\Http\Controllers\Auth;

use App\Enums\UserRole;
use App\Exceptions\ResetTokenExpiredException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\PasswordResetRequest;
use App\Models\Passport\Platform;
use App\Models\PasswordReset;
use App\Repositories\UsersRepository;
use Illuminate\Http\Request;

class ResetPasswordController extends Controller
{
    public function __construct(
        private readonly UsersRepository $usersRepository,
    )
    {
        //
    }

    public function show(Request $request)
    {
        $token = $request->string('token');

        if ($token->isEmpty()) {
            return redirect()->route('welcome');
        }

        $passwordReset = PasswordReset::findBy($token);
        if (!$passwordReset) {
            return redirect()->route('welcome');
        }

        return view(
            'auth.passwords.reset-password',
            [
                'email' => $passwordReset->email,
            ]
        );
    }

    public function store(PasswordResetRequest $request)
    {
        try {
            $user = $this->usersRepository->resetPassword(
                $request->input('token'),
                $request->input('password')
            );

            $login = null;
            if ($request->session()->has('authClient')) {
                $login = Platform::query()
                    ->select('url')
                    ->where('oauth_client_id', $request->session()->get('authClient'))
                    ->firstOrFail()
                    ->url;
            } elseif ($user->hasRole(UserRole::Vendor)) {
                $login = Platform::query()
                    ->select('url')
                    ->where('platform', \App\Enums\Platform::Vendor)
                    ->first()
                    ?->url;
            }
            $login ??= route('welcome');

            return view(
                'auth.passwords.password-was-reset',
                [
                    'login' => $login,
                ]
            );
        } catch (ResetTokenExpiredException) {
            return redirect()->route('login');
        }
    }
}
