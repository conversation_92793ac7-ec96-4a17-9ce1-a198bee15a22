<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller as BaseController;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    private ?User $currentUser = null;

    protected function currentUserId(): ?int
    {
        return $this->currentUser()->id;
    }

    protected function currentUser(): ?User
    {
        if (! $this->currentUser) {
            $this->currentUser = auth()->user();
        }

        return $this->currentUser;
    }

    protected function json(mixed $data = null, int $code = 200, bool $wrap = true): Response|JsonResponse
    {
        if ($data instanceof JsonResource) {
            return $data->response()->setStatusCode($code);
        }

        if (! $data) {
            return response()->noContent($code);
        }

        $content = $wrap
            ? ['data' => $data]
            : $data;

        return response()->json($content, $code, [
            'Content-Type' => 'application/json;charset=UTF-8',
            'Charset' => 'utf-8',
        ], JSON_UNESCAPED_UNICODE | JSON_THROW_ON_ERROR);
    }

    protected function ok(mixed $data = null, bool $wrap = true): Response|JsonResponse
    {
        return $this->json($data, 200, $wrap);
    }

    protected function created(mixed $data = null, bool $wrap = true): Response|JsonResponse
    {
        return $this->json($data, 201, $wrap);
    }

    protected function noContent(): Response
    {
        return $this->json(code: 204);
    }

    protected function accepted(mixed $data = null): Response|JsonResponse
    {
        return $this->json($data, code: 202);
    }

    protected function unprocessable(array|string $message = 'Unprocessable Entity',$errors = []): JsonResponse
    {

        return $this->json(
            data:
            [
                'error' => [
                    'message' => $message,
                    'errors' => $errors,
                ]
            ],
            code: 422,
            wrap: false
        );

    }
}
