<?php

namespace App\Http\Filtering;

use App\Http\Filtering\Filters\Filter;
use App\Http\Filtering\Filters\OnTheFlyFilter;
use App\Http\Filtering\Sorters\OnTheFlySorter;
use App\Http\Filtering\Sorters\Sorter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class Filterer
{
    public function filterQuery(Builder $query, Collection|array $filters, Collection|array $sorters = []): Builder
    {
        $state = new FilterState($query, request());

        return $this->executeFiltersAndSorters($state, collect($filters), collect($sorters));
    }

    public function filter(Builder $query, Collection|array $filters, Collection|array $sorters = []): Collection
    {
        return $this->filterQuery($query, $filters, $sorters)->get();
    }

    public function paginate(Builder $query, Collection|array $sorters = [],
        int $defaultPerPage = 10, int $defaultPage = 1): LengthAwarePaginator
    {
        return $this->filterAndPaginate($query, [], $sorters, $defaultPerPage, $defaultPage);
    }

    public function filterAndPaginate(Builder $query, Collection|array $filters, Collection|array $sorters = [],
        int $defaultPerPage = 10, int $defaultPage = 1): LengthAwarePaginator
    {
        $query = $this->filterQuery($query, $filters, $sorters);

        $perPage = max(intval(request('per_page', $defaultPerPage)), 1);
        $page = intval(request('page', $defaultPage));

        return $query->paginate(
            perPage: $perPage,
            page: $page,
        );
    }

    public function filterBy(string $id): OnTheFlyFilter
    {
        return new OnTheFlyFilter($id);
    }

    public function sortBy(string $id): OnTheFlySorter
    {
        return new OnTheFlySorter($id);
    }

    private function executeFiltersAndSorters(FilterState $state, Collection $filters, Collection $sorters): Builder
    {
        $this->executeFilters($state, $filters);
        $this->executeSorters($state, $sorters);

        return $state->query();
    }

    private function executeFilters(FilterState $state, Collection $filters): void
    {
        $filtersByKey = $filters->groupBy(fn (Filter $filter) => $filter->filterKey());
        $filterValues = collect($state->request()->input('filters') ?? []);

        $filterValues
            ->filter(fn ($value) => $value !== null)
            ->each(function ($value, $rawFilterKey) use ($state, $filtersByKey) {
                [$negate, $filterKey] = $this->extractNegateFilterKey($rawFilterKey);
                foreach ($filtersByKey->get($filterKey, []) as $filter) {
                    $filter->filter($state, $value, $negate);
                }
            });

        if ($filterValues->isEmpty()) {
            $filters
                ->filter(fn (Filter $filter) => $filter->defaultValue() !== null)
                ->each(fn (Filter $filter) => $filter->filter($state, $filter->defaultValue(), false));
        }
    }

    private function executeSorters(FilterState $state, Collection $sorters): void
    {
        $sortersByKey = $sorters->groupBy(fn (Sorter $sorter) => $sorter->sorterKey());
        $sortByValues = collect($state->request()->input('sort_by') ?? []);

        $sortByValues
            ->filter(fn ($value) => $value !== null)
            ->each(function ($value, $sorterKey) use ($state, $sortersByKey) {
                foreach ($sortersByKey->get($sorterKey, []) as $sorter) {
                    $sorter->sort($state, $value);
                }
            });

        if ($sortByValues->isEmpty()) {
            $sorters
                ->filter(fn (Sorter $sorter) => $sorter->defaultValue() !== null)
                ->each(fn (Sorter $sorter) => $sorter->sort($state, $sorter->defaultValue()));
        }
    }

    private function extractNegateFilterKey(string $rawFilterKey): array
    {
        $negate = false;
        $filterKey = $rawFilterKey;

        if (str_starts_with($filterKey, '-')) {
            $negate = true;
            $filterKey = substr($filterKey, 1);
        }

        return [$negate, $filterKey];
    }
}
