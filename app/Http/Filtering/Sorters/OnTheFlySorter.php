<?php

namespace App\Http\Filtering\Sorters;

use App\Http\Filtering\FilterState;
use Closure;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OnTheFlySorter implements Sorter
{
    private ?string $column;

    private bool $array = false;

    private ?string $default = null;

    private ?Closure $preparator = null;

    public function __construct(
        private string $id,
    ) {}

    public function sorterKey(): string
    {
        return $this->id;
    }

    public function defaultValue(): ?string
    {
        return $this->default;
    }

    public function sort(FilterState $state, string $value): void
    {
        $value = $this->prepareValue($value);

        $column = $this->getColumn();
        if ($this->isRelationship()) {
            // Only belongs to direct relationships are supported right now.
            // That should cover most of the cases, where parent contains
            // root information.
            $baseColumn = str_after($column, '.');
            $relationship = str_before($column, '.');
            $column = $this->joinRelationship($state, $relationship, $baseColumn);
        }

        if ($this->array) {
            $field = e($column);

            $enums = explode(',', $value);
            $preparedEnums = array_fill(0, count($enums), '?');
            $preparedEnumsString = implode(', ', $preparedEnums);

            $state->query()->orderByRaw("FIELD($field, $preparedEnumsString)", $enums);
        } else {
            $direction = strtolower($value) === 'desc' ? 'desc' : 'asc';
            $state->query()->orderBy($column, $direction);
        }
    }

    private function joinRelationship(FilterState $state, string $relationship, string $column): string
    {
        /** @var BelongsTo $belongsTo */
        $belongsTo = $state->query()->getModel()->{$relationship}();
        $relationshipTable = $belongsTo->getRelated()->getTable();
        $relationshipKey = $belongsTo->getOwnerKeyName();
        $modelTable = $state->query()->getModel()->getTable();
        $modelKey = $belongsTo->getForeignKeyName();

        $aliasColumn = "__order_{$relationship}_{$column}";

        $state->query()->join(
            "{$relationshipTable} as {$relationship}",
            "{$modelTable}.{$modelKey}",
            "{$relationship}.{$relationshipKey}",
        )->select([
            ...$state->query()->getQuery()->columns ?? ["{$modelTable}.*"],
            "{$relationship}.{$column} as {$aliasColumn}",
        ]);

        return $aliasColumn;
    }

    public function column(string $column): self
    {
        $this->column = $column;

        return $this;
    }

    public function array(): self
    {
        $this->array = true;

        return $this;
    }

    public function prepare(Closure $preparator): self
    {
        $this->preparator = $preparator;

        return $this;
    }

    public function default(string $default = 'asc'): self
    {
        $this->default = $default;

        return $this;
    }

    private function getColumn(): string
    {
        return $this->column ?? $this->id;
    }

    private function prepareValue(mixed $value): mixed
    {
        return $this->preparator ? $this->preparator($value) : $value;
    }

    private function isRelationship(): bool
    {
        return str_contains($this->getColumn(), '.');
    }
}
