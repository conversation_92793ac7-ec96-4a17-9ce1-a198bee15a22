<?php

namespace App\Http\Filtering;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class FilterState
{
    public function __construct(
        private Builder $query,
        private Request $request,
    ) {}

    public function query(): Builder
    {
        return $this->query;
    }

    public function request(): Request
    {
        return $this->request;
    }

    public function subQuery(Builder $subQuery): self
    {
        return new FilterState($subQuery, $this->request);
    }
}
