<?php


declare(strict_types=1);

namespace App\Http\Filtering;

use App\Enums\CompanyCategory;
use App\Enums\ComponentType;
use App\Enums\Country;
use App\Enums\Seniority;
use App\Enums\TenderStatus;
use App\Http\Factories\Resources\Forms\ComponentFactory;
use App\Models\Company;
use App\Models\Industry;
use App\Models\Technology;
use App\Models\Tender;
use App\Models\User;
use Illuminate\Support\Collection;

final class FilterBuilder
{
    public static function search(
        string $name = 'Search',
        string $payloadKey = 'search'
    ): array
    {
        return [
            ComponentFactory::create(
                name: $name,
                type: ComponentType::InputText,
                payloadKey: $payloadKey,
                value: ''
            ),
        ];
    }

    public static function seniority(
        string $name = 'Seniority',
        string $payloadKey = 'seniority'
    ): array
    {
        return [
            ComponentFactory::create(
                name: $name,
                type: ComponentType::MultiSelect,
                payloadKey: $payloadKey,
                value: [],
                extraOptions: [
                    'options' => collect(Seniority::cases())
                        ->map(fn(Seniority $seniority): array => [
                            'value' => $seniority->value,
                            'label' => $seniority->trans()
                        ])
                        ->toArray(),
                ]
            ),
        ];
    }

    public static function status(
        string $name = 'Status',
        string $payloadKey = 'status'
    ): array
    {
        return [
            ComponentFactory::create(
                name: $name,
                type: ComponentType::MultiSelect,
                payloadKey: $payloadKey,
                value: [],
                extraOptions: [
                    'options' => collect(TenderStatus::cases())
                        ->map(fn(TenderStatus $status): array => [
                            'value' => $status->value,
                            'label' => $status->name
                        ])
                        ->toArray(),
                ]
            ),
        ];
    }

    public static function categories(
        string $name = 'Category',
        string $payloadKey = 'category'
    ): array
    {
        return [
            ComponentFactory::create(
                name: $name,
                type: ComponentType::MultiSelect,
                payloadKey: $payloadKey,
                value: [],
                extraOptions: [
                    'options' => collect(CompanyCategory::cases())
                        ->map(fn(CompanyCategory $companyCategory): array => [
                            'value' => $companyCategory->value,
                            'label' => $companyCategory->trans()
                        ])
                        ->toArray(),
                ]
            ),
        ];
    }

    public static function countries(
        string $name = 'Residence',
        ?iterable $allowed = null,
        string $payloadKey = 'country'
    ): array
    {
        $allowedSet = collect($allowed);

        $options = collect(Country::cases())
            ->when(
                $allowedSet->isNotEmpty(),
                fn(Collection $all): Collection => $all->filter(
                    fn(Country $country): bool => $allowedSet->contains($country)
                )
            )
            ->map(fn(Country $country): array => ['value' => $country->value, 'label' => $country->trans()])
            ->toArray();

        return [
            ComponentFactory::create(
                name: $name,
                type: ComponentType::MultiSelect,
                payloadKey: $payloadKey,
                value: [],
                extraOptions: ['options' => $options]
            ),
        ];
    }

    public static function technologies(
        ?iterable $technologies,
        string $name = 'Technologies',
        string $payloadKey = 'technologies'
    ): array
    {
        $items = collect($technologies);
        if ($items->isEmpty()) {
            return [];
        }

        $options = $items
            ->map(fn(Technology $technology): array => ['value' => $technology->id, 'label' => $technology->name])
            ->toArray();

        return [
            ComponentFactory::create(
                name: $name,
                type: ComponentType::MultiSelect,
                payloadKey: $payloadKey,
                value: [],
                extraOptions: ['options' => $options]
            ),
        ];
    }

    public static function industries(
        ?iterable $industries,
        string $name = 'Industry',
        string $payloadKey = 'industries'
    ): array
    {
        $items = collect($industries);
        if ($items->isEmpty()) {
            return [];
        }

        $options = $items
            ->map(fn(Industry $industry): array => ['value' => $industry->id, 'label' => $industry->name])
            ->toArray();

        return [
            ComponentFactory::create(
                name: $name,
                type: ComponentType::MultiSelect,
                payloadKey: $payloadKey,
                value: [],
                extraOptions: ['options' => $options]
            ),
        ];
    }

    public static function companies(
        ?iterable $companies,
        string $name = 'Company',
        string $payloadKey = 'company'
    ): array
    {
        $items = collect($companies);
        if ($items->isEmpty()) {
            return [];
        }

        $options = $items
            ->map(fn(Company $company): array => ['value' => $company->id, 'label' => $company->name])
            ->toArray();

        return [
            ComponentFactory::create(
                name: $name,
                type: ComponentType::MultiSelect,
                payloadKey: $payloadKey,
                value: [],
                extraOptions: ['options' => $options]
            ),
        ];
    }

    public static function managers(
        ?iterable $managers,
        string $name = 'Managed By',
        string $payloadKey = 'manager'
    ): array
    {
        $items = collect($managers);
        if ($items->isEmpty()) {
            return [];
        }

        $options = $items
            ->map(function (User $manager): array {
                $suffix = ($manager->division ? ' (' . $manager->division . ')' : '') .
                    ($manager->department ? ' - ' . $manager->department : '');
                return ['value' => $manager->id, 'label' => trim($manager->name . ' ' . $manager->surname . $suffix)];
            })
            ->toArray();

        return [
            ComponentFactory::create(
                name: $name,
                type: ComponentType::MultiSelect,
                payloadKey: $payloadKey,
                value: [],
                extraOptions: ['options' => $options]
            ),
        ];
    }

    public static function tenders(
        ?iterable $tenders,
        string $name = 'Tender',
        string $payloadKey = 'tender'
    ): array
    {
        $items = collect($tenders);
        if ($items->isEmpty()) {
            return [];
        }

        $options = $items
            ->map(fn(Tender $tender): array => ['value' => $tender->id, 'label' => $tender->name])
            ->toArray();

        return [
            ComponentFactory::create(
                name: $name,
                type: ComponentType::MultiSelect,
                payloadKey: $payloadKey,
                value: [],
                extraOptions: ['options' => $options]
            ),
        ];
    }

    public static function rateMinMax(
        string $minName = 'Min Rate',
        string $maxName = 'Max Rate',
        string $minKey = 'min_rate',
        string $maxKey = 'max_rate'
    ): array
    {
        return [
            ComponentFactory::create(
                name: $minName,
                type: ComponentType::Number,
                payloadKey: $minKey,
                value: null,
                extraOptions: ['min' => 0]
            ),
            ComponentFactory::create(
                name: $maxName,
                type: ComponentType::Number,
                payloadKey: $maxKey,
                value: null,
                extraOptions: ['min' => 0]
            ),
        ];
    }

    public static function singleDate(
        string $name,
        string $payloadKey
    ): array
    {
        return [
            ComponentFactory::create(
                name: $name,
                type: ComponentType::SingleDate,
                payloadKey: $payloadKey,
                value: null
            ),
        ];
    }

    public static function profession(
        string $name = 'Profession',
        string $payloadKey = 'profession'
    ): array
    {
        return [
            ComponentFactory::create(
                name: $name,
                type: ComponentType::InputText,
                payloadKey: $payloadKey,
                value: ''
            ),
        ];
    }

    public static function engagedRange(
        string $name = 'Engaged',
        string $rangeKey = 'engagement_period',
        string $startKey = 'engaged_from',
        string $endKey = 'engaged_to',
    ): array {
        return [
            ComponentFactory::create(
                name: $name,
                type: ComponentType::DateRange,
                payloadKey: $rangeKey,
                value: null,
                extraOptions: [
                    'start_key' => $startKey,
                    'end_key'   => $endKey,
                ]
            ),
        ];
    }
}
