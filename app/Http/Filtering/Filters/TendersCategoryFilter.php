<?php

namespace App\Http\Filtering\Filters;

use App\Enums\TenderStatus;
use App\Http\Filtering\FilterState;

class TendersCategoryFilter implements Filter
{
    /**
     * @var array<string, array<Filter>>
     */
    private array $categoryFilters;

    public function __construct()
    {
        $this->categoryFilters = [
            'my' => $this->myTendersFilterer(),
            'open' => $this->openTendersFilterer(),
            'completed' => $this->completedTendersFilterer(),
        ];
    }

    public function filterKey(): string
    {
        return 'category';
    }

    public function defaultValue(): ?string
    {
        return 'none';
    }

    public function filter(FilterState $state, string $value, bool $negate): void
    {
        if ($filters = $this->categoryFilters[$value] ?? null) {
            collect($filters)
                ->filter(fn (Filter $filter) => $filter->defaultValue() !== null)
                ->each(fn (Filter $filter) => $filter->filter($state, $filter->defaultValue(), $negate));
        } else {
            // If invalid category is provided, ensure that no result will be returned
            $state->query()->whereRaw('1 != 1');
        }
    }

    private function myTendersFilterer(): array
    {
        return [(new OnTheFlyFilter('my_category'))
            ->column('company_id')
            ->default($this->currentUserCompanyId()),
        ];
    }

    private function openTendersFilterer(): array
    {
        return [
            (new OnTheFlyFilter('open_category'))
                ->column('status')
                ->array()
                ->default(TenderStatus::Incubation->value.','.TenderStatus::Open->value),
            (new OnTheFlyFilter('open_excluded'))
                ->column('company_id')
                ->operator('!=')
                ->default($this->currentUserCompanyId()),
        ];
    }

    private function completedTendersFilterer(): array
    {
        return [
            (new OnTheFlyFilter('completed_category'))
                ->column('status')
                ->array()
                ->default(TenderStatus::Reviewing->value.','.TenderStatus::Ended->value),
            (new OnTheFlyFilter('completed_excluded'))
                ->column('company_id')
                ->operator('!=')
                ->default($this->currentUserCompanyId()),
        ];
    }

    private function currentUserCompanyId(): ?int
    {
        return auth()->user()->company_id;
    }
}
