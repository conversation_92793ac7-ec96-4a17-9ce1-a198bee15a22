<?php

namespace App\Http\Filtering\Filters;

use App\Http\Filtering\FilterState;
use App\Repositories\TenderPositionsRepository;
use Illuminate\Database\Eloquent\Builder;

class AvailableCandidatesForTenderPositionFilter implements Filter
{
    public function __construct(
        private TenderPositionsRepository $tenderPositionsRepository,
    ) {}

    public function filterKey(): string
    {
        return 'available_for_tender_position';
    }

    public function defaultValue(): ?string
    {
        return null;
    }

    public function filter(FilterState $state, string $value, bool $negate): void
    {
        $whereClones = $negate ? 'whereHas' : 'whereDoesntHave';

        $position = $this->tenderPositionsRepository->findBySlugOrFail($value);

        $state->query()->where(fn (Builder $query) => $query
            ->{$whereClones}('clones', fn (Builder $cloneQuery) => $cloneQuery->whereHas('positions', fn (Builder $positionQuery) => $positionQuery->slug($value))
            )
            ->whereRelation('vendor.company', 'id', $negate ? '=' : '!=', $position->tender->company_id)
        );
    }
}
