<?php

namespace App\Http\Filtering\Filters;

use App\Http\Filtering\FilterState;
use Illuminate\Database\Eloquent\Builder;

class AvailableCandidatesForBenchVendorFilter implements Filter
{
    public function filterKey(): string
    {
        return 'available_for_bench_vendor';
    }

    public function defaultValue(): ?string
    {
        return null;
    }

    public function filter(FilterState $state, string $value, bool $negate): void
    {
        $whereClones = $negate ? 'whereHas' : 'whereDoesntHave';

        $state->query()->where(fn (Builder $query) => $query
            ->whereHas('vendor', fn (Builder $vendorQuery) => $vendorQuery->slug($value))
            ->{$whereClones}('clones', fn (Builder $cloneQuery) => $cloneQuery->whereHas('benchSpecialist', fn (Builder $specialistQuery) => $specialistQuery->active())
            )
        );
    }
}
