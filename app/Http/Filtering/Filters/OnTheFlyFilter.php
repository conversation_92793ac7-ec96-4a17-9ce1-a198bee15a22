<?php

namespace App\Http\Filtering\Filters;

use App\Http\Filtering\FilterState;
use Closure;
use UnexpectedValueException;

class OnTheFlyFilter implements Filter
{
    private ?array $columns;

    private ?string $type = null;

    private string $operator = '=';

    private ?string $default = null;

    private ?Closure $preparator = null;

    public function __construct(
        private string $id,
    ) {}

    public function filterKey(): string
    {
        return $this->id;
    }

    public function defaultValue(): ?string
    {
        return $this->default;
    }

    public function filter(FilterState $state, string $value, bool $negate): void
    {
        $operator = $this->operator;
        $where = 'where';
        $prefix = '';
        $suffix = '';

        // Based on operator, determine any suffix/prefix, such as for like
        switch ($operator) {
            case 'like':
                $prefix = '%';
                $suffix = '%';
                break;
            case 'rlike':
                $prefix = '[';
                $suffix = ']';
                break;
            case 'starts_with':
                $operator = 'like';
                $prefix = '';
                $suffix = '%';
                break;
            case 'ends_with':
                $operator = 'like';
                $prefix = '%';
                $suffix = '';
                break;
        }

        // Attempts to negate condition.
        // Does not work on all cases - if this functionality
        // is not good enough, implement custom filter.
        if ($negate) {
            switch ($operator) {
                case 'like':
                    $operator = 'not like';
                    break;
                case 'rlike':
                    $operator = 'not rlike';
                    break;
                case '=':
                    $operator = '!=';
                    break;
                case '>':
                    $operator = '<=';
                    break;
                case '>=':
                    $operator = '<';
                    break;
                case '<':
                    $operator = '>=';
                    break;
                case '<=':
                    $operator = '>';
                    break;
            }
        }

        // Apply those suffix/prefixes. Also, if type is array,
        // value will be parsed by comma delimiter
        $value = $this->prepareValue($value);
        if ($this->type === 'array') {
            $value = explode(',', $value);
            for ($i = 0; $i < count($value); $i++) {
                $value[$i] = $prefix.$value[$i].$suffix;
            }
        } else {
            $value = $prefix.$value.$suffix;
        }

        $columns = $this->getColumns();

        if (count($columns) === 1) {
            $this->findTargetAndExecuteQueryFilter($state, $columns[0], $value, $where, $operator);
        } else {
            $state->query()->where(fn ($subQuery) => collect($columns)->each(fn ($column) => $subQuery->orWhere(fn ($orWhereQuery) => $this->findTargetAndExecuteQueryFilter(
                $state->subQuery($orWhereQuery), $column, $value, $where, $operator
            )
            )
            )
            );
        }
    }

    /**
     * Determines whether query is column or relationship based,
     * and depending on that executes query filter
     */
    private function findTargetAndExecuteQueryFilter(FilterState $state, string $column, mixed $value, string $where, string $operator): void
    {
        if (! $this->isRelationship($column)) {
            $this->executeQueryFilter($state, $where, $column, $operator, $value);
        } else {
            $parts = explode('.', $column);
            $column = array_pop($parts);
            $relationshipPath = implode('.', $parts);
            $column = $this->getRelationshipTableName($state->query()->getModel(), $parts).'.'.$column;

            $state->query()->whereHas($relationshipPath,
                fn ($subQuery) => $this->executeQueryFilter($state->subQuery($subQuery), $where, $column, $operator, $value)
            );
        }
    }

    /**
     * Performs filtering on a given query builder
     */
    private function executeQueryFilter(FilterState $state, string $where, string $column, string $operator, string|array $values): void
    {
        if ($this->type === 'array') {
            $state->query()->{$where}(function ($subQuery) use ($column, $operator, $values) {
                foreach ($values as $value) {
                    $subQuery->orWhere($column, $operator, $value);
                }
            });
        } elseif ($this->type === 'fulltext' || $this->type === 'fulltext_sorted') {
            $fulltextSearch = $this->getFulltextSearchQuery($state, $column, $values);
            $state->query()->whereRaw($fulltextSearch[0], $fulltextSearch[1]);
            if ($this->type === 'fulltext_sorted') {
                $state->query()->orderByRaw($fulltextSearch[0], $fulltextSearch[1]);
            }
        } else {
            $state->query()->{$where}($column, $operator, $values);
        }
    }

    /**
     * Drills down relationships and returns column name
     */
    private function getRelationshipTableName(mixed $model, array $pathParts): string
    {
        $relationship = $model->{array_shift($pathParts)}();

        if (count($pathParts) > 0) {
            return $this->getRelationshipTableName($relationship->getQuery()->getModel(), $pathParts);
        }

        return $relationship->getModel()->getTable();
    }

    /**
     * Generates fulltext search SQL query for current database system
     *
     *
     * @return array - first value is prepared query, second value is array of arguments
     *
     * @throws UnexpectedValueException
     */
    private function getFulltextSearchQuery(FilterState $state, string|array $columns, string|array $value): array
    {
        $driverName = $state->query()->getConnection()->getDriverName();

        // First make sure that we are always working with array
        if (! is_array($columns)) {
            $columns = [$columns];
        }

        // Encapsulate all fields
        $columns = collect($columns)->map(function ($column) {
            return collect(explode('.', $column))->map(function ($part) {
                return "`$part`";
            })->implode('.');
        })->toArray();

        // Build SQL query for current driver
        $queryString = '';
        $arguments = [];

        if ($driverName === 'mysql') {
            $columnsString = implode(',', $columns);

            $queryString = "MATCH ($columnsString) AGAINST (? IN BOOLEAN MODE)";
            $arguments[] = $this->prepareFulltextSearchTerm($value);
        } else {
            throw new UnexpectedValueException("Driver '$driverName' not supported for fulltext search!");
        }

        return [
            $queryString,
            $arguments,
        ];
    }

    private function prepareFulltextSearchTerm(string $term): string
    {
        // Getting rid of special characters MySQL recognizes in boolean mode
        $reservedSymbols = ['-', '+', '<', '>', '@', '(', ')', '~'];
        $term = str_replace($reservedSymbols, '', $term);

        $words = explode(' ', $term);

        foreach ($words as $key => $word) {
            // Only applying wildcard to words that are indexed by MySQL
            // Two character words are ignored
            if (strlen($word) >= 3) {
                $words[$key] = '*'.$word.'*';
            }
        }

        $searchTerm = implode(' ', $words);

        return $searchTerm;
    }

    public function column(string ...$columns): self
    {
        $this->columns = $columns;

        return $this;
    }

    public function columns(array $columns): self
    {
        $this->columns = $columns;

        return $this;
    }

    public function array(): self
    {
        $this->type = 'array';

        return $this;
    }

    public function fulltext(): self
    {
        $this->type = 'fulltext';

        return $this;
    }

    public function fulltextSorted(): self
    {
        $this->type = 'fulltext_sorted';

        return $this;
    }

    public function operator($operator): self
    {
        $this->operator = strtolower($operator);

        return $this;
    }

    public function prepare(Closure $preparator): self
    {
        $this->preparator = $preparator;

        return $this;
    }

    public function default(?string $default): self
    {
        $this->default = $default;

        return $this;
    }

    private function getColumns(): array
    {
        return $this->columns ?? [$this->id];
    }

    private function prepareValue(mixed $value): mixed
    {
        return $this->preparator ? $this->preparator($value) : $value;
    }

    private function isRelationship(string $column): bool
    {
        return str_contains($column, '.');
    }
}
