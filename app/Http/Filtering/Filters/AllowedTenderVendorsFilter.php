<?php

namespace App\Http\Filtering\Filters;

use App\Http\Filtering\FilterState;
use App\Repositories\TendersRepository;
use Illuminate\Database\Eloquent\Builder;

class AllowedTenderVendorsFilter implements Filter
{
    public function __construct(
        private string $slug,
    ) {}

    public function filterKey(): string
    {
        return 'allowed';
    }

    public function defaultValue(): ?string
    {
        return null;
    }

    public function filter(FilterState $state, string $value, bool $negate): void
    {
        $boolValue = (bool) $value ^ $negate;
        $whereHas = $boolValue ? 'whereHas' : 'whereDoesntHave';

        $tender = app(TendersRepository::class)->findBySlugOrFail($this->slug);

        $state->query()->{$whereHas}(
            'tenders',
            fn (Builder $subQuery) => $subQuery->where('tender_id', $tender->id)->where('allowed', true),
        );
    }
}
