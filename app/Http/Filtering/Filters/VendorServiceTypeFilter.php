<?php

namespace App\Http\Filtering\Filters;

use App\Http\Filtering\FilterState;
use Closure;
use Illuminate\Database\Eloquent\Builder;

class VendorServiceTypeFilter implements Filter
{
    public function __construct(
        private ?string $relationship = null,
    ) {}

    public function filterKey(): string
    {
        return 'service_type';
    }

    public function defaultValue(): ?string
    {
        return null;
    }

    public function filter(FilterState $state, string $value, bool $negate): void
    {
        $withQuery = fn (Closure $callback) => $this->relationship
            ? $state->query()->whereHas($this->relationship, $callback)
            : $callback($state->query());

        if ($value === 'resources') {
            $withQuery(fn (Builder $query) => $query->where('offering_resources', $negate ? '!=' : '=', true));
        } elseif ($value === 'solution') {
            $withQuery(fn (Builder $query) => $query->where('offering_solutions', $negate ? '!=' : '=', true));
        } elseif ($value === 'all') {
            // No need to filter if all option is enabled
            // This if here is to only catch it
        } else {
            // If invalid category is provided, ensure that no result will be returned
            $withQuery(fn (Builder $query) => $query->whereRaw('1 != 1'));
        }
    }
}
