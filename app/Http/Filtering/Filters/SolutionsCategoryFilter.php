<?php

namespace App\Http\Filtering\Filters;

use App\Http\Filtering\FilterState;
use Illuminate\Database\Eloquent\Builder;

class SolutionsCategoryFilter implements Filter
{
    public function filterKey(): string
    {
        return 'category';
    }

    public function defaultValue(): ?string
    {
        return null;
    }

    public function filter(FilterState $state, string $value, bool $negate): void
    {
        // Filtering by all, so we are not scoping
        if ($value === 'all') {
            return;
        }

        // Filtering own solutions, so scoping by own vendor id
        if ($value === 'own') {
            $vendorId = auth()->user()?->vendor?->id;
            $state->query()->where('vendor_id', $vendorId);

            return;
        }

        // Filtering by every other categories besides the featured ones
        if ($value === 'other') {
            $state->query()->whereRelation('main_industry', 'featured', $negate ? '!=' : '=', false);

            return;
        }

        // Otherwise, we will search by provided featured main industry
        $state->query()->where(fn (Builder $subQuery) => $subQuery
            ->whereRelation('main_industry', 'featured', true)
            ->where('main_industry_id', $negate ? '!=' : '=', $value)
        );
    }
}
