<?php

namespace App\Http\Filtering\Filters;

use App\Http\Filtering\FilterState;
use Illuminate\Database\Eloquent\Builder;

class TenderCandidatesStatusFilter implements Filter
{
    public function filterKey(): string
    {
        return 'status';
    }

    public function defaultValue(): ?string
    {
        return null;
    }

    public function filter(FilterState $state, string $value, bool $negate): void
    {
        $whereHas = $negate ? 'whereDoesntHave' : 'whereHas';
        $value = explode(',', $value);

        $state->query()->{$whereHas}('positions', fn (Builder $subQuery) => $subQuery->whereIn('status', $value));
    }
}
