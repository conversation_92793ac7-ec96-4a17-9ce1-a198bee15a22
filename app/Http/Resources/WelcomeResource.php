<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Symfony\Component\Routing\Exception\RouteNotFoundException;

class WelcomeResource extends JsonResource
{
    public static $wrap = null;

    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'title' => 'Platform Api',
            'author' => 'Nordics.io',
            'version' => config('app.version'),
            'documentation' => $this->getDocumentationUrl(),
        ];
    }

    private function getDocumentationUrl()
    {
        try {
            return route('scribe', parameters: true);
        } catch(RouteNotFoundException $e) {
            return url('documentation');
        }

    }
}
