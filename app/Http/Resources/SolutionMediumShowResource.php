<?php

namespace App\Http\Resources;

use App\Models\SolutionMedium;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SolutionMediumShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var SolutionMedium|self $this */
        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type,
            'cover' => $this->cover,
            'source' => $this->source,
        ];
    }
}
