<?php

namespace App\Http\Resources\Enterprise;

use App\Enums\Enterprise\TenderMatchingLocationReason;
use App\Models\TenderMatchCompany;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/** @mixin TenderMatchCompany */
class TenderMatchCompanyResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $matchingReason = collect($this->matching_reason ?? null)->recursive();

        if ($matchingReason->isEmpty()) {
            return [];
        }

        // Rates
        $matchingRateMin = (int) round(data_get($matchingReason, 'rates.rates_min'));
        $matchingRateMax = (int) round(data_get($matchingReason, 'rates.rates_max'));
        if ($matchingRateMin === $matchingRateMax) {
            $matchingRates = $matchingRateMin;
        } else {
            $matchingRates = [
                'min' => $matchingRateMin,
                'max' => $matchingRateMax,
            ];
        }

        // Location
        $matchingLocationEnum = TenderMatchingLocationReason::tryFrom(data_get($matchingReason, 'location.reason'));

        $top3projectsScore = $matchingReason->get('projects', collect())
            ->pluck('score')
            ->sortDesc()
            ->take(3)
            ->avg() ?? 0;

        return [
            'projects_score' => round($top3projectsScore * 100, 1),
            'overall_score' =>  round($this->score * 100, 1),
            'technologies_score' => round($matchingReason->get('tech_score', 0) * 100, 1),
            'matching_rates' => $matchingRates,
            'location_match' => match ($matchingLocationEnum) {
                TenderMatchingLocationReason::DifferentLocation => sprintf(
                    'Different: %s vs %s',
                    strtoupper(data_get($matchingReason, 'location.vendor_country', 'N/A')),
                    strtoupper(data_get($matchingReason, 'location.client_country', 'N/A')),
                ),
                default => Str::headline($matchingLocationEnum?->value),
            },
            'location_score' => round(data_get($matchingReason, 'location.score', 0) * 100, 1),
            'projects_count' => count((array) $matchingReason->get('projects', [])),
            'technologies' => MatchedTechnologyResource::collection(
                $matchingReason->get('evaluated_technologies', [])
            ),
        ];
    }
}
