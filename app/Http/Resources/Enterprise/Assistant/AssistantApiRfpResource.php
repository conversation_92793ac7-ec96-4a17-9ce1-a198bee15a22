<?php

namespace App\Http\Resources\Enterprise\Assistant;

use App\Models\Rfp;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Rfp */
class AssistantApiRfpResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'title' => $this->title,
            'location' => $this->locations?->pluck('id_string')?->toArray(),
            'timeline' => [
                'start_date' => $this->start_date?->format('Y-m-d'),
                'end_date' => $this->end_date?->format('Y-m-d')
            ],
            'timezone' => $this->timezone,
            'project' => [
                'primary_industry' => $this->primaryIndustry?->id_string,
                'description' => $this->description
            ]
        ];
    }
}
