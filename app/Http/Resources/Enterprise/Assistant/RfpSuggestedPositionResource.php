<?php

namespace App\Http\Resources\Enterprise\Assistant;

use App\Enums\Enterprise\TechnologyType;
use App\Models\Technology;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;

class RfpSuggestedPositionResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = collect($this->resource)->recursive();

        return [
            'job_title' => $data->get('job_title'),
            'technologies' => $this->mapTechnologies(
                $data->get('technologies') ?? collect(),
                TechnologyType::Technology
            ),
            'tools' => $this->mapTechnologies(
                $data->get('technologies') ?? collect(),
                TechnologyType::Tool
            ),

        ];
    }

    private function mapTechnologies(Collection $technologies, TechnologyType $type): Collection
    {
        if ($technologies->isEmpty()) {
            return collect();
        }

        return Technology::query()
            ->select(['uuid', 'name'])
            ->whereIn('uuid', $technologies)
            ->where('type', $type)
            ->orderBy('name')
            ->get()
            ->map(fn (Technology $technology): array => [
                'id' => $technology->uuid,
                'name' => $technology->name,
            ]);
    }
}
