<?php

namespace App\Http\Resources\Enterprise\Assistant;

use App\Enums\ComponentType;
use App\Enums\Enterprise\IndustryType;
use App\Enums\Enterprise\Timezone;
use App\Http\Factories\Resources\Forms\ComponentFactory;
use App\Models\Industry;
use App\Models\Location;
use App\Models\Rfp;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Rfp */
class RfpInfoFormResource extends JsonResource
{
    /**
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            "form" => "standard",
            "data" => [
                ComponentFactory::create(
                    name: 'RFP Title',
                    type: ComponentType::InputText,
                    payloadKey: 'title',
                    value: $this->title
                ),
                ComponentFactory::create(
                    name: 'Preferred Vendor Location',
                    type: ComponentType::MultiSelect,
                    payloadKey: 'location',
                    value: $this->locations->pluck('id_string')->toArray(),
                    extraOptions: [
                        'options' => Location::all()
                            ->map(fn(Location $location): array => [
                                'value' => $location->id_string,
                                'label' => $location->name
                            ])
                            ->toArray(),
                    ]
                ),
                ComponentFactory::create(
                    name: 'Time Zone',
                    type: ComponentType::Select,
                    payloadKey: 'timezone',
                    value: $this->timezone,
                    extraOptions: [
                        'options' => array_map(fn(Timezone $tz): array => [
                            'value' => $tz->value,
                            'label' => $tz->value
                        ], Timezone::cases())
                    ]
                ),
                ComponentFactory::create(
                    name: 'Expected Start & End Date',
                    type: ComponentType::DateRange,
                    payloadKey: 'timeline',
                    value: ($this->start_date === null && $this->end_date === null)
                        ? null
                        : [
                            'start_date' => optional($this->start_date)->format('Y-m-d'),
                            'end_date' => optional($this->end_date)->format('Y-m-d')
                        ]
                ),
                ComponentFactory::create(
                    name: 'Project Industry',
                    type: ComponentType::Select,
                    payloadKey: 'primary_industry',
                    value: optional($this->primaryIndustry)->id_string,
                    extraOptions: [
                        'options' => Industry::where('type', IndustryType::Primary)
                            ->get()
                            ->map(fn(Industry $industry): array => [
                                'value' => $industry->id_string,
                                'label' => $industry->name
                            ])
                            ->toArray(),
                    ]
                ),
                ComponentFactory::create(
                    name: 'Project Description',
                    type: ComponentType::Textarea,
                    payloadKey: 'description',
                    value: $this->description
                )
            ]
        ];
    }
}
