<?php

namespace App\Http\Resources\Enterprise\Assistant;

use App\Models\Rfp;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Rfp */
class RfpResource extends JsonResource
{
    /**
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'created_by' => $this->author->display_name,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'files' => collect($this->files ?? [])
                ->map(fn(string $path): array => [
                    'url' => asset('storage/' . $path),
                    'name' => basename($path)
                ])
                ->toArray(),
            'step' => $this->step,
            'tender_id' => $this->whenLoaded('tender', fn (): string => $this->tender->public_id),
        ];
    }
}
