<?php

namespace App\Http\Resources\Enterprise\Assistant;

use App\Enums\ComponentType;
use App\Enums\Enterprise\SeniorityLevel;
use App\Enums\Enterprise\TechnologyType;
use App\Enums\Enterprise\WorkLocation;
use App\Http\Factories\Resources\Forms\ComponentFactory;
use App\Models\Language;
use App\Models\Rfp;
use App\Models\RfpPosition;
use App\Models\Technology;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Rfp */
class RfpResourcesFormResource extends JsonResource
{
    /**
     * Transform the RFP model into a structured JSON array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            'form' => 'collection',
            'data' => $this->rfpPositions->map(function (RfpPosition $position): array {
                return [
                    'id' => $position->id,
                    'body' => [
                        ComponentFactory::create(
                            name: 'Job Title',
                            type: ComponentType::InputText,
                            payloadKey: 'job_title',
                            value: $position->job_title
                        ),
                        ComponentFactory::create(
                            name: 'Number of Resources',
                            type: ComponentType::Number,
                            payloadKey: 'number_of_resources',
                            value: $position->number_of_resources,
                            defaultValue: 1
                        ),
                        ComponentFactory::create(
                            name: 'Seniority Level',
                            type: ComponentType::Select,
                            payloadKey: 'seniority_level',
                            value: $position->seniority_level,
                            extraOptions: [
                                'options' => array_map(fn(SeniorityLevel $seniority): array => [
                                    'value' => $seniority->value,
                                    'label' => $seniority->value
                                ], SeniorityLevel::cases()),
                            ]
                        ),
                        ComponentFactory::create(
                            name: 'Workload',
                            type: ComponentType::Slider,
                            payloadKey: 'workload',
                            value: $position->workload,
                            defaultValue: 100,
                            extraOptions: [
                                'min' => 0,
                                'max' => 100,
                            ]
                        ),
                        ComponentFactory::create(
                            name: 'Languages',
                            type: ComponentType::MultiSelect,
                            payloadKey: 'languages',
                            value: $position->languages->pluck('id_string')->toArray(),
                            defaultValue: ['en'],
                            extraOptions: [
                                'options' => Language::all()
                                    ->map(fn(Language $language): array => [
                                        'value' => $language->id_string,
                                        'label' => $language->name
                                    ])
                                    ->toArray(),
                            ]
                        ),
                        ComponentFactory::create(
                            name: 'Hourly Rate Expectations',
                            type: ComponentType::NumberRange,
                            payloadKey: 'hourly_rate_expectations',
                            value: ($position->rate_min === null && $position->rate_max === null)
                                ? null
                                : [
                                    $position->rate_min,
                                    $position->rate_max
                                ],
                            defaultValue: [20, 300],
                            extraOptions: [
                                'min' => 0,
                            ]
                        ),
                        ComponentFactory::create(
                            name: 'Technology Stack',
                            type: ComponentType::MultiSelect,
                            payloadKey: 'technologies',
                            value: $position->technologies->pluck('uuid')->toArray(),
                            extraOptions: [
                                'options' => Technology::where('type', TechnologyType::Technology)
                                    ->orderBy('name')
                                    // Send 100 to show at least some items in the dropdown.
                                    // This will be replaced with a different form component later.
                                    ->take(100)
                                    ->get()
                                    ->map(fn(Technology $tech): array => [
                                        'value' => $tech->uuid,
                                        'label' => $tech->name
                                    ])
                                    ->toArray(),
                                'selected_options' => $position->technologies
                                    ->map(fn(Technology $technology): array => [
                                        'value' => $technology->uuid,
                                        'label' => $technology->name
                                    ])
                                    ->toArray(),
                                'options_endpoint' => [
                                    'url' => route('enterprise.technologies.index', [
                                        'type' => TechnologyType::Technology->value
                                    ]),
                                    'mapping' => [
                                        'value' => 'id',
                                        'label' => 'name'
                                    ]
                                ]
                            ]
                        ),
                        ComponentFactory::create(
                            name: 'Must-have Technology',
                            type: ComponentType::Select,
                            payloadKey: 'mandatory_technology',
                            value: $position->technologies()->wherePivot('is_mandatory', true)->first()?->uuid,
                            extraOptions: [
                                'options' => $position->technologies
                                    ->map(fn(Technology $technology): array => [
                                        'value' => $technology->uuid,
                                        'label' => $technology->name
                                    ])
                                    ->toArray(),
                                'options_from' => [
                                    'payload_key' => 'technologies',
                                    'label' => 'Technology Stack'
                                ]
                            ]
                        ),
                        ComponentFactory::create(
                            name: 'Key Tools',
                            type: ComponentType::MultiSelect,
                            payloadKey: 'key_tools',
                            value: $position->tools->pluck('uuid')->toArray(),
                            extraOptions: [
                                'options' => Technology::where('type', TechnologyType::Tool)
                                    ->orderBy('name')
                                    // Send 100 to show at least some items in the dropdown.
                                    // This will be replaced with a different form component later.
                                    ->take(100)
                                    ->get()
                                    ->map(fn(Technology $tech): array => [
                                        'value' => $tech->uuid,
                                        'label' => $tech->name
                                    ])
                                    ->toArray(),
                                'selected_options' => $position->tools
                                    ->map(fn(Technology $technology): array => [
                                        'value' => $technology->uuid,
                                        'label' => $technology->name
                                    ])
                                    ->toArray(),
                                'options_endpoint' => [
                                    'url' => route('enterprise.technologies.index', [
                                        'type' => TechnologyType::Tool->value
                                    ]),
                                    'mapping' => [
                                        'value' => 'id',
                                        'label' => 'name'
                                    ]
                                ]
                            ]
                        ),
                        ComponentFactory::create(
                            name: 'Work location',
                            type: ComponentType::Select,
                            payloadKey: 'work_location',
                            value: $position->work_location,
                            extraOptions: [
                                'options' => array_map(fn(WorkLocation $workLocation): array => [
                                    'value' => $workLocation->value,
                                    'label' => $workLocation->value
                                ], WorkLocation::cases()),
                            ]
                        ),
                        ComponentFactory::create(
                            name: 'Notes',
                            type: ComponentType::Textarea,
                            payloadKey: 'notes',
                            value: $position->notes,
                        )
                    ]
                ];
            })
        ];
    }
}
