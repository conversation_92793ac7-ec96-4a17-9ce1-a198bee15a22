<?php

namespace App\Http\Resources\Enterprise;

use App\Models\TenderCompanyInvitation;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin TenderCompanyInvitation */
class TenderCompanyInvitationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $matchDetails = $this->match_details;
        return [
            ...CompanyResource::make($this)->toArray($request),

            'employee_emails' => $this->when(
                $this->is_in_workspace,
                fn() => $this->users->pluck('email')
            ),

            'match_details' => $this->when(
                $matchDetails,
                fn () => TenderMatchCompanyResource::make($matchDetails)->toArray($request)
            ),

            'sent_at' => $this->invitation->sent_at?->format('Y-m-d'),
            'first_viewed_at' => $this->invitation->first_viewed_at,
            'candidates_count' => $this->whenNotNull($this->candidates_count),
        ];
    }
}
