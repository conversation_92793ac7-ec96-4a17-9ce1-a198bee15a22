<?php

declare(strict_types=1);

namespace App\Http\Resources\Enterprise;

use App\Enums\TenderStatus;
use App\Http\Filtering\FilterBuilder;
use Illuminate\Http\Resources\Json\JsonResource;

class TenderFiltersResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            ...FilterBuilder::search(),
            ...FilterBuilder::status(),
            ...FilterBuilder::singleDate('Deadline from', 'deadline_from'),
            ...FilterBuilder::singleDate('Deadline to', 'deadline_to'),
        ];
    }
}
