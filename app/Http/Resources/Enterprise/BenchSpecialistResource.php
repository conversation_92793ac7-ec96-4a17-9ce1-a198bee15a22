<?php

namespace App\Http\Resources\Enterprise;

use App\Models\BenchSpecialist;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin BenchSpecialist */
class BenchSpecialistResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->public_id,
            'available_from' => $this->available_from,
            'available_to' => $this->available_to,

            'candidate' => $this->whenLoaded('candidate', fn (): array => [
                'name' => $this->candidate->name,
                'profession' => $this->candidate->profession,
                'seniority' => $this->candidate->seniority,
                'rate' => $this->candidate->rate,
                'country' => $this->candidate->country->trans(),
            ]),

            'company' => $this->when(
                $this->relationLoaded('candidate') &&
                $this->candidate->relationLoaded('vendor') &&
                $this->candidate->vendor->relationLoaded('company'),
                fn (): array => [
                    'id' => $this->candidate->vendor->company->public_id,
                    'name' => $this->candidate->vendor->company->name,
                    'category' => $this->candidate->vendor->company->category,
                    'country' => $this->candidate->vendor->company->country->trans(),
                    'headquarters' => $this->candidate->vendor->company->hq,
                ]
            )
        ];
    }
}
