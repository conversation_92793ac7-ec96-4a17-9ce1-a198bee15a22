<?php

namespace App\Http\Resources\Enterprise;

use App\Models\Technology;
use Illuminate\Http\Resources\Json\JsonResource;

class MatchedTechnologyResource extends JsonResource
{
    /**
     * @param array $request format:
     * [
     *   "_id" => string, // Technology::uuid
     *   "score" => float,
     *   "found_by_ids" => string[], // Technology::uuid[]
     *  ]
     */
    public function toArray($request): array
    {
        $techData = $this->resource;
        $allIds = collect([
            $techData['_id'] ?? $techData['id'],
            ...($techData['found_by_ids'] ?? [])
        ])->filter()->unique();

        $technologiesById = Technology::whereIn('uuid', $allIds)->get()->keyBy('uuid');

        $techModel = $technologiesById->get($techData['_id'] ?? $techData['id']);
        $foundByNames = collect($techData['found_by_ids'] ?? [])
            ->map(fn($uuid) => $technologiesById->get($uuid)?->name)
            ->filter()
            ->values()
            ->toArray();

        return [
            'name' => $techModel?->name ?? 'Unknown',
            'score' => round(($techData['score'] ?? 0) * 100, 1),
            'found_by' => $foundByNames,
        ];
    }
}
