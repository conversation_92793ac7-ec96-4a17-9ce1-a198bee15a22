<?php

namespace App\Http\Resources\Enterprise\Candidate;

use App\Models\CandidateExperience;
use App\Models\CandidateSkill;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\FileResource;

class CandidateShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Candidate|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'finished' => $this->finished,

            'internal_name' => $this->internal_name,
            'name' => $this->name,
            'country' => $this->country->trans(),
            'city' => $this->city,
            'rate' => $this->rate,

            'profession' => $this->profession,
            'seniority' => $this->seniority,
            'last_job_title' => $this->last_job_title,
            'years_of_experience' => $this->years_of_experience,
            'highest_education' => $this->highest_education,
            'field_of_study' => $this->field_of_study,

            'cv' => FileResource::make($this->cv_resource),

            'skills' => $this->skills->sortByDesc('years_of_experience')
                ->map(fn (CandidateSkill $skill) => [
                    'technology' => $skill->technology->name,
                    'years_of_experience' => $skill->years_of_experience,
                ])
                ->values(),

            'experiences' => $this->experiences->sortByDesc('order')
                ->map(fn (CandidateExperience $experience) => [
                    'id' => $experience->public_id,
                    'name' => $experience->name,
                    'description' => $experience->description,
                    'length' => $experience->length_type->transChoice($experience->length),
                ])
                ->values(),

            'vendor' => [
                'id' => $this->vendor->public_id,
                'slug' => $this->vendor->slug,
                'company_slug' => $this->vendor->company->slug,
                'name' => $this->vendor->company->name,
            ],
        ];
    }
}
