<?php

namespace App\Http\Resources\Enterprise\Candidate;

use App\Enums\ComponentType;
use App\Enums\Country;
use App\Enums\Seniority;
use App\Http\Factories\Resources\Forms\ComponentFactory;
use App\Models\Company;
use App\Models\Technology;
use App\Models\Tender;
use App\Models\User;
use Illuminate\Http\Resources\Json\JsonResource;

class AssignedCandidateFiltersResource extends JsonResource
{
    public function toArray($request): array
    {
        $filters = [
            ComponentFactory::create(
                name: 'Search',
                type: ComponentType::InputText,
                payloadKey: 'search',
                value: ''
            ),
            ComponentFactory::create(
                name: 'Seniority',
                type: ComponentType::MultiSelect,
                payloadKey: 'seniority',
                value: [],
                extraOptions: [
                    'options' => collect(Seniority::cases())
                        ->map(fn(Seniority $seniority): array => [
                            'value' => $seniority->value,
                            'label' => $seniority->trans(),
                        ])
                        ->toArray(),
                ]
            ),
        ];

        if (!collect($this->resource['companies'])->isEmpty()) {
            $filters[] = ComponentFactory::create(
                name: 'Company',
                type: ComponentType::MultiSelect,
                payloadKey: 'company',
                value: [],
                extraOptions: [
                    'options' => collect($this->resource['companies'])
                        ->map(fn(Company $company): array => [
                            'value' => $company->id,
                            'label' => $company->name,
                        ])
                        ->toArray(),
                ]
            );
        }

        if (!collect($this->resource['technologies'] ?? [])->isEmpty()) {
            $filters[] = ComponentFactory::create(
                name: 'Technologies',
                type: ComponentType::MultiSelect,
                payloadKey: 'technologies',
                value: [],
                extraOptions: [
                    'options' => collect($this->resource['technologies'])
                        ->map(fn(Technology $technology): array => [
                            'value' => $technology->id,
                            'label' => $technology->name,
                        ])
                        ->toArray(),
                ]
            );
        }

        if (!collect($this->resource['managers'] ?? [])->isEmpty()) {
            $filters[] = ComponentFactory::create(
                name: 'Managed By',
                type: ComponentType::MultiSelect,
                payloadKey: 'manager',
                value: [],
                extraOptions: [
                    'options' => collect($this->resource['managers'])
                        ->map(fn(User $manager): array => [
                            'value' => $manager->id,
                            'label' => $manager->name . ' ' . $manager->surname .
                                ($manager->division ? ' (' . $manager->division . ')' : '') .
                                ($manager->department ? ' - ' . $manager->department : ''),
                        ])
                        ->toArray(),
                ]
            );
        }

        $filters[] = ComponentFactory::create(
            name: 'Engaged from',
            type: ComponentType::SingleDate,
            payloadKey: 'engaged_from',
            value: null,
        );

        $filters[] = ComponentFactory::create(
            name: 'Engaged to',
            type: ComponentType::SingleDate,
            payloadKey: 'engaged_to',
            value: null,
        );

        if (!collect($this->resource['tenders'] ?? [])->isEmpty()) {
            $filters[] = ComponentFactory::create(
                name: 'Engaged',
                type: ComponentType::DateRange,
                payloadKey: 'engagement_period',
                value: null,
                extraOptions: [
                    'start_key' => 'engaged_from',
                    'end_key' => 'engaged_to',
                ]
            );
        }

        if (!collect($this->resource['tenders'] ?? [])->isEmpty()) {
            $filters[] = ComponentFactory::create(
                name: 'Tender',
                type: ComponentType::MultiSelect,
                payloadKey: 'tender',
                value: [],
                extraOptions: [
                    'options' => collect($this->resource['tenders'])
                        ->map(fn(Tender $tender): array => [
                            'value' => $tender->id,
                            'label' => $tender->name,
                        ])
                        ->toArray(),
                ]
            );
        }

        $filters[] = ComponentFactory::create(
            name: 'Min Rate',
            type: ComponentType::Number,
            payloadKey: 'min_rate',
            value: null,
            extraOptions: [
                'min' => 0,
            ]
        );

        $filters[] = ComponentFactory::create(
            name: 'Max Rate',
            type: ComponentType::Number,
            payloadKey: 'max_rate',
            value: null,
            extraOptions: [
                'min' => 0,
            ]
        );

        $filters[] = ComponentFactory::create(
            name: 'Residence',
            type: ComponentType::MultiSelect,
            payloadKey: 'country',
            value: [],
            extraOptions: [
                'options' => collect(Country::cases())
                    ->when(
                        !empty($this->resource['countries']),
                        fn($allCountries) => $allCountries->filter(
                            fn(Country $country) => $this->resource['countries']->contains($country)
                        )
                    )
                    ->map(fn(Country $country): array => [
                        'value' => $country->value,
                        'label' => $country->trans(),
                    ])
                    ->toArray(),
            ]
        );

        $filters[] = ComponentFactory::create(
            name: 'Profession',
            type: ComponentType::InputText,
            payloadKey: 'profession',
            value: ''
        );

        return $filters;
    }
}
