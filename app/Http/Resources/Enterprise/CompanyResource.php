<?php

namespace App\Http\Resources\Enterprise;

use App\Models\Company;
use App\Models\VendorClient;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\ImageResource;

/** @mixin Company */
class CompanyResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->public_id,
            'name' => $this->name,
            'country' => $this->country->trans(),
            'category' => $this->category,
            'headquarters' => $this->hq,
            'website' => $this->website,
            'linkedin' => $this->linkedin,
            'about' => $this->about,
            'founded_at' => $this->founded,
            'cover' => $this->when(
                (bool) $this->cover_resource,
                fn (): ?string => ImageResource::main($this->cover_resource)?->url
            ),
            'logo' => $this->when(
                (bool) $this->logo_resource,
                fn (): ?string => ImageResource::main($this->logo_resource)?->url
            ),
            'main_industry' => $this->vendor->main_industry->name,
            'industries' => $this->vendor->industries->pluck('name'),
            'technologies' => $this->vendor->technologies->pluck('name'),
            'clients' => $this->vendor->clients
                ->map(
                    fn (VendorClient $client): ?string => optional($client->client)->name
                )
                ->filter()
                ->values(),
            'employees' => [
                'employees' => $this->vendor->employees,
                'developers' => $this->vendor->developers,
                'positions' => $this->vendor->employee_positions->pluck('name'),
            ],
            'rates' => array_filter([
                'juniors' => $this->vendor->rate_junior,
                'mediors' => $this->vendor->rate_medior,
                'seniors' => $this->vendor->rate_senior,
                'leads' => $this->vendor->rate_lead,
            ]),
        ];
    }
}
