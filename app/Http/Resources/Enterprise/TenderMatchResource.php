<?php

namespace App\Http\Resources\Enterprise;

use App\Enums\Enterprise\TenderMatchingStatus;
use App\Models\Company;
use App\Models\TenderMatch;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin TenderMatch */
class TenderMatchResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $matchingApiResponseStatus = $this->matching_api_response_status;
        return [
            'id' => $this->id,
            'tender_id' => $this->tender->public_id,
            'created_at' => $this->created_at?->format('Y-m-d'),
            'updated_at' => $this->updated_at?->format('Y-m-d'),
            'status' => match (true) {
                $matchingApiResponseStatus >= 200 && $matchingApiResponseStatus <= 204 => TenderMatchingStatus::Completed,
                $matchingApiResponseStatus === null => TenderMatchingStatus::InProgress,
                default => TenderMatchingStatus::Failed
            },
            'companies_filter' => $this->companies_filter,
            'companies' => $this->matchedCompanies
                ->map(fn (Company $company): array => [
                    ...CompanyResource::make($company)->toArray($request),
                    'match_details' => TenderMatchCompanyResource::make($company->pivot)->toArray($request)
                ])
        ];
    }
}

