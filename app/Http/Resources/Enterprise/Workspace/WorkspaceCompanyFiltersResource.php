<?php

declare(strict_types=1);

namespace App\Http\Resources\Enterprise\Workspace;

use App\Http\Filtering\FilterBuilder;
use Illuminate\Http\Resources\Json\JsonResource;

class WorkspaceCompanyFiltersResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            ...FilterBuilder::search(),
            ...FilterBuilder::categories(), // CompanyCategory enum
            ...FilterBuilder::countries(
                name: 'Country',
                allowed: $this->resource['countries'] ?? null,
                payloadKey: 'country'
            ),
            ...FilterBuilder::technologies($this->resource['technologies'] ?? null),
            ...FilterBuilder::industries($this->resource['industries'] ?? null),
        ];
    }
}
