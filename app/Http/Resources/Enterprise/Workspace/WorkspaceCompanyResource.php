<?php

namespace App\Http\Resources\Enterprise\Workspace;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\ImageResource;

class WorkspaceCompanyResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->public_id,
            'name' => $this->name,
            'category' => $this->category,
            'status' => $this->publish_status,
            'country' => $this->country->trans(),
            'headquarters' => $this->hq,
            'cover' => $this->display_cover_resource ? ImageResource::main($this->display_cover_resource)->url : null,
            'logo' => $this->display_logo_resource ? ImageResource::main($this->display_logo_resource)->url : null,
            'employees_count' => $this->vendor?->employees,
        ];
    }
}