<?php

namespace App\Http\Resources\Enterprise\Workspace;

use App\Models\Company;
use App\Models\Workspace;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Workspace */
class WorkspaceShowResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'name' => $this->name,
            'rate_type' => $this->rate_type,
            'marketplace_disabled' => $this->marketplace_disabled,
            // TODO: this should not be here due to possible production performance issues
            'companies' => $this->companies
                ->sortBy('name')
                ->map(static function (Company $company): array {
                    return [
                        'id' => $company->public_id,
                        'name' => $company->name,
                        'category' => $company->category,
                        'status' => $company->publish_status,
                        'country' => $company->country->trans(),
                        'headquarters' => $company->hq,
                    ];
                })
                ->values(),
        ];
    }
}
