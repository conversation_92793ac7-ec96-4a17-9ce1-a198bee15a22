<?php

namespace App\Http\Resources\Enterprise;

use App\Http\Resources\Company\CandidateAssignmentResource;
use App\Models\Candidate;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin Candidate */
class CandidateResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->public_id,
            'name' => $this->name,
            'profession' => $this->profession,
            'seniority' => $this->seniority,
            'years_of_experience' => $this->years_of_experience,
            'rate' => $this->rate,
            'country' => $this->country?->name,

            'company' => [
                'id' => $this->company->public_id,
                'name' => $this->company->name,
                'category' => $this->company->category,
                'country' => $this->company->country->trans(),
                'headquarters' => $this->company->hq,
            ],

            'bench_specialist' => BenchSpecialistResource::make($this->whenLoaded('activeBenchSpecialist')),
            'assignment' => CandidateAssignmentResource::make($this->whenLoaded('activeAssignment')),
        ];
    }
}
