<?php

namespace App\Http\Resources\Enterprise;

use App\Enums\Enterprise\TenderMatchingCompaniesFilter;
use App\Models\Tender;
use App\Models\TenderPosition;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/** @mixin Tender */
class TenderResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $latestMatching = $this->matches()
            ->where('companies_filter', TenderMatchingCompaniesFilter::Selection)
            ->latest()
            ->first();

        return [
            'id' => $this->public_id,
            'created_at' => $this->created_at?->format('Y-m-d'),
            'created_by' => $this->creator?->name,
            'name' => $this->name,
            'description' => Str::limit($this->description, 500),
            'region' => $this->rfp?->locations?->take(3)->implode('name', ', ')
                ?: $this->company->country->transWithCity($this->company->hq),
            'main_industry' => $this->project->main_industry->name,
            'rfp_start_date' => $this->rfp?->start_date,
            'rfp_end_date' => $this->rfp?->end_date,
            'matching_exists' => $latestMatching !== null,
            'matching_in_progress' => $latestMatching &&
              $latestMatching->matching_api_response_status === null,
            'technologies' => $this->project->technologies->pluck('name')->toArray(),
            'positions' => $this->positions->map(function (TenderPosition $position): array {
                $positionData = [
                    'name' => $position->name,
                    'count' => $position->count,
                    'seniority' => $position->seniorities->first()?->seniority?->trans(),
                    'must_have_requirements' => $position->must_have_requirements ?? $position->requirements,
                    'price' => $position->price,
                ];

                if ($position->price_to !== $position->price) {
                    $positionData['price_to'] = $position->price_to;
                }

                return $positionData;
            }),
            'submissions_deadline' => $this->submissions_deadline,
            'status' => $this->status,
            'total_invited_companies' => $this->whenCounted('invitedCompanies'),
            'total_viewed_companies' => $this->whenCounted('viewedCompaniesCount'),
            'simulation' => $this->simulation,
        ];
    }
}
