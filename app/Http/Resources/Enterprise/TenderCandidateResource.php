<?php

namespace App\Http\Resources\Enterprise;

use App\Enums\Enterprise\CandidateMatchingSeniorityReason;
use App\Models\Candidate;
use App\Models\TenderPosition;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\Enterprise\MatchedTechnologyResource;
use Illuminate\Support\Str;

/** @mixin Candidate */
class TenderCandidateResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        /** @var TenderPosition $position */
        $position = $this->positions->first();
        $company = $this->company;

        return [
            'id' => $this->public_id,
            'name' => $this->name,
            'profession' => $this->profession,
            'seniority' => $this->seniority,
            'rate' => $this->rate,
            'country' => $this->country?->name,

            'company' => [
                'id' => $company->public_id,
                'name' => $company->name,
                'category' => $company->category,
                'country' => $company->country->trans(),
                'headquarters' => $company->hq,
            ],

            'position' => [
                'id' => $position->id,
                'name' => $position->name,
                'price' => $position->price,
                'price_to' => $position->price_to,
                'seniority' => $position->seniorities->first()->seniority,
            ],

            'application' => [
                'status' => $position->pivot->status,
            ],

            'matching' => $this->when(
                $position->pivot->matching_api_response_status === 200,
                static function () use ($position): array {
                    $matchingApiResponse = $position->pivot->matching_api_response->recursive();
                    return [
                        'score' => ($score = data_get($matchingApiResponse, 'score')) !== null
                            ? (int) round($score * 100)
                            : null,
                        'technologies_score' => ($techScore = data_get($matchingApiResponse, 'technologies.scoring.score')) !== null
                            ? (int) round($techScore * 100)
                            : null,
                        'technologies' => MatchedTechnologyResource::collection(
                            $matchingApiResponse
                                ->get('technologies')
                                ?->get('evaluated_technologies')
                                ?->sortByDesc('score') ?? collect()
                        ),
                        'experiences_count' => count(data_get($matchingApiResponse, 'experience.evaluated_experience', [])),
                        'experiences_score' => ($expScore = data_get($matchingApiResponse, 'experience.scoring.score')) !== null
                            ? (int) round($expScore * 100)
                            : null,
                        'rate_difference_percentage' => ($rateDiff = data_get($matchingApiResponse, 'rates.difference_percentage')) !== null
                            ? round($rateDiff, 2)
                            : null,
                        'seniority_match' => ($seniorityReason = CandidateMatchingSeniorityReason::tryFrom(
                            data_get($matchingApiResponse, 'seniority.reason')
                        )) !== null
                            ? Str::headline($seniorityReason->value)
                            : null,
                    ];
            }),
        ];
    }
}
