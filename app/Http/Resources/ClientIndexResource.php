<?php

namespace App\Http\Resources;

use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\ImageResource;

class ClientIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Client|self $this */
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'logo' => ImageResource::main($this->logo_resource),
        ];
    }
}
