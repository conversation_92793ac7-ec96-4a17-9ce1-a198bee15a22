<?php

namespace App\Http\Resources;

use App\Http\Resources\Traits\SolutionAttributesTrait;
use App\Models\Solution;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\ImageResource;

class SolutionShowResource extends JsonResource
{
    use SolutionAttributesTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        $isOwnSolution = $this->isOwnSolution($this);

        /* @var Solution|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'name' => $this->name,
            'about' => $this->escaped_about,
            'cover' => ImageResource::main($this->cover_resource),
            'main_industry_id' => $this->main_industry_id,

            $this->mergeWhen($isOwnSolution, [
                'publish_status' => $this->publish_status,
                'description' => $this->description,
                'country' => $this->country,
                'ftes' => $this->ftes,
                'value' => $this->value,
                'length_type' => $this->length_type,
                'length' => $this->length,
                'in_house' => $this->in_house,
            ]),

            'industries' => $this->industries->pluck('id'),

            'technologies' => $this->technologies->pluck('id'),

            'vendor' => $this->when($isOwnSolution || $this->vendor->company->is_public, fn () => [
                'id' => $this->vendor->public_id,
                'slug' => $this->vendor->slug,
                'name' => $this->vendor->display_name,
                'logo' => ImageResource::main($this->vendor->display_logo_resource),
                'about' => $this->when($this->vendor->company->is_public_to_user, $this->vendor->company->escaped_about, null),
            ]),

            'client' => $this->when($this->hasSolutionClient() && ($isOwnSolution || ! $this->client->anonymous), fn () => [
                'name' => $this->client->client->name ?? $this->client->name,
                'logo' => ImageResource::main($this->client->client?->logo_resource),
                'description' => $this->client->client->description ?? null,

                $this->mergeWhen($isOwnSolution, fn () => [
                    'anonymous' => $this->client->anonymous,
                    'id' => $this->client->client->id ?? null,
                    'review' => $this->client->escaped_review,
                    'reviewer' => $this->client->reviewer,
                    'reviewer_position' => $this->client->reviewer_position,
                ]),
            ]),

            'review' => $this->when($this->client?->review, fn () => [
                'text' => $this->client->escaped_review,
                'author' => collect([$this->client->reviewer, $this->client->reviewer_position])
                    ->whereNotNull()
                    ->implode(', '),
            ]),

            'media' => SolutionMediumShowResource::collection($this->media),

            'badges' => $this->collectBadges(),

            'unapproved_change' => $this->when($isOwnSolution && $this->unapproved_change, fn () => self::make($this->unapproved_change)),
        ];
    }

    private function hasSolutionClient(): bool
    {
        /* @var Solution|self $this */
        return ! $this->in_house && $this->client;
    }
}
