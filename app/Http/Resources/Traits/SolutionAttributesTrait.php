<?php

namespace App\Http\Resources\Traits;

use App\Enums\UserRole;
use App\Enums\LengthType;
use App\Models\Solution;
use App\POPOs\Badge;

trait SolutionAttributesTrait
{
    protected function collectBadges(?Solution $solution = null): array
    {
        $solution = $solution ?? $this;

        return collect([
            $this->when($this->isOwnSolution($solution), fn () => Badge::primaryText(__('global.labels.own_solution'))),

            $this->when(
                $solution->is_draft,
                fn () => Badge::warningText(__('global.publish_states.draft')),
            ),
            $this->when(
                $this->isOwnSolution($solution) && ($solution->is_awaiting_approval || $solution->unapproved_change),
                fn () => Badge::warningText(__('global.publish_states.awaiting_approval')),
            ),

            Badge::text($solution->main_industry->name),

            $this->when(
                $solution->ftes,
                fn () => Badge::text(trans_choice('global.labels.ftes', $solution->ftes)),
            ),

            $this->when(
                $solution->length_type !== LengthType::Unspecified,
                fn () => Badge::text($solution->length_type->transChoice($solution->length)),
            ),

            $this->when(
                $solution->value,
                fn () => Badge::text(__('global.labels.value', ['value' => format_large_number($solution->value)])),
            ),

            Badge::country($solution->country),
        ])->toArray();
    }

    protected function isOwnSolution(Solution|self $solution): bool
    {
        $user = auth()->user();

        return UserRole::Vendor->has($user)
            && $user->vendor
            && $user->vendor->id === $solution->vendor_id;
    }
}
