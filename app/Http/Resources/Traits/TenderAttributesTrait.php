<?php

namespace App\Http\Resources\Traits;

use App\Enums\ServiceType;
use App\Enums\UserRole;
use App\Models\Tender;
use App\POPOs\Badge;
use Illuminate\Http\Resources\MissingValue;

trait TenderAttributesTrait
{
    protected function hasProjectClient(): bool
    {
        /* @var Tender $this */
        return ! $this->project->anonymous
            && ! $this->project->in_house
            && ($this->project->client || $this->project->client_name);
    }

    protected function hasAppliedCandidates(): MissingValue|bool
    {
        /* @var Tender $this */

        $user = auth()->user();

        if (! $user || UserRole::SuperAdmin->has($user)) {
            return new MissingValue;
        }

        if ($this->isOwnTender()) {
            return new MissingValue;
        }

        return $this->positions->sum('own_candidates_count') > 0;
    }

    protected function shouldDisplayCompany(): bool
    {
        /* @var Tender $this */

        if ($this->isOwnTender()) {
            return true;
        }

        return ! $this->anonymous_company && $this->company->is_public_to_user;
    }

    protected function collectBadges(): array
    {
        /* @var Tender $this */
        return [
            $this->when(
                $this->isOwnTender(),
                Badge::primaryText(__('global.labels.own_tender')),
            ),
            Badge::text($this->project->main_industry->name),
            $this->when(
                $this->service_type === ServiceType::Resources,
                fn () => Badge::text(trans_choice('global.labels.ftes', $this->positions->sum('count')))
            ),
            $this->when(
                $this->service_type === ServiceType::Solution,
                fn () => Badge::text($this->length_type->transChoice($this->length)),
            ),
            Badge::country($this->country),
        ];
    }

    private function isOwnTender(): bool
    {
        /* @var Tender $this */
        return auth()->user()->company_id === $this->company->id;
    }
}
