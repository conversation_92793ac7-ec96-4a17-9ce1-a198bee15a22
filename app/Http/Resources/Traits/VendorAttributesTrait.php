<?php

namespace App\Http\Resources\Traits;

use App\Models\Solution;
use App\Models\Vendor;
use App\POPOs\Badge;
use Libs\Warehouse\JsonResources\ImageResource;

trait VendorAttributesTrait
{
    use SolutionAttributesTrait {
        collectBadges as collectSolutionBadges;
    }

    protected function collectPublishedSolutions(?int $take = null): array
    {
        /* @var Vendor $this */

        $solutions = $take
            ? $this->published_solutions->take($take)
            : $this->published_solutions;

        return $solutions->map(fn (Solution $solution) => [
            'id' => $solution->public_id,
            'slug' => $solution->slug,
            'name' => $solution->name,
            'description' => $solution->description,
            'cover' => ImageResource::thumbnail($solution->cover_resource, 'preview'),
            'client' => $this->when($solution->client && ! $solution->client->anonymous, fn () => [
                'name' => $solution->client->client->name ?? $solution->client->name,
                'logo' => ImageResource::main($solution->client->client?->logo_resource),
                'description' => $solution->client->client->description ?? null,
            ]),
            'badges' => $this->collectSolutionBadges($solution),
        ])->toArray();
    }

    protected function collectBadges(): array
    {
        /* @var Vendor $this */
        return [
            Badge::text($this->main_industry->name),
            Badge::text(trans_choice('global.labels.ftes', $this->employees)),
            Badge::text(__('global.labels.since', ['year' => $this->company->founded])),
            Badge::country($this->company->country, $this->company->country->transWithCity($this->company->hq)),
        ];
    }
}
