<?php

namespace App\Http\Resources;

use App\Models\Candidate;
use App\Models\CandidateExperience;
use App\Models\CandidateSkill;
use App\Models\CandidateAssignment;
use App\Models\TenderPosition;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * Candidate as shown to the client - the owner of the tender
 */
class TenderCandidateClientIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Candidate|self $this */

        // FIXME: add CVs conditionally
        // FIXME: add vendor conditionally

        // Only one position is scoped per candidate, and it is guaranteed
        // that candidate clone is associated with exactly one
        /* @var TenderPosition $position */
        /* @var CandidateAssignment $pivot */
        $position = $this->positions->first();
        $pivot = $position->pivot;

        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'status' => $pivot->status,
            'rejection_reason' => $pivot->rejection_reason,
            'note' => $pivot->client_note,

            'name' => $this->name,
            'country' => $this->country,
            'city' => $this->city,
            'rate' => $this->rate,

            'profession' => $this->profession,
            'seniority' => $this->seniority,
            'last_job_title' => $this->last_job_title,
            'years_of_experience' => $this->years_of_experience,
            'highest_education' => $this->highest_education,
            'field_of_study' => $this->field_of_study,

            'skills' => $this->skills->sortByDesc('years_of_experience')->map(fn (CandidateSkill $skill) => [
                'technology_id' => $skill->technology_id,
                'years_of_experience' => $skill->years_of_experience,
                'experiences' => $skill->experiences()->pluck('public_id'),
            ])->values(),

            'experiences' => $this->experiences->map(fn (CandidateExperience $experience) => [
                'id' => $experience->public_id,
                'name' => $experience->name,
                'description' => $experience->description,
                'length_type' => $experience->length_type,
                'length' => $experience->length,
            ])->values(),

            'position' => [
                'id' => $position->id,
                'name' => $position->name,
                'status' => $pivot->status,
            ],
        ];
    }
}
