<?php

namespace App\Http\Resources;

use App\Models\BenchSpecialist;
use App\Models\CandidateExperience;
use App\Models\CandidateSkill;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BenchSpecialistShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /** @var BenchSpecialist|self $this */

        // FIXME: add CVs conditionally
        // FIXME: add vendor conditionally

        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'available_from' => $this->available_from,
            'available_to' => $this->available_to,

            'candidate' => [
                'name' => $this->candidate->name,
                'country' => $this->candidate->country,
                'city' => $this->candidate->city,
                'rate' => $this->candidate->rate,

                'profession' => $this->candidate->profession,
                'seniority' => $this->candidate->seniority,
                'last_job_title' => $this->candidate->last_job_title,
                'years_of_experience' => $this->candidate->years_of_experience,
                'highest_education' => $this->candidate->highest_education,
                'field_of_study' => $this->candidate->field_of_study,

                'skills' => $this->candidate->skills->sortByDesc('years_of_experience')->map(fn (CandidateSkill $skill) => [
                    'technology_id' => $skill->technology_id,
                    'years_of_experience' => $skill->years_of_experience,
                    'experiences' => $skill->experiences()->pluck('public_id'),
                ])->values(),

                'experiences' => $this->candidate->experiences->map(fn (CandidateExperience $experience) => [
                    'id' => $experience->public_id,
                    'name' => $experience->name,
                    'description' => $experience->description,
                    'length_type' => $experience->length_type,
                    'length' => $experience->length,
                ])->values(),
            ],
        ];
    }
}
