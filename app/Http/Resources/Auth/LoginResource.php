<?php

namespace App\Http\Resources\Auth;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Sentinel\LoginTokens;

class LoginResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        /* @var LoginTokens|self $this */
        return [
            'refresh_token' => $this->newAccessToken->plainTextToken,
            'jwt' => $this->signedJwtToken->toString(),
        ];
    }
}
