<?php

namespace App\Http\Resources;

use App\Http\Resources\Traits\VendorAttributesTrait;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\ImageResource;

class VendorIndexResource extends JsonResource
{
    use VendorAttributesTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Vendor|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'name' => $this->display_name,
            'logo' => ImageResource::thumbnail($this->display_logo_resource, 'preview'),
            'cover' => ImageResource::thumbnail($this->display_cover_resource, 'preview'),

            'solutions' => $this->collectPublishedSolutions(3),

            'badges' => $this->collectBadges(),
        ];
    }
}
