<?php

namespace App\Http\Resources\Admin;

use App\Models\Candidate;
use App\Models\TenderPosition;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TenderCandidateIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Candidate|self $this */

        // Only one position is scoped per candidate, and it is guaranteed
        // that candidate clone is associated with exactly one
        /* @var TenderPosition $position */
        $position = $this->positions->first();

        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'status' => $position->pivot->status,
            'rejection_reason' => $position->pivot->rejection_reason,

            'internal_name' => $this->internal_name,
            'profession' => $this->profession,
            'seniority' => $this->seniority,
            'rate' => $this->rate,
            'country' => $this->country,

            'position' => [
                'id' => $position->id,
                'name' => $position->name,
                'status' => $position->pivot->status,
            ],

            'vendor' => [
                'id' => $this->vendor->public_id,
                'name' => $this->vendor->display_name,
            ],
        ];
    }
}
