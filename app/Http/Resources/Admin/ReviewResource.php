<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        $submittedAt = $this->unapproved_change->updated_at ?? $this->updated_at;

        return [
            'type' => $this->__type,
            'submitted_at' => $submittedAt->format(__('global.dates.full')),
            $this->merge($this->__config['resource']::make($this)),
        ];
    }
}
