<?php

namespace App\Http\Resources\Admin;

use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VendorShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Vendor|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'publish_status' => $this->publish_status,
            'main_industry_id' => $this->main_industry_id,
            'employees' => $this->employees,
            'developers' => $this->developers,
            'offering_resources' => $this->offering_resources,
            'offering_solutions' => $this->offering_solutions,
            'payment_time_and_material' => $this->payment_time_and_material,
            'payment_fixed_price' => $this->payment_fixed_price,
            'notify_irrelevant_offers' => $this->notify_irrelevant_offers,

            'rate_junior' => $this->rate_junior,
            'rate_medior' => $this->rate_medior,
            'rate_senior' => $this->rate_senior,
            'rate_lead' => $this->rate_lead,

            'industries' => $this->industries->pluck('id'),
            'technologies' => $this->technologies->pluck('id'),

            'clients' => $this->clients->map(fn ($client) => [
                'id' => $client->client->id ?? null,
                'name' => $client->client->name ?? $client->name,
            ]),

            'employee_positions' => $this->employee_positions->pluck('id'),

            'unapproved_change' => self::make($this->whenLoaded('unapproved_change')),
        ];
    }
}
