<?php

namespace App\Http\Resources\Admin;

use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VendorReviewResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Vendor|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'name' => $this->display_name,

            'company' => [
                'id' => $this->company->public_id,
                'slug' => $this->company->slug,
                'name' => $this->company->name,
            ],
        ];
    }
}
