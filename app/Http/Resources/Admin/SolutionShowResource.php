<?php

namespace App\Http\Resources\Admin;

use App\Models\Solution;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\ImageResource;

class SolutionShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Solution|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'publish_status' => $this->publish_status,
            'name' => $this->name,
            'description' => $this->description,
            'about' => $this->escaped_about,
            'cover' => ImageResource::main($this->cover_resource),
            'country' => $this->country,
            'main_industry_id' => $this->main_industry_id,
            'length_type' => $this->length_type,
            'length' => $this->length,
            'ftes' => $this->ftes,
            'value' => $this->value,
            'in_house' => $this->in_house,

            'industries' => $this->industries->pluck('id'),
            'technologies' => $this->technologies->pluck('id'),

            'client' => $this->when($this->hasSolutionClient(), fn () => [
                'anonymous' => $this->client->anonymous,
                'id' => $this->client->client->id ?? null,
                'name' => $this->client->client->name ?? $this->client->name,
                'review' => $this->client->escaped_review,
                'reviewer' => $this->client->reviewer,
                'reviewer_position' => $this->client->reviewer_position,
            ]),

            'media' => SolutionMediumShowResource::collection($this->media),

            'vendor' => [
                'id' => $this->public_id,
                'slug' => $this->slug,
                'name' => $this->vendor->company->name,
            ],

            'unapproved_change' => self::make($this->whenLoaded('unapproved_change')),
        ];
    }

    private function hasSolutionClient(): bool
    {
        return ! $this->in_house && $this->client;
    }
}
