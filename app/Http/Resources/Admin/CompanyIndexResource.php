<?php

namespace App\Http\Resources\Admin;

use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Company|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'publish_status' => $this->publish_status,
            'has_unapproved_change' => (bool) $this->unapproved_change_exists,
            'is_public' => $this->is_public,
            'is_vendor' => $this->is_vendor,
            'name' => $this->name,
            'country' => $this->country,
            'hq' => $this->hq,

            'vendor' => $this->when($this->vendor, fn () => [
                'id' => $this->vendor->public_id,
                'publish_status' => $this->vendor->publish_status,
                'has_unapproved_change' => (bool) $this->vendor->unapproved_change_exists,
            ]),
        ];
    }
}
