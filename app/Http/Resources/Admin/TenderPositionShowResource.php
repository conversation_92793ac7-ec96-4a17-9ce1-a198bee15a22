<?php

namespace App\Http\Resources\Admin;

use App\Models\TenderPosition;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TenderPositionShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var TenderPosition|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'name' => $this->name,
            'description' => $this->escaped_description,
            'requirements' => $this->escaped_requirements,
            'must_have_requirements' => $this->escaped_must_have_requirements,
            'price' => $this->price,
            'price_to' => $this->price_to,
            'start_date' => $this->start_date,
            'length_type' => $this->length_type,
            'length' => $this->length,
            'possible_extension' => $this->possible_extension,
            'workload' => $this->workload,
            'work_location' => $this->work_location,
            'count' => $this->count,
            'interview' => $this->interview,
            'equipment' => $this->equipment,
            'employee_position' => [
                'id' => 15, // Use ID of "Other" as a temporary workaround.
                'name' => $this->name,
            ],

            'seniorities' => $this->seniorities->pluck('seniority'),

            'technologies' => $this->technologies->pluck('id'),
        ];
    }
}
