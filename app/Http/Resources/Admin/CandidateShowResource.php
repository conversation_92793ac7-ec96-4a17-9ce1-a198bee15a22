<?php

namespace App\Http\Resources\Admin;

use App\Models\Candidate;
use App\Models\CandidateExperience;
use App\Models\CandidateSkill;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\FileResource;

class CandidateShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        /* @var Candidate|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'finished' => $this->finished,
            'internal_name' => $this->internal_name,
            'name' => $this->name,
            'country' => $this->country,
            'city' => $this->city,
            'rate' => $this->rate,
            'profession' => $this->profession,
            'seniority' => $this->seniority,
            'last_job_title' => $this->last_job_title,
            'years_of_experience' => $this->years_of_experience,
            'highest_education' => $this->highest_education,
            'field_of_study' => $this->field_of_study,
            'cv' => FileResource::make($this->cv_resource),
            'skills' => $this->skills->sortByDesc('years_of_experience')->map(fn (CandidateSkill $skill) => [
                'technology_id' => $skill->technology_id,
                'years_of_experience' => $skill->years_of_experience,
                'experiences' => $skill->experiences()->pluck('public_id'),
            ])->values(),
            'experiences' => $this->experiences->sortByDesc('order')
                ->map(fn (CandidateExperience $experience) => [
                    'id' => $experience->public_id,
                    'name' => $experience->name,
                    'description' => $experience->description,
                    'length_type' => $experience->length_type,
                    'length' => $experience->length,
                ])->values(),
            'vendor' => [
                'id' => $this->vendor->public_id,
                'slug' => $this->vendor->slug,
                'company_slug' => $this->vendor->company->slug,
                'name' => $this->vendor->company->name,
            ],
        ];
    }
}
