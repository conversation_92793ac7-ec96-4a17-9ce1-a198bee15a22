<?php

namespace App\Http\Resources\Admin;

use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TenderVendorIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Vendor|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'company_slug' => $this->company->slug,
            'name' => $this->display_name,
            'country' => $this->company->country,
            'notify_irrelevant_offers' => $this->notify_irrelevant_offers,
            'allowed' => (bool) $this->tenders->first()?->pivot?->allowed,
            'notified' => (bool) $this->tenders->first()?->pivot?->notified,
        ];
    }
}
