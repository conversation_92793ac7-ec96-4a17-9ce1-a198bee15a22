<?php

namespace App\Http\Resources\Admin;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\ImageResource;

class StaffUserShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var User|self $this */
        return [
            'id' => $this->id,
            'slug' => $this->slug,
            'name' => $this->name,
            'surname' => $this->surname,
            'email' => $this->email,
            'phone' => $this->phone,
            'avatar' => ImageResource::main($this->avatar_resource),
            'position' => $this->position,
            'division' => $this->division,
            'department' => $this->department,
            'roles' => $this->getRoleNames(),
        ];
    }
}
