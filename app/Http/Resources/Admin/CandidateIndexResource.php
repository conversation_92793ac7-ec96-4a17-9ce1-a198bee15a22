<?php

namespace App\Http\Resources\Admin;

use App\Models\Candidate;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CandidateIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Candidate|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'internal_name' => $this->internal_name,
            'profession' => $this->profession,
            'seniority' => $this->seniority,
            'rate' => $this->rate,
            'country' => $this->country,

            'vendor' => [
                'id' => $this->vendor->public_id,
                'slug' => $this->vendor->slug,
                'company_slug' => $this->vendor->company->slug,
                'name' => $this->vendor->company->name,
            ],
        ];
    }
}
