<?php

namespace App\Http\Resources\Admin;

use App\Models\BenchSpecialist;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BenchSpecialistIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var BenchSpecialist|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'publish_status' => $this->publish_status,
            'available_from' => $this->available_from,
            'available_to' => $this->available_to,

            'candidate' => CandidateIndexResource::make($this->candidate),
        ];
    }
}
