<?php

namespace App\Http\Resources\Admin;

use App\Models\Tender;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TenderIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Tender|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'name' => $this->name,
            'status' => $this->status,
            'publish_status' => $this->publish_status,
            'submissions_deadline' => $this->submissions_deadline,
            'service_type' => $this->service_type,

            'company' => [
                'id' => $this->company->public_id,
                'slug' => $this->company->slug,
                'name' => $this->company->name,
            ],
        ];
    }
}
