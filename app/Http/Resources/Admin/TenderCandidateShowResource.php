<?php

namespace App\Http\Resources\Admin;

use App\Models\Candidate;
use App\Models\TenderPosition;
use Illuminate\Http\Request;

/**
 * Normally, resources are following KISS pattern rather than DRY pattern.
 * However, FE consumer of both candidate and tender candidate endpoints
 * is the same, so the same structure is expected - with addition of
 * tender relationship metadata
 */
class TenderCandidateShowResource extends CandidateShowResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Candidate|self $this */

        // Only one position is scoped per candidate, and it is guaranteed
        // that candidate clone is associated with exactly one
        /* @var TenderPosition $position */
        $position = $this->positions->first();

        return parent::toArray($request) + [
            'status' => $position->pivot->status,
            'rejection_reason' => $position->pivot->rejection_reason,
        ];
    }
}
