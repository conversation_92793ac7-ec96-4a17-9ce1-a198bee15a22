<?php

namespace App\Http\Resources\Admin;

use App\Models\CompanyNote;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\ImageResource;

class CompanyNoteIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var CompanyNote|self $this */
        return [
            'id' => $this->id,
            'author' => $this->whenLoaded('user', fn () => [
                'name' => $this->user->display_name,
                'avatar' => ImageResource::thumbnail($this->user->avatar_resource, 'preview'),
            ], null),
            'note' => $this->escaped_note,
            'posted_at' => $this->created_at->format(__('global.dates.full')),
        ];
    }
}
