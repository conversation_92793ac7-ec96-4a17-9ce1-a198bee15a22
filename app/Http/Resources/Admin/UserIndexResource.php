<?php

namespace App\Http\Resources\Admin;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var User|self $this */
        return [
            'id' => $this->id,
            'slug' => $this->slug,
            'name' => $this->name,
            'surname' => $this->surname,
            'email' => $this->email,
            'phone' => $this->phone,
            'position' => $this->position,
            'division' => $this->division,
            'department' => $this->department,
            // TODO(<PERSON>): backward compatibility, remove once UI is updated.
            'role' => $this->getRoleNames()->first(),
            'roles' => $this->getRoleNames(),

            'company' => [
                'id' => $this->company->id,
                'name' => $this->company->name,
            ],
        ];
    }
}
