<?php

namespace App\Http\Resources\Admin;

use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\FileResource;
use Libs\Warehouse\JsonResources\ImageResource;

class CompanyShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Company|self $this */
        return [
            'id' => $this->public_id,
            'publish_status' => $this->publish_status,
            'slug' => $this->slug,
            'is_public' => $this->is_public,
            'is_vendor' => $this->is_vendor,
            'name' => $this->name,
            'logo' => ImageResource::main($this->logo_resource),
            'cover' => ImageResource::main($this->cover_resource),
            'owner' => $this->owner,
            'hq' => $this->hq,
            'country' => $this->country,
            'eu_vat' => $this->eu_vat,
            'website' => $this->website,
            'linkedin' => $this->linkedin,
            'founded' => $this->founded,
            'about' => $this->escaped_about,
            'notes' => $this->escaped_notes,

            'company_documents' => FileResource::collection($this->whenLoaded('document_resources')),

            'vendor' => $this->when($this->vendor, VendorShowResource::make($this->vendor)),

            'unapproved_change' => self::make($this->whenLoaded('unapproved_change')),
        ];
    }
}
