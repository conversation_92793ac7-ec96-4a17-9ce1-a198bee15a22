<?php

namespace App\Http\Resources\Admin;

use App\Models\Technology;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\ImageResource;

class TechnologyIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Technology|self $this */
        return [
            'id' => $this->id,
            'emsi_id' => $this->emsi_id,
            'slug' => $this->slug,
            'name' => $this->name,
            'parent_id' => $this->parent_id,
            'logo' => ImageResource::main($this->logo_resource),
            'featured' => $this->featured,
        ];
    }
}
