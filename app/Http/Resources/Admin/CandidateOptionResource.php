<?php

namespace App\Http\Resources\Admin;

use App\Models\Candidate;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CandidateOptionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        /* @var Candidate|self $this */
        return [
            'id' => $this->public_id,
            'label' => $this->internal_name,
        ];
    }
}
