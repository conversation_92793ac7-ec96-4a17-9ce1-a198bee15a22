<?php

namespace App\Http\Resources\Admin;

use App\Models\EmployeePosition;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EmployeePositionShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var EmployeePosition|self $this */
        return [
            'id' => $this->id,
            'slug' => $this->slug,
            'name' => $this->name,
            'name_plural' => $this->name_plural,
            'hireable' => $this->hireable,
            'seniorities' => $this->seniorities_collection,
        ];
    }
}
