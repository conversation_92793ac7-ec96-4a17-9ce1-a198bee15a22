<?php

namespace App\Http\Resources;

use App\Models\BenchSpecialist;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BenchSpecialistIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /** @var BenchSpecialist|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'available_from' => $this->available_from,
            'available_to' => $this->available_to,

            'candidate' => [
                'name' => $this->candidate->name,
                'profession' => $this->candidate->profession,
                'seniority' => $this->candidate->seniority,
                'rate' => $this->candidate->rate,
                'country' => $this->candidate->country,
            ],
        ];
    }
}
