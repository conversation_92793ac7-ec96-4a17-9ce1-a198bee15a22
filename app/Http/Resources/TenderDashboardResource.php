<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TenderDashboardResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        return [
            'stats' => $this->when($this->stats, $this->stats),
            'company_tenders' => $this->when(
                $this->companyTenders,
                fn () => TenderIndexResource::collection($this->companyTenders),
            ),
            'open_tenders' => TenderIndexResource::collection($this->openTenders),
            'completed_tenders' => TenderIndexResource::collection($this->completedTenders),
        ];
    }
}
