<?php

namespace App\Http\Resources\Users\Profile;

use App\Http\Resources\Enterprise\Workspace\WorkspaceShowResource;
use App\Models\Tender;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\ImageResource;

class ProfileResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        /* @var User|self $this */
        return [
            'name' => $this->name,
            'surname' => $this->surname,
            'email' => $this->email,
            'email_verified' => (bool) $this->email_verified_at,
            'phone' => $this->phone,
            'position' => $this->position,
            'division' => $this->division,
            'department' => $this->department,
            'avatar' => ImageResource::main($this->avatar_resource),
            'roles' => $this->getRoleNames(),

            'company' => $this->when($this->company, fn () => [
                'id' => $this->company->public_id,
                'name' => $this->company->name,
                'is_vendor' => $this->company->is_vendor,
                'vendor_id' => $this->when($this->company->is_vendor, fn () => $this->vendor->public_id),
            ]),

            'workspaces' => WorkspaceShowResource::collection($this->whenLoaded('workspaces')),

            'tenders' => Tender::query()
                ->when(
                    (bool) $this->company_id,
                    fn (Builder $query): Builder => $query->where('company_id', $this->company_id),
                    fn (Builder $query): Builder => $query->where('created_by', $this->id),
                )
                ->exists(),
        ];
    }
}
