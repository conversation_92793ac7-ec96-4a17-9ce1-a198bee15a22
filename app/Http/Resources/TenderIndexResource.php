<?php

namespace App\Http\Resources;

use App\Http\Resources\Traits\TenderAttributesTrait;
use App\Models\Tender;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\ImageResource;

class TenderIndexResource extends JsonResource
{
    use TenderAttributesTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Tender|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'name' => $this->name,
            'description' => $this->description,
            'status' => $this->status,
            'publish_status' => $this->publish_status,
            'submissions_deadline' => $this->submissions_deadline,
            'service_type' => $this->service_type,
            'start_date' => $this->rfp?->start_date,
            'end_date' => $this->rfp?->end_date,

            'company' => $this->when($this->shouldDisplayCompany(), [
                'id' => $this->company->public_id,
                'slug' => $this->company->slug,
                'name' => $this->company->display_name,
                'logo' => ImageResource::thumbnail(
                    $this->company->display_logo_resource,
                    'preview',
                    asset('assets/logo-client.png'),
                ),
            ], [
                'name' => __('global.labels.partner_client'),
                'logo' => ImageResource::fromUrl(asset('assets/logo-client.png')),
            ]),

            'badges' => $this->collectBadges(),
        ];
    }
}
