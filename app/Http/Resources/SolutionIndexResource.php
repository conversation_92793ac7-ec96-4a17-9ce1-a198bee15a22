<?php

namespace App\Http\Resources;

use App\Http\Resources\Traits\SolutionAttributesTrait;
use App\Models\Solution;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\ImageResource;

class SolutionIndexResource extends JsonResource
{
    use SolutionAttributesTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Solution|self $this */

        $isOwnSolution = $this->isOwnSolution($this);

        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'name' => $this->name,
            'description' => $this->description,
            'cover' => ImageResource::thumbnail($this->cover_resource, 'preview'),

            'vendor' => $this->when($isOwnSolution || $this->vendor->company->is_public, [
                'name' => $this->vendor->display_name,
                'logo' => ImageResource::thumbnail($this->vendor->display_logo_resource, 'preview'),
            ]),

            'client' => $this->when($this->client && ! $this->client->anonymous, fn () => [
                'name' => $this->client->client->name ?? $this->client->name,
                'logo' => ImageResource::main($this->client->client?->logo_resource),
            ]),

            'badges' => $this->collectBadges(),
        ];
    }
}
