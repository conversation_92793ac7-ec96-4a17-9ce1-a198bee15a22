<?php

namespace App\Http\Resources;

use App\Http\Resources\Traits\VendorAttributesTrait;
use App\Enums\UserRole;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\MissingValue;
use Libs\Warehouse\JsonResources\FileResource;
use Libs\Warehouse\JsonResources\ImageResource;

class VendorShowResource extends JsonResource
{
    use VendorAttributesTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Vendor|self $this */
        $isPublic = $this->company->is_public_to_user;

        /* @var Vendor|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'name' => $this->company->display_name,
            'logo' => ImageResource::main($this->company->display_logo_resource),
            'cover' => ImageResource::main($this->company->display_cover_resource),
            'company_slug' => $this->when(UserRole::SuperAdmin->has($request->user()), $this->company->slug),

            'country' => $this->company->country,
            'hq' => $this->company->hq,
            'founded' => $this->company->founded,
            'about' => $this->when($isPublic, $this->company->escaped_about, null),
            'website' => $this->when($isPublic, $this->company->website, null),
            'linkedin' => $this->when($isPublic, $this->company->linkedin, null),
            'employees' => $this->employees,
            'developers' => $this->developers,
            'positions' => $this->employee_positions->map(fn ($position) => [
                'name' => $position->employee_position->name ?? $position->name,
            ]),

            'solutions' => $this->collectPublishedSolutions(),

            'industries' => $this->industries->pluck('id'),

            'technologies' => $this->technologies->pluck('id'),

            'clients' => $this->clients->map(fn ($client) => [
                'name' => $client->client->name ?? $client->name,
            ]),

            'employee_positions' => $this->employee_positions->pluck('id'),

            'badges' => $this->collectBadges(),

            $this->mergeWhen($isPublic, fn () => [
                'presentation' => FileResource::make($this->company->presentation_resource),
                'presentation_url' => $this->company->presentation_url ?? new MissingValue,
            ]),
        ];
    }
}
