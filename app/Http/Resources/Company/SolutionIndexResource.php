<?php

namespace App\Http\Resources\Company;

use App\Models\Solution;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SolutionIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Solution|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'name' => $this->name,
            'publish_status' => $this->publish_status,
            'has_unapproved_change' => (bool) $this->unapproved_change_exists,
        ];
    }
}
