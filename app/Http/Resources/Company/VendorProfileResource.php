<?php

namespace App\Http\Resources\Company;

use App\Models\Company;
use App\Models\Vendor;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Overseer\PublishStatus;

/** @mixin Company */
class VendorProfileResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $hasProfile = (bool) $this->vendor;

        return [
            'is_vendor' => $this->is_vendor && $this->vendor?->is_published,
            'has_profile' => $hasProfile,

            $this->mergeWhen($hasProfile, fn (): array => $this->makeVendor($this->vendor)),
        ];
    }

    private function makeVendor(Vendor $vendor, bool $original = true): array
    {
        $isBrandNewProfile = $original && $vendor->publish_status !== PublishStatus::Published;
        $unapprovedChange = $original ? $vendor->unapproved_change : null;

        if ($isBrandNewProfile) {
            $unapprovedChange = $vendor;
            $vendor = new Vendor();
        }

        return [
            'id' => $vendor->public_id,
            'publish_status' => $vendor->publish_status,
            'main_industry_id' => $vendor->main_industry_id,
            'employees' => $vendor->employees,
            'developers' => $vendor->developers,
            'offering_resources' => $vendor->offering_resources,
            'offering_solutions' => $vendor->offering_solutions,
            'payment_time_and_material' => $vendor->payment_time_and_material,
            'payment_fixed_price' => $vendor->payment_fixed_price,
            'notify_irrelevant_offers' => $vendor->notify_irrelevant_offers,

            'rate_junior' => $vendor->rate_junior,
            'rate_medior' => $vendor->rate_medior,
            'rate_senior' => $vendor->rate_senior,
            'rate_lead' => $vendor->rate_lead,

            'industries' => $vendor->industries->pluck('id'),
            'technologies' => $vendor->technologies->pluck('id'),

            'clients' => $vendor->clients->map(fn ($client) => [
                'id' => $client->client->id ?? null,
                'name' => $client->client->name ?? $client->name,
            ]),

            'employee_positions' => $vendor->employee_positions->pluck('id'),

            'unapproved_change' => $this->when(
                (bool) $unapprovedChange,
                fn (): array => $this->makeVendor($unapprovedChange, original: false)
            ),
        ];
    }
}
