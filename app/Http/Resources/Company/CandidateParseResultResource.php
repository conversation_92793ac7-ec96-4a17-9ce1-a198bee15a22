<?php

namespace App\Http\Resources\Company;

use App\POPOs\CandidateParseResult;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CandidateParseResultResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /** @var CandidateParseResult|self $this */
        return [
            'parse_status' => $this->parseStatus,
            'candidate_slug' => $this->candidateSlug,
        ];
    }
}
