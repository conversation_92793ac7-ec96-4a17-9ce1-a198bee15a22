<?php

namespace App\Http\Resources\Company;

use App\Enums\TenderCandidateStatus;
use App\Models\Candidate;
use App\Models\CandidateAssignment;
use App\Models\CandidateExperience;
use App\Models\CandidateSkill;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\FileResource;

class CandidateShowResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Candidate|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'finished' => $this->finished,

            'internal_name' => $this->internal_name,
            'name' => $this->name,
            'country' => $this->country,
            'city' => $this->city,
            'rate' => $this->rate,

            'profession' => $this->profession,
            'seniority' => $this->seniority,
            'last_job_title' => $this->last_job_title,
            'years_of_experience' => $this->years_of_experience,
            'highest_education' => $this->highest_education,
            'field_of_study' => $this->field_of_study,

            'cv' => FileResource::make($this->cv_resource),

            'skills' => $this->skills->sortByDesc('years_of_experience')->map(fn (CandidateSkill $skill) => [
                'technology_id' => $skill->technology_id,
                'years_of_experience' => $skill->years_of_experience,
                'experiences' => $skill->experiences()->pluck('public_id'),
            ])->values(),

            'experiences' => $this->experiences->map(fn (CandidateExperience $experience) => [
                'id' => $experience->public_id,
                'name' => $experience->name,
                'description' => $experience->description,
                'length_type' => $experience->length_type,
                'length' => $experience->length,
            ])->values(),

            'bench_specialist' => $this->when($this->activeBenchSpecialist, fn (): array => [
                'id' => $this->activeBenchSpecialist->public_id,
                'slug' => $this->activeBenchSpecialist->slug,
                'publish_status' => $this->activeBenchSpecialist->publish_status,
                'available_from' => $this->activeBenchSpecialist->available_from,
                'available_to' => $this->activeBenchSpecialist->available_to,
            ]),

            'assignment' => CandidateAssignmentResource::make($this->activeAssignment),
        ];
    }
}
