<?php

namespace App\Http\Resources\Company;

use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\FileResource;
use Libs\Warehouse\JsonResources\ImageResource;

class ProfileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Company|self $this */
        return [
            'name' => $this->name,
            'logo' => ImageResource::main($this->logo_resource),
            'cover' => ImageResource::main($this->cover_resource),
            'hq' => $this->hq,
            'country' => $this->country,

            'owner' => $this->owner,
            'eu_vat' => $this->eu_vat,
            'website' => $this->website,
            'linkedin' => $this->linkedin,
            'founded' => $this->founded,
            'about' => $this->escaped_about,

            'company_documents' => FileResource::collection($this->whenLoaded('document_resources')),

            'unapproved_change' => self::make($this->whenLoaded('unapproved_change')),
        ];
    }
}
