<?php

namespace App\Http\Resources\Company;

use App\Http\Resources\Enterprise\UserResource;
use App\Models\CandidateAssignment;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/** @mixin CandidateAssignment */
class CandidateAssignmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        return [
            'company' => $this->when(
                $this->relationLoaded('company') && $this->company,
                fn (): array => [
                    'id' => $this->company->public_id,
                    'name' => $this->company->name,
                ]
            ),
            'tender' => $this->when(
                $this->relationLoaded('tender') && $this->tender,
                fn (): array => [
                    'id' => $this->tender->public_id,
                    'name' => $this->tender->name,
                ]
            ),
            'tender_position' => $this->when(
                $this->relationLoaded('tenderPosition') && $this->tenderPosition,
                fn (): array => [
                    'id' => $this->tenderPosition->public_id,
                    'name' => $this->tenderPosition->name,
                ]
            ),
            'manager' => $this->when(
                $this->relationLoaded('manager') && $this->manager,
                fn (): array => UserResource::make($this->manager)->toArray($request)
            ),
            'start_date' => $this->start_date,
            'end_date' => $this->end_date
        ];
    }
}
