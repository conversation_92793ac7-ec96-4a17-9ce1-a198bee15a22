<?php

namespace App\Http\Resources\Company;

use App\Models\Company;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\ImageResource;

class CompanyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Company|self $this */
        return [
            'id' => $this->public_id,
            'name' => $this->name,
            'category' => $this->category,
            'logo' => ImageResource::main($this->logo_resource),
            'cover' => ImageResource::main($this->cover_resource),
            'hq' => $this->hq,
            'country' => $this->country,
        ];
    }
}
