<?php

namespace App\Http\Resources\Company;

use App\Enums\TenderCandidateStatus;
use App\Models\Candidate;
use App\Models\CandidateAssignment;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CandidateIndexResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Candidate|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'internal_name' => $this->internal_name,
            'profession' => $this->profession,
            'seniority' => $this->seniority,
            'rate' => $this->rate,
            'country' => $this->country,

            'bench_specialist' => $this->when(
                $this->relationLoaded('activeBenchSpecialist') && $this->activeBenchSpecialist,
                fn (): array => [
                    'id' => $this->activeBenchSpecialist->public_id,
                    'slug' => $this->activeBenchSpecialist->slug,
                    'publish_status' => $this->activeBenchSpecialist->publish_status,
                    'available_from' => $this->activeBenchSpecialist->available_from,
                    'available_to' => $this->activeBenchSpecialist->available_to,
                ]
            ),

            'assignment' => CandidateAssignmentResource::make($this->whenLoaded('activeAssignment')),
        ];
    }
}
