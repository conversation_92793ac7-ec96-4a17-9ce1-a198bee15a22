<?php

namespace App\Http\Resources;

use App\Enums\ServiceType;
use App\Enums\TenderProcessingType;
use App\Enums\TenderStatus;
use App\Http\Resources\Traits\TenderAttributesTrait;
use App\Models\Tender;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\ImageResource;

class TenderShowResource extends JsonResource
{
    use TenderAttributesTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        /* @var Tender|self $this */
        return [
            'id' => $this->public_id,
            'slug' => $this->slug,
            'anonymous_company' => $this->anonymous_company,
            'status' => $this->status,
            'submissions_deadline' => $this->submissions_deadline,
            'end_of_incubation' => $this->when($this->status === TenderStatus::Incubation, $this->end_of_incubation),
            'processing_type' => $this->processing_type,
            'evaluation_date' => $this->when($this->processing_type === TenderProcessingType::CustomDate, $this->evaluation_date),
            'service_type' => $this->service_type,
            $this->mergeWhen($this->service_type === ServiceType::Solution, [
                'payment_type' => $this->payment_type,
                'price' => $this->price,
                'price_to' => $this->price_to,
                'length_type' => $this->length_type,
                'length' => $this->length,
            ]),
            'start_date' => $this->rfp?->start_date,
            'end_date' => $this->rfp?->end_date,
            'name' => $this->name,
            'description' => $this->description,
            'about' => $this->escaped_about,
            'cover' => ImageResource::main($this->cover_resource),
            'country' => $this->country,

            'company' => $this->when($this->shouldDisplayCompany(), [
                'id' => $this->company->public_id,
                'slug' => $this->company->slug,
                'name' => $this->company->name,
                'logo' => ImageResource::main($this->company->logo_resource),
            ]),

            'badges' => $this->collectBadges(),

            'project' => [
                'main_industry_id' => $this->project->main_industry_id,
                'in_house' => $this->project->in_house,
                'anonymous' => $this->project->anonymous,

                $this->mergeWhen(! $this->project->anonymous || $this->isOwnTender(), [
                    'name' => $this->project->name,
                    'about' => $this->project->escaped_about,
                ]),

                'technologies' => $this->project->technologies->pluck('id'),

                'industries' => $this->project->industries->pluck('id'),

                'client' => $this->when($this->hasProjectClient(), fn () => [
                    'id' => $this->project->client->id ?? null,
                    'name' => $this->project->client->name ?? $this->project->client_name,
                    'logo' => ImageResource::main($this->project->client?->logo_resource),
                    'description' => $this->project->client->description ?? null,
                ]),
            ],

            'positions' => $this->when(
                $this->service_type === ServiceType::Resources,
                TenderPositionShowResource::collection($this->positions),
            ),

            'has_applied_candidates' => $this->hasAppliedCandidates(),
            'simulation' => $this->simulation,
        ];
    }
}
