<?php

namespace App\Http\Requests\Users\Profile;

use App\Enums\UserRole;
use Illuminate\Foundation\Http\FormRequest;

class ProfileRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $passwordRules = $this->input('password') ? [
            'current_password' => 'required|string|current_password',
            'password' => password_rules(),
        ] : [];

        $phoneRequired = UserRole::Vendor->has(auth()->user()) ? 'required' : 'nullable';

        return [
            'name' => 'required|string|max:50',
            'surname' => 'required|string|max:50',
            'avatar_resource_id' => 'nullable|string|exists:resources,public_id,deleted_at,NULL,type,user_avatar_image',
            'phone' => "$phoneRequired|phone:INTERNATIONAL",
            'position' => 'required|string|max:150',
            'division' => 'nullable|string|max:150',
            'department' => 'nullable|string|max:150',
        ] + $passwordRules;
    }
}
