<?php

namespace App\Http\Requests\Traits;

/**
 * Handles multipart/form-data cases
 * when data is wrapped in data attribute.
 */
trait HasDataWrapper
{
    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        if ($this->has('data')) {
            $data = $this->input('data');
            $this->merge($data);
        }
    }
}
