<?php

namespace App\Http\Requests\Traits;

use App\Enums\LengthType;
use App\Models\Candidate;

trait CandidateRulesTrait
{
    protected function experiencesRules(?Candidate $candidate): array
    {
        $candidateId = $candidate?->id;

        $individual = is_array($this->input('experiences'))
            ? collect($this->input('experiences'))->reduce(function ($rules, $experience, $index) use ($candidateId) {
                // Ensure that index is integer. If wrong data is passed,
                // it may actually be a string key, which would cause typing error
                $index = is_int($index) ? $index : 0;

                $edit = $candidateId && ($experience['id'] ?? null);

                $baseRules = [
                    "experiences.$index.name" => 'required|string|max:100',
                    "experiences.$index.description" => 'required|string',
                    "experiences.$index.length_type" => 'required|string|'.LengthType::inRule(),
                    "experiences.$index.length" => 'nullable|required_if:experiences.*.length_type,'.LengthType::specificValueList(',').'|integer',
                ];

                $idRules = $edit ? [
                    "experiences.$index.id" => "required|string|exists:candidate_experiences,public_id,deleted_at,NULL,candidate_id,$candidateId",
                    "experiences.$index.pseudo_id" => 'prohibited',
                ] : [
                    "experiences.$index.id" => 'prohibited',
                    "experiences.$index.pseudo_id" => 'required|integer',
                ];

                return $rules + $baseRules + $idRules;
            }, [])
            : [];

        return [
            'experiences' => 'present|array',
        ] + $individual;
    }

    protected function skillsRules(?Candidate $candidate): array
    {
        $candidateId = $candidate?->id;

        $experienceIdsRules = $candidateId ? [
            'skills.*.experiences' => 'present|array',
            'skills.*.experiences.*' => "required|string|exists:candidate_experiences,public_id,deleted_at,NULL,candidate_id,$candidateId",
        ] : [
            'skills.*.experiences' => 'nullable|array|max:0',
        ];

        return [
            'skills' => 'required|array',
            'skills.*.technology_id' => 'required|integer|exists:technologies,id,deleted_at,NULL',
            'skills.*.years_of_experience' => 'nullable|integer|min:1',
            'skills.*.new_experiences' => 'present|array',
            'skills.*.new_experiences.*' => 'required|integer|in_array:experiences.*.pseudo_id',
        ] + $experienceIdsRules;
    }
}
