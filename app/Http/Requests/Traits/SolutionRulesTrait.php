<?php

namespace App\Http\Requests\Traits;

use App\Enums\SolutionMediumType;
use App\Models\Solution;

trait SolutionRulesTrait
{
    protected function mediaRules(?Solution $solution, bool $isAdminDirectEdit = false): array
    {
        $solutionId = $isAdminDirectEdit
            ? $solution?->id
            : ($solution?->unapproved_change ? $solution?->unapproved_change->id : $solution?->id) ?? false;

        $individual = is_array($this->input('media'))
            ? collect($this->input('media'))->reduce(function ($rules, $medium, $index) use ($solutionId) {
                // Ensure that index is integer. If wrong data is passed,
                // it may actually be a string key, which would cause typing error
                $index = is_int($index) ? $index : 0;

                $edit = $solutionId && ($medium['id'] ?? null);

                if ($edit) {
                    return $rules + [
                        "media.$index.id" => "required|integer|exists:solution_media,id,deleted_at,NULL,solution_id,$solutionId",
                    ];
                }

                return $rules + $this->getSourceRulesForType($index, $medium['type'] ?? null) + [
                    "media.$index.id" => 'prohibited',
                    "media.$index.type" => 'required|string|'.SolutionMediumType::inRule(),
                ];
            }, [])
            : [];

        return [
            'media' => 'present|array',
        ] + $individual;
    }

    protected function getSourceRulesForType(int $index, ?string $type): array
    {
        $enum = SolutionMediumType::tryFrom($type ?? null);

        return $enum ? ["media.$index.source" => $enum->sourceRules()] : [];
    }

    protected function customMessages(): array
    {
        return [
            'client.reviewer.required_without' => __('validation.specific.solution.client_review_reviewer'),
            'client.reviewer_position.required_without' => __('validation.specific.solution.client_review_reviewer'),
        ];
    }
}
