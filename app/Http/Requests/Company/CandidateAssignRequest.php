<?php

namespace App\Http\Requests\Company;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Libs\Overseer\PublishStatus;

class CandidateAssignRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'company_id' => [
                'required',
                Rule::exists('companies', 'public_id')
                    ->whereNull('deleted_at')
                    ->where('publish_status', PublishStatus::Published),
            ],
            'tender_id' => [
                'nullable',
                Rule::exists('tenders', 'public_id')
                    ->whereNull('deleted_at')
                    ->where('publish_status', PublishStatus::Published)
            ],
            'tender_position_id' => [
                'nullable',
                Rule::exists('tender_positions', 'public_id')
                    ->whereNull('deleted_at'),
            ],
            'start_date' => ['required', 'date'],
            'end_date' => ['nullable', 'date', 'after_or_equal:start_date'],
        ];
    }
}
