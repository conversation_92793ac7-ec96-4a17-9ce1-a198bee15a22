<?php

namespace App\Http\Requests\Company;

use App\Enums\Country;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProfileRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $currentYear = now()->year;

        return [
            'name' => 'required|string|max:100',
            'logo_resource_id' => 'required|string|exists:resources,public_id,deleted_at,NULL,type,company_logo_image',
            'cover_resource_id' => 'required|string|exists:resources,public_id,deleted_at,NULL,type,company_cover_image',
            'hq' => 'required|string|max:150',
            'country' => 'required|string|' . Country::inRule(),

            'owner' => 'required|string|max:150',
            'eu_vat' => 'nullable|string|max:20',
            'website' => 'required|url|max:191',
            'linkedin' => 'required|url|max:191',
            'founded' => "required|integer|max:$currentYear",
            'about' => 'nullable|string',

            'company_document_resource_ids' => 'required|array',
            'company_document_resource_ids.*' => [
                'required',
                Rule::exists('resources', 'public_id')
                    ->whereNull('deleted_at')
                    ->whereIn('type', ['company_document', 'company_presentation_file']),
            ],
        ];
    }
}
