<?php

namespace App\Http\Requests\Company;

use Illuminate\Foundation\Http\FormRequest;

class CandidateCvRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'cv_resource_id' => 'required|string|exists:resources,public_id,deleted_at,NULL,type,candidate_cv_file',
        ];
    }
}
