<?php

namespace App\Http\Requests\Tenders;

use App\Enums\TenderCandidateStatus;
use Illuminate\Foundation\Http\FormRequest;

class TenderCandidateStatusRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'status' => 'required|string|'.TenderCandidateStatus::inRuleUnrestricted(),
            'rejection_reason' => 'prohibited_unless:status,'.TenderCandidateStatus::rejectedValueList(',')
                                   .'|nullable|required_if:status,'.TenderCandidateStatus::rejectedWithMandatoryReasonValueList(','),
        ];
    }
}
