<?php

namespace App\Http\Requests;

use App\Http\Requests\Traits\HasDataWrapper;
use Illuminate\Foundation\Http\FormRequest;
use Libs\Warehouse\Warehouse;

class ResourceRequest extends FormRequest
{
    use HasDataWrapper;

    private ?Warehouse $warehouse = null;

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $type = $this->input('type');

        $this->loadDependencies();
        $types = $this->warehouse->uploadableTypes();
        $rules = is_string($type) && in_array($type, $types->toArray()) ? $this->warehouse->rules($type) : [];

        return [
            'type' => "required|string|in:{$types->implode(',')}",
        ] + $rules;
    }

    private function loadDependencies(): void
    {
        if (!$this->warehouse) {
            $this->warehouse = app(Warehouse::class);
        }
    }
}
