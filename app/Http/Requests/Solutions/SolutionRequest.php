<?php

namespace App\Http\Requests\Solutions;

use App\Enums\Country;
use App\Enums\LengthType;
use App\Http\Requests\Traits\SolutionRulesTrait;
use App\Models\Solution;
use App\Repositories\SolutionsRepository;
use Illuminate\Foundation\Http\FormRequest;

class SolutionRequest extends FormRequest
{
    use SolutionRulesTrait;

    public ?Solution $solution = null;

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $solutionId = unslugify($this->route('solution'));
        if ($solutionId) {
            $this->solution = app(SolutionsRepository::class)->findBySlugOrFail($solutionId);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $anonymousClient = (bool) $this->input('client.anonymous');
        $clientIdRequire = $anonymousClient ? 'nullable' : 'nullable|required_without:client.name';
        $clientNameRequire = $anonymousClient ? 'nullable' : 'nullable|required_without:client.id';
        $hasClientReview = (bool) $this->input('client.review');
        $clientReviewerRequire = $hasClientReview ? 'nullable|required_without:client.reviewer_position' : 'nullable';
        $clientReviewerPositionRequire = $hasClientReview ? 'nullable|required_without:client.reviewer' : 'nullable';

        $general = [
            'as_draft' => 'nullable|boolean',

            'name' => 'required|string|max:100',
            'description' => 'required|string|max:150',
            'about' => 'required|string',
            'cover_resource_id' => 'required|string|exists:resources,public_id,deleted_at,NULL,type,solution_cover_image',
            'country' => 'required|string|'.Country::inRule(),
            'main_industry_id' => 'required|integer|exists:industries,id,deleted_at,NULL',
            'length_type' => 'required|string|'.LengthType::inRule(),
            'length' => 'nullable|required_if:length_type,'.LengthType::specificValueList(',').'|integer',
            'ftes' => 'nullable|integer|min:1',
            'value' => 'nullable|integer|min:1',

            'industries' => 'nullable|array',
            'industries.*' => 'required|integer|exists:industries,id,deleted_at,NULL',
            'technologies' => 'nullable|array',
            'technologies.*' => 'required|integer|exists:technologies,id,deleted_at,NULL',

            'in_house' => 'required|boolean',
        ];

        $client = $this->input('in_house') ? [
            'client' => 'prohibited',
        ] : [
            'client.anonymous' => 'required|boolean',
            'client.id' => "$clientIdRequire|integer|exists:clients,id,deleted_at,NULL",
            'client.name' => "$clientNameRequire|string|max:150",
            'client.review' => 'nullable|string',
            'client.reviewer' => "$clientReviewerRequire|string|max:150",
            'client.reviewer_position' => "$clientReviewerPositionRequire|string|max:150",
        ];

        $media = $this->mediaRules($this->solution);

        return $general + $client + $media;
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return $this->customMessages();
    }
}
