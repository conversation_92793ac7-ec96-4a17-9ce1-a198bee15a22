<?php

namespace App\Http\Requests\Auth;

use App\Enums\Country;
use App\Http\Requests\Traits\HasDataWrapper;
use Illuminate\Foundation\Http\FormRequest;
use Libs\Warehouse\Warehouse;

class RegistrationRequest extends FormRequest
{
    use HasDataWrapper;

    private Warehouse $warehouse;

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(Warehouse $warehouse): array
    {
        $this->warehouse = $warehouse;
        $currentYear = now()->year;

        return [
            'tos_privacy_accepted' => 'required|boolean|accepted',
            'recaptcha' => 'required|captcha',

            'company.name' => 'required|string|max:100',
            'company.owner' => 'required|string|max:150',
            'company.website' => 'required|url|max:191',
            'company.linkedin' => 'required|url|max:191',
            'company.founded' => "required|integer|max:$currentYear",
            'company.hq' => 'required|string|max:150',
            'company.country' => 'required|string|' . Country::inRule(),

            'company_documents' => 'required|array',
            'company_documents.*' => $this->warehouse->rules('company_document')['content'],

            'user.name' => 'required|string|max:50',
            'user.surname' => 'required|string|max:50',
            'user.email' => 'required|string|email|unique:users,email',
            'user.phone' => 'required|phone:INTERNATIONAL',
            'user.position' => 'required|string|max:150',
            'user.password' => password_rules(),
        ];
    }
}
