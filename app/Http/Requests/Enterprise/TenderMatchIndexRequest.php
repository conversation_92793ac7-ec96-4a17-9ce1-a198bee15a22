<?php

namespace App\Http\Requests\Enterprise;

use App\Enums\Enterprise\TenderMatchingCompaniesFilter;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TenderMatchIndexRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'companies_filter' => ['nullable', Rule::enum(TenderMatchingCompaniesFilter::class)]
        ];
    }
}
