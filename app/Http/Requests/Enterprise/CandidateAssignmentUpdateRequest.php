<?php

namespace App\Http\Requests\Enterprise;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CandidateAssignmentUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'manager_id' => [
                'nullable',
                Rule::exists(User::class, 'id')
                    ->where('company_id', auth()->user()->company_id)
            ],
        ];
    }
}
