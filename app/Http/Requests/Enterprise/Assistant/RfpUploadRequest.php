<?php

namespace App\Http\Requests\Enterprise\Assistant;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Validator;

class RfpUploadRequest extends FormRequest
{
    const int MIN_TEXT_LENGTH = 100;

    public function rules(): array
    {
        return [
            'file' => ['array', 'required_without:text'],
            'file.*' => ['file', 'mimes:pdf,xlsx,txt'],
            'text' => ['string', 'nullable', 'required_without:file'],
        ];
    }

    public function after(): array
    {
        return [
            function (Validator $validator) {
                $charCount = 0;
                if ($this->has('text')) {
                    $charCount += mb_strlen($this->input('text'));
                }
                if ($this->has('file') && $charCount < self::MIN_TEXT_LENGTH) {
                    foreach ($this->file('file') as $file) {
                        $charCount += mb_strlen($file->get());
                        if ($charCount >= self::MIN_TEXT_LENGTH) {
                            break;
                        }
                    }
                }
                if ($charCount < self::MIN_TEXT_LENGTH) {
                    $validator->errors()->add(
                        'text',
                        'The text or file content must be at least ' . self::MIN_TEXT_LENGTH . ' characters long in total.'
                    );
                }
            }
        ];
    }
}
