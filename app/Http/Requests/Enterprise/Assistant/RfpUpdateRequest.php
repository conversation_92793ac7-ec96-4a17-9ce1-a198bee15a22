<?php

namespace App\Http\Requests\Enterprise\Assistant;

use App\Enums\Enterprise\IndustryType;
use App\Enums\Enterprise\SeniorityLevel;
use App\Enums\Enterprise\TechnologyType;
use App\Enums\Enterprise\Timezone;
use App\Enums\Enterprise\WorkLocation;
use App\Models\Industry;
use App\Models\Language;
use App\Models\Location;
use App\Models\RfpPosition;
use App\Models\Technology;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RfpUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        if ($this->header('Nio-Bypass-Validation')) {
            return [];
        }

        return [
            'info' => ['array'],
            'info.title' => ['required_with:info', 'string'],
            'info.location' => ['nullable', 'array'],
            'info.location.*' => Rule::exists(Location::class, 'id_string'),
            'info.timeline' => ['required_with:info', 'array'],
            'info.timeline.start_date' => ['required_with:info', 'date'],
            'info.timeline.end_date' => ['required_with:info', 'date', 'after_or_equal:info.timeline.start_date'],
            'info.timezone' => ['required_with:info', Rule::enum(Timezone::class)],
            'info.primary_industry' => [
                'required_with:info',
                Rule::exists(Industry::class, 'id_string')->where('type', IndustryType::Primary)
            ],
            'info.description' => ['required_with:info', 'string', 'min:100'],

            'resources' => ['array'],
            'resources.*.id' => ['nullable', Rule::exists(RfpPosition::class, 'id')],
            'resources.*.job_title' => ['required_with:resources', 'string'],
            'resources.*.number_of_resources' => ['required_with:resources', 'integer', 'min:1'],
            'resources.*.seniority_level' => ['required_with:resources', Rule::enum(SeniorityLevel::class)],
            'resources.*.workload' => ['required_with:resources', 'integer', 'min:0', 'max:100'],
            'resources.*.work_location' => ['nullable', Rule::enum(WorkLocation::class)],
            'resources.*.notes' => ['nullable', 'string'],
            'resources.*.languages' => ['required_with:resources', 'array'],
            'resources.*.languages.*' => ['required_with:resources', Rule::exists(Language::class, 'id_string')],
            'resources.*.hourly_rate_expectations' => ['required_with:resources', 'array', 'size:2'],
            'resources.*.hourly_rate_expectations.0' => [
                'required_with:resources',
                'numeric',
                'min:0',
                'lte:resources.*.hourly_rate_expectations.1'
            ],
            'resources.*.hourly_rate_expectations.1' => [
                'required_with:resources',
                'numeric',
                'min:0'
            ],
            'resources.*.technologies' => ['required_with:resources', 'array'],
            'resources.*.technologies.*' => [
                'required_with:resources',
                Rule::exists(Technology::class, 'uuid')->where('type', TechnologyType::Technology)
            ],
            'resources.*.mandatory_technology' => [
                'nullable',
                'in_array:resources.*.technologies.*',
                Rule::exists(Technology::class, 'uuid')->where('type', TechnologyType::Technology)
            ],
            'resources.*.key_tools' => [
                'nullable',
                'array'
            ],
            'resources.*.key_tools.*' => Rule::exists(Technology::class, 'uuid')
                ->where('type', TechnologyType::Tool),
        ];
    }

    public function messages(): array
    {
        $messages = [];

        foreach ($this->rules() as $field => $rules) {
            $messages["{$field}.required_with"] = 'This field is required.';
        }

        return $messages;
    }
}
