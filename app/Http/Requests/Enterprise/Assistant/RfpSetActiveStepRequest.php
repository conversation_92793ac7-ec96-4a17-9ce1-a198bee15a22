<?php

namespace App\Http\Requests\Enterprise\Assistant;

use App\Enums\Enterprise\AssistantStep;
use App\Models\Rfp;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RfpSetActiveStepRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'step' => ['required', Rule::in($this->getAllowedSteps())],
        ];
    }

    private function getAllowedSteps(): array
    {
        $rfp = $this->route('rfp');
        if (!$rfp) {
            return AssistantStep::cases();
        } else if ($rfp->step === AssistantStep::Final) {
            return [AssistantStep::Final];
        } else {
            $allowedSteps = [
                AssistantStep::Info
            ];
            if ($rfp->rfp_api_called) {
                $allowedSteps[] = AssistantStep::Resources;
                if ($rfp->resources_extract_api_called) {
                    $allowedSteps[] = AssistantStep::Final;
                }
            }
            return $allowedSteps;
        }
    }
}
