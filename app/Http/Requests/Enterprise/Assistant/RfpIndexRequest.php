<?php

namespace App\Http\Requests\Enterprise\Assistant;

use App\Enums\Enterprise\RfpFilterType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class RfpIndexRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'type' => ['nullable', Rule::enum(RfpFilterType::class)],
            'search' => ['nullable', 'string', 'min:2'],
        ];
    }
}
