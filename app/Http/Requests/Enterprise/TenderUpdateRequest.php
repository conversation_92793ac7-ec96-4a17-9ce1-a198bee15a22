<?php

namespace App\Http\Requests\Enterprise;

use Illuminate\Foundation\Http\FormRequest;

class TenderUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'submissions_deadline' => 'sometimes|required|date|after:now',
            'simulation' => 'sometimes|required|boolean',
        ];
    }
}
