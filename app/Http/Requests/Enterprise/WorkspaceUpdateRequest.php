<?php

namespace App\Http\Requests\Enterprise;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\Company;


class WorkspaceUpdateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'company_ids' => ['required', 'array'],
            'company_ids.*' => Rule::exists(Company::class, 'public_id')
        ];
    }

    /**
     * Get custom attribute names for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'company_ids' => 'list of companies',
            'company_ids.*' => 'company ID',
        ];
    }
}
