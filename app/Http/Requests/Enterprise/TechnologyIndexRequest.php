<?php

namespace App\Http\Requests\Enterprise;

use App\Enums\Enterprise\RfpFilterType;
use App\Enums\Enterprise\TechnologyType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TechnologyIndexRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'type' => ['nullable', Rule::enum(TechnologyType::class)],
            'search' => ['nullable', 'string', 'min:2'],
        ];
    }
}
