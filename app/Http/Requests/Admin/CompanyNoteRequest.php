<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Not to be confused with {@link CompanyNotesRequest}.
 * This one is for individual notes (or logs) from staff users.
 * The other is for internal generic notes for company.
 */
class CompanyNoteRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'note' => 'required|string',
        ];
    }
}
