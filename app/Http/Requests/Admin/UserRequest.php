<?php

namespace App\Http\Requests\Admin;

use App\Enums\UserRole;
use App\Models\User;
use App\Repositories\UsersRepository;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;

class UserRequest extends FormRequest
{
    private ?User $user = null;

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $userSlug = $this->route('user');
        if ($userSlug) {
            $this->user = app(UsersRepository::class)->users()->findBySlugOrFail($userSlug);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $passwordRequired = $this->user ? 'nullable' : 'required';

        $emailCompanyRules = ! $this->user ? [
            'company_id' => 'required|string|exists:companies,public_id,deleted_at,NULL',
        ] : [];

        return [
            'name' => 'required|string|max:50',
            'surname' => 'required|string|max:50',
            'position' => 'required|string|max:150',
            'division' => 'nullable|string|max:150',
            'department' => 'nullable|string|max:150',
            'email' => "required|string|email|unique:users,email,{$this->user?->id}",
            'phone' => 'required|phone:INTERNATIONAL',
            'avatar_resource_id' => 'nullable|string|exists:resources,public_id,deleted_at,NULL,type,user_avatar_image',
            'password' => password_rules($passwordRequired),
            'roles' => 'required|array',
            'roles.*' => [
                'required',
                Rule::enum(UserRole::class)
                    ->when(
                        $this->user,
                        fn (Enum $rule): Enum => $rule->only(UserRole::allowedRoles($this->user->company)),
                        fn (Enum $rule): Enum => $rule->except(UserRole::SuperAdmin),
                    )
            ]
        ] + $emailCompanyRules;
    }
}
