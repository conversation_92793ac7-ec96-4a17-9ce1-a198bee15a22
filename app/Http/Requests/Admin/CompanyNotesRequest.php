<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Not to be confused with {@link CompanyNoteRequest}.
 * This class is for internal generic notes for company.
 * The other is for individual notes (or logs) from staff users.
 */
class CompanyNotesRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'notes' => 'nullable|string',
        ];
    }
}
