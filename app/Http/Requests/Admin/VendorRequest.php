<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;
use Libs\Overseer\PublishStatus;

class VendorRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'publish_status' => 'sometimes|required|string|'.PublishStatus::inRule(),
            'main_industry_id' => 'required|integer|exists:industries,id,deleted_at,NULL',
            'employees' => 'nullable|integer|min:0',
            'developers' => 'nullable|integer|min:0',

            'offering_resources' => 'nullable|boolean|accepted_if:offering_solutions,false',
            'offering_solutions' => 'nullable|boolean|accepted_if:offering_resources,false',
            'payment_time_and_material' => 'nullable|boolean|accepted_if:payment_fixed_price,false',
            'payment_fixed_price' => 'nullable|boolean|accepted_if:payment_time_and_material,false',
            'notify_irrelevant_offers' => 'nullable|boolean',

            'rate_junior' => 'nullable|integer|min:1',
            'rate_medior' => 'nullable|integer|min:1',
            'rate_senior' => 'nullable|integer|min:1',
            'rate_lead' => 'nullable|integer|min:1',

            'industries' => 'nullable|array',
            'industries.*' => 'required|integer|exists:industries,id,deleted_at,NULL',

            'technologies' => 'nullable|array',
            'technologies.*' => 'required|integer|exists:technologies,id,deleted_at,NULL',

            'clients' => 'nullable|array',
            'clients.*.id' => 'nullable|integer|exists:clients,id,deleted_at,NULL',
            'clients.*.name' => 'nullable|required_without:clients.*.id|string|max:150',

            'employee_positions' => 'nullable|array',
            'employee_positions.*' => 'required|integer|exists:employee_positions,id,deleted_at,NULL',
        ];
    }
}
