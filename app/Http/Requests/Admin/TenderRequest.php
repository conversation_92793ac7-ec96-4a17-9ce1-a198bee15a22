<?php

namespace App\Http\Requests\Admin;

use App\Enums\Country;
use App\Enums\LengthType;
use App\Enums\PaymentType;
use App\Enums\ServiceType;
use App\Enums\TenderProcessingType;
use App\Enums\TenderStatus;
use App\Models\Tender;
use App\Repositories\TendersRepository;
use Illuminate\Foundation\Http\FormRequest;
use Libs\Overseer\PublishStatus;

class TenderRequest extends FormRequest
{
    public ?Tender $tender = null;

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $tenderId = unslugify($this->route('tender'));
        if ($tenderId) {
            $this->tender = app(TendersRepository::class)->query()->findBySlugOrFail($tenderId);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $specificLengthTypes = LengthType::specificValueList(',');
        $lengthTypes = $this->input('service_type') === ServiceType::Resources->value ? LengthType::Unspecified->value : $specificLengthTypes;

        $inHouseProject = (bool) $this->input('project.in_house');
        $anonymousProject = (bool) $this->input('project.anonymous');
        $clientIdRequire = $inHouseProject || $anonymousProject ? 'nullable' : 'nullable|prohibited_if:project.in_house,true';
        $clientNameRequire = $inHouseProject || $anonymousProject ? 'nullable' : 'nullable|prohibited_if:project.in_house,true';

        $companyRule = $this->tender ? [] : [
            'company_id' => 'required|string|exists:companies,public_id,deleted_at,NULL',
        ];

        return $companyRule + [
            'anonymous_company' => 'required|boolean',
            'publish_status' => 'required|string|'.PublishStatus::inRule(),
            'status' => 'required|string|'.TenderStatus::inRule(),
            'submissions_deadline' => 'required|date',
            'end_of_incubation' => 'nullable|required_if:status,'.TenderStatus::Incubation->value.'|date|before:submissions_deadline',
            'processing_type' => 'required|string|'.TenderProcessingType::inRule(),
            'evaluation_date' => 'nullable|required_if:processing_type,'.TenderProcessingType::CustomDate->value.'|date',
            'service_type' => 'required|string|'.ServiceType::inRule(),
            'payment_type' => 'nullable|required_if:service_type,'.ServiceType::Solution->value.'|prohibited_if:service_type,resources|string|'.PaymentType::inRule(),
            'price' => 'nullable|required_if:service_type,'.ServiceType::Solution->value.'|prohibited_if:service_type,resources|integer|min:0',
            'price_to' => 'nullable|integer|gt:price',
            'length_type' => "required|string|in:$lengthTypes",
            'length' => "nullable|required_if:length_type,$specificLengthTypes|prohibited_unless:length_type,$specificLengthTypes|integer|min:1",
            'name' => 'required|string|max:100',
            'description' => 'required|string',
            'about' => 'required|string',
            'cover_resource_id' => 'required|string|exists:resources,public_id,deleted_at,NULL,type,tender_cover_image',
            'country' => 'required|string|'.Country::inRule(),

            'project.main_industry_id' => 'required|integer|exists:industries,id,deleted_at,NULL',
            'project.in_house' => 'required|boolean',
            'project.client_id' => "$clientIdRequire|integer|exists:clients,id,deleted_at,NULL",
            'project.client_name' => "$clientNameRequire|string|max:150",
            'project.anonymous' => 'required|boolean',
            'project.industries' => 'nullable|array',
            'project.industries.*' => 'required|integer|exists:industries,id,deleted_at,NULL',
            'project.technologies' => 'nullable|array',
            'project.technologies.*' => 'required|integer|exists:technologies,id,deleted_at,NULL',
            'project.name' => 'nullable|required_if:project.anonymous,false|string|max:100',
            'project.about' => 'nullable|required_if:project.anonymous,false|string',
        ];
    }
}
