<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class TenderVendorNotifyRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'message' => 'required|string',        
            'vendor_public_ids' => 'nullable|array',
            'vendor_public_ids.*' => 'exists:vendors,public_id'
        ];
    }
}
