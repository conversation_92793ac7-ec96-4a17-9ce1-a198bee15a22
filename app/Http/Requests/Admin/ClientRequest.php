<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class ClientRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:100',
            'description' => 'nullable|string|max:150',
            'logo_resource_id' => 'nullable|string|exists:resources,public_id,deleted_at,NULL,type,client_logo_image',
        ];
    }
}
