<?php

namespace App\Http\Requests\Admin;

use App\Enums\Country;
use App\Models\Company;
use App\Repositories\CompaniesRepository;
use Illuminate\Foundation\Http\FormRequest;

class CompanyRequest extends FormRequest
{
    public ?Company $company = null;
    private ?CompaniesRepository $companiesRepository = null;

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        $companyId = unslugify($this->route('company'));
        if ($companyId) {
            $this->company = app(CompaniesRepository::class)->query()->findBySlugOrFail($companyId);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $currentYear = now()->year;

        return [
            'name' => 'required|string|max:100',
            'logo_resource_id' => 'required|string|exists:resources,public_id,deleted_at,NULL,type,company_logo_image',
            'cover_resource_id' => 'required|string|exists:resources,public_id,deleted_at,NULL,type,company_cover_image',
            'hq' => 'required|string|max:150',
            'country' => 'required|string|max:150|'.Country::inRule(),
            'is_public' => 'required|boolean',

            'owner' => 'required|string|max:150',
            'eu_vat' => 'nullable|string|max:20',
            'website' => 'required|url|max:191',
            'linkedin' => 'required|url|max:191',
            'founded' => "required|integer|max:$currentYear",
            'about' => 'nullable|string',

            'presentation_resource_id' => 'nullable|string|exists:resources,public_id,deleted_at,NULL,type,company_presentation_file',
            'presentation_url' => 'nullable|url|max:191',
        ];
    }
}
