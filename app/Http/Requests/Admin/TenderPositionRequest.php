<?php

namespace App\Http\Requests\Admin;

use App\Enums\LengthType;
use App\Models\EmployeePosition;
use App\Models\TenderPosition;
use Illuminate\Foundation\Http\FormRequest;

class TenderPositionRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $seniorities = EmployeePosition::where('id', $this->input('employee_position_id'))->value('seniorities') ?? '';
        $senioritiesArraySize = $seniorities ? 'min:1' : 'min:0|max:0';

        return [
            'employee_position_id' => 'required|integer|exists:employee_positions,id,deleted_at,NULL',
            'seniorities' => "present|array|$senioritiesArraySize",
            'seniorities.*' => "required|string|in:$seniorities",
            'name' => 'required|string|max:100',
            'description' => 'required|string',
            'requirements' => 'required|string',
            'must_have_requirements' => 'nullable|string',
            'technologies' => 'required|array|min:1',
            'technologies.*' => 'required|integer|exists:technologies,id,deleted_at,NULL',
            'price' => 'required|integer|min:0',
            'price_to' => 'nullable|integer|gt:price',
            'start_date' => 'nullable|date',
            'length_type' => 'required|string|'.LengthType::inRuleSpecific(),
            'length' => 'required|integer|min:1',
            'possible_extension' => 'required|boolean',
            'workload' => 'required|integer|min:1|max:' . TenderPosition::FULL_WORKLOAD,
            'count' => 'required|integer|min:1',
            'interview' => 'nullable|string|max:150',
            'equipment' => 'nullable|string|max:150',
        ];
    }
}
