<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class IndustryRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $industryId = (int) $this->route('industry');
        $parentIdExclude = $industryId ? "|not_in:{$industryId}" : '';

        return [
            'name' => 'required|string|max:100',
            'parent_id' => "nullable|integer|exists:industries,id,deleted_at,NULL{$parentIdExclude}",
            'featured' => 'required|boolean',
        ];
    }
}
