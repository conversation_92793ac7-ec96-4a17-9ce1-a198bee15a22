<?php

namespace App\Http\Requests\Admin;

use App\Enums\Seniority;
use Illuminate\Foundation\Http\FormRequest;

class EmployeePositionRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:100',
            'name_plural' => 'required|string|max:100',
            'hireable' => 'required|boolean',
            'seniorities' => 'nullable|array',
            'seniorities.*' => 'required|string|'.Seniority::inRule(),
        ];
    }
}
