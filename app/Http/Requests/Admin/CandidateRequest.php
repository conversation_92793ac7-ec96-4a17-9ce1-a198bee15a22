<?php

namespace App\Http\Requests\Admin;

use App\Enums\Country;
use App\Enums\Education;
use App\Enums\Seniority;
use App\Http\Requests\Traits\CandidateRulesTrait;
use App\Models\Candidate;
use App\Repositories\CandidatesRepository;
use Illuminate\Foundation\Http\FormRequest;

class CandidateRequest extends FormRequest
{
    use CandidateRulesTrait;

    private ?Candidate $candidate = null;
    private ?CandidatesRepository $candidatesRepository = null;

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $candidateId = unslugify($this->route('candidate'));
        if ($candidateId) {
            $this->candidate = app(CandidatesRepository::class)->query()->findBySlugOrFail($candidateId);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $vendor = ! $this->route('candidate') ? [
            'vendor_id' => 'required|string|exists:vendors,public_id,deleted_at,NULL',
        ] : [];

        $general = [
            'internal_name' => 'required|string|max:50',
            'name' => 'required|string|max:50',
            'country' => 'required|string|'.Country::inRule(),
            'city' => 'nullable|string|max:50',
            'rate' => 'required|integer|min:1',

            'profession' => 'required|string|max:50',
            'seniority' => 'required|string|'.Seniority::inRule(),
            'last_job_title' => 'required|string|max:50',
            'years_of_experience' => 'required|integer|min:1',
            'highest_education' => 'nullable|string|'.Education::inRule(),
            'field_of_study' => 'nullable|string|max:50',

            'cv_resource_id' => 'nullable|string|exists:resources,public_id,deleted_at,NULL,type,candidate_cv_file',
        ];

        $experiences = $this->experiencesRules($this->candidate);
        $skills = $this->skillsRules($this->candidate);

        return $vendor + $general + $experiences + $skills;
    }
}
