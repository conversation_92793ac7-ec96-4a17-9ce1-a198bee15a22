<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class TenderCandidateNotifyRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'message' => 'required|string',        
            'candidate_public_ids' => 'nullable|array',
            'candidate_public_ids.*' => 'exists:candidates,public_id'
        ];
    }
}
