<?php

namespace App\Http\Requests\Admin;

use App\Models\User;
use App\Repositories\UsersRepository;
use Illuminate\Foundation\Http\FormRequest;

class StaffUserRequest extends FormRequest
{
    private ?User $superAdmin = null;

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation(): void
    {
        $userSlug = $this->route('user');
        if ($userSlug) {
            $this->superAdmin = app(UsersRepository::class)->superAdmins()->findBySlugOrFail($userSlug);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $passwordRequired = $this->superAdmin ? 'nullable' : 'required';

        return [
            'name' => 'required|string|max:50',
            'surname' => 'required|string|max:50',
            'email' => "required|string|email|unique:users,email,{$this->superAdmin?->id}",
            'phone' => 'nullable|phone:INTERNATIONAL',
            'position' => 'required|string|max:150',
            'division' => 'nullable|string|max:150',
            'department' => 'nullable|string|max:150',
            'avatar_resource_id' => 'nullable|string|exists:resources,public_id,deleted_at,NULL,type,user_avatar_image',
            'password' => password_rules($passwordRequired),
        ];
    }
}
