<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class TechnologyRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $technologyId = $this->route('technology') ? (int) $this->route('technology') : null;
        $parentIdExclude = $technologyId ? "|not_in:$technologyId" : '';

        return [
            'name' => 'required|string|max:100',
            'emsi_id' => "nullable|string|max:100|unique:technologies,emsi_id,$technologyId,id,deleted_at,NULL",
            'parent_id' => "nullable|integer|exists:technologies,id,deleted_at,NULL$parentIdExclude",
            'featured' => 'required|boolean',
            'logo_resource_id' => 'nullable|string|exists:resources,public_id,deleted_at,NULL,type,technology_logo_image',
        ];
    }
}
