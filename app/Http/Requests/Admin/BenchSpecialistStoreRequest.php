<?php

namespace App\Http\Requests\Admin;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class BenchSpecialistStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'candidate_ids' => 'required|array',
            'candidate_ids.*' => 'exists:candidates,public_id,deleted_at,NULL',
            'available_from' => 'required|date|after_or_equal:today',
        ];
    }
}
