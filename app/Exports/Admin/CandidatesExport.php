<?php

namespace App\Exports\Admin;

use App\Exports\SimpleCollectionExport;
use App\Models\Candidate;
use App\Models\CandidateSkill;
use Illuminate\Support\Collection;

class CandidatesExport extends SimpleCollectionExport
{
    /**
     * @param  Collection<Candidate>  $candidates
     */
    public function __construct(
        private Collection $candidates,
    ) {}

    public function headings(): array
    {
        return [
            __('exports.candidates.name'),
            __('exports.candidates.position'),
            __('exports.candidates.rate'),
            __('exports.candidates.rate_commission'),
            __('exports.candidates.profession'),
            __('exports.candidates.seniority'),
            __('exports.candidates.location'),
            __('exports.candidates.years_of_experience'),
            __('exports.candidates.last_job_title'),
            __('exports.candidates.highest_education'),
            __('exports.candidates.skills'),
        ];
    }

    public function collection(): Collection
    {
        return $this->candidates->map(fn (Candidate $candidate) => [
            $candidate->name,
            $this->position($candidate),
            '€'.$candidate->rate,
            '€ '.format_money($candidate->rateCommission, 1),
            $candidate->profession,
            $candidate->seniority->trans(),
            $this->location($candidate),
            trans_choice('global.labels.years', $candidate->years_of_experience),
            $candidate->last_job_title,
            $this->education($candidate),
            $this->skills($candidate),
        ]);
    }

    private function position(Candidate $candidate): string
    {
        // Only one position is scoped per candidate, and it is guaranteed
        // that candidate clone is associated with exactly one
        $position = $candidate->positions->first();

        return $position->name;

    }

    private function location(Candidate $candidate): string
    {
        $country = $candidate->country->trans();

        if (! $candidate->city) {
            return $country;
        }

        return "$country ($candidate->city)";
    }

    private function education(Candidate $candidate): string
    {
        if (! $candidate->highest_education) {
            return __('global.labels.unspecified');
        }

        if (! $candidate->field_of_study) {
            return $candidate->highest_education->trans();
        }

        return "{$candidate->highest_education->trans()} ($candidate->field_of_study)";
    }

    private function skills(Candidate $candidate): string
    {
        return $candidate->skills->map(function (CandidateSkill $skill) {
            if (! $skill->years_of_experience) {
                return $skill->technology->name;
            }

            $yearsOfExperience = trans_choice('global.labels.years', $skill->years_of_experience);

            return "{$skill->technology->name} ($yearsOfExperience)";
        })->join('; ');
    }
}
