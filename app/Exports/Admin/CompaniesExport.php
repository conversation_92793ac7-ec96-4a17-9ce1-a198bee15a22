<?php

namespace App\Exports\Admin;

use App\Exports\SimpleCollectionExport;
use App\Models\Company;
use Illuminate\Support\Collection;

class CompaniesExport extends SimpleCollectionExport
{
    /**
     * @param  Collection<Company>  $companies
     */
    public function __construct(
        private Collection $companies,
    ) {}

    public function headings(): array
    {
        return [
            __('exports.companies.name'),
            __('exports.companies.country'),
            __('exports.companies.contact'),
            __('exports.companies.contact_email'),
        ];
    }

    public function collection(): Collection
    {
        return $this->companies->map(fn (Company $company) => [
            $company->name,
            $company->country->trans(),
            $company->users->first()?->display_name ?? 'No contact person',
            $company->users->first()?->email,
        ]);
    }
}
