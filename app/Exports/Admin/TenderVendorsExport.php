<?php

namespace App\Exports\Admin;

use App\Exports\SimpleCollectionExport;
use App\Models\Vendor;
use Illuminate\Support\Collection;

class TenderVendorsExport extends SimpleCollectionExport
{
    /**
     * @param  Collection<Vendor>  $vendors
     */
    public function __construct(
        private Collection $vendors,
    ) {}

    public function headings(): array
    {
        return [
            __('exports.companies.name'),
            __('exports.companies.country'),
            __('exports.companies.contact'),
            __('exports.companies.contact_email'),
        ];
    }

    public function collection(): Collection
    {
        return $this->vendors->map(fn (Vendor $vendor) => [
            $vendor->company->name,
            $vendor->company->country->trans(),
            $vendor->company->users->first()?->display_name ?? 'No contact person',
            $vendor->company->users->first()?->email,
        ]);
    }
}
