<?php

namespace App\Data\Enterprise;

use App\Enums\Enterprise\CompanyImportStatus;
use Illuminate\Http\Request;

class CompanyImportDto
{
    public function __construct(
        public string $name,
        public string $import_id,
        public array $alternatives = [],
        public CompanyImportStatus $status = CompanyImportStatus::Pending,
        public ?string $company_id = null
    ) {}

    /**
     * @param Request $request
     * @return self
     */
    public static function fromRequest(Request $request): self
    {
        return new self(
            $request->input('name'),
            $request->input('import_id'),
            [],
            filled($request->input('status'))
                ? CompanyImportStatus::tryFrom($request->input('status')) ?? CompanyImportStatus::Pending
                : CompanyImportStatus::Pending
        );
    }
}
