<?php

namespace App\Exceptions;

use Illuminate\Http\Client\RequestException;
use Illuminate\Http\Client\Response;

class VerboseRequestException extends RequestException
{
    public function __construct(Response $response)
    {
        parent::__construct($response);
    }

    protected function prepareMessage(Response $response)
    {
        $message = "HTTP request returned status code {$response->status()}";

        // Double encoding for security, to ensure that response is indeed JSON
        // and that when we decode it back, we only get one line that will be
        // escaped and will not cause log injection.
        $body = json_encode($response->json());

        return $message.":\n$body\n";
    }
}
