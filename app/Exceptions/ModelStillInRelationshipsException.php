<?php

namespace App\Exceptions;

use Illuminate\Database\Eloquent\Model;

class ModelStillInRelationshipsException extends AppException
{
    public function __construct(Model $model)
    {
        $modelName = get_class($model);
        $modelId = $model->getKey();

        parent::__construct(
            message: __('exceptions.model_still_in_relationships'),
            code: 409,
            devMessage: "Model [{$modelName}] with id [{$modelId}] still has active relationships and therefore cannot be removed.",
        );
    }
}
