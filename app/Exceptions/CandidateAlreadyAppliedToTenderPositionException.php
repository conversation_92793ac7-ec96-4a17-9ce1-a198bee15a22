<?php

namespace App\Exceptions;

use App\Models\Candidate;
use App\Models\TenderPosition;

class CandidateAlreadyAppliedToTenderPositionException extends AppException
{
    public function __construct(TenderPosition $position, Candidate $candidate)
    {
        parent::__construct(
            message: __('exceptions.candidate_already_applied_to_tender_position'),
            code: 409,
            devMessage: "Candidate [$candidate->id] is already applied to tender position [$position->id].",
        );
    }
}
