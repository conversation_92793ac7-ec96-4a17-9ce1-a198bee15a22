<?php

namespace App\Exceptions;

use Carbon\CarbonInterval;

class BrokenCircuitException extends AppException
{
    public function __construct(string $id, CarbonInterval $decay)
    {
        parent::__construct(
            message: __('exceptions.service_unavailable'),
            code: 503,
            devMessage: "Circuit [$id] is broken right now and will be available in under [{$decay->forHumans()}].",
        );
    }
}
