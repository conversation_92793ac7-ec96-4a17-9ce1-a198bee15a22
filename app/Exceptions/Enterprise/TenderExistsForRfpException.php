<?php

namespace App\Exceptions\Enterprise;

use App\Exceptions\AppException;
use App\Models\Rfp;

class TenderExistsForRfpException extends AppException
{
    public function __construct(Rfp $rfp)
    {
        parent::__construct(
            message: __('tender_already_exists_for_rfp'),
            code: 422,
            devMessage: "Unable to perform requested action on RFP [$rfp->id] as it is used in tender [$rfp->tender_id].",
        );
    }
}
