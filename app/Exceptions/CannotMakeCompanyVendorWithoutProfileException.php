<?php

namespace App\Exceptions;

use App\Models\Company;

class CannotMakeCompanyVendorWithoutProfileException extends AppException
{
    public function __construct(Company $company)
    {
        parent::__construct(
            message: __('exceptions.cannot_make_company_vendor_without_profile'),
            code: 409,
            devMessage: "Company [$company->id] cannot have vendor profile enabled because it is not defined.",
        );
    }
}
