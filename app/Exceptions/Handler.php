<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Libs\Warehouse\Exceptions\MissingFileExtensionException;
use Psr\Log\LogLevel;
use Throwable;

class Handler extends ExceptionHandler
{
    private const REQUEST_PARAM_EXCEPTION_UUID = 'app_exception_handler_exception_uuid';

    /**
     * A list of exception types with their corresponding custom log levels.
     *
     * @var array<class-string<Throwable>, LogLevel::*>
     */
    protected $levels = [
        //
    ];

    /**
     * A list of the exception types that are not reported.
     *
     * @var array<int, class-string<Throwable>>
     */
    protected $dontReport = [
        MissingFileExtensionException::class,
    ];

    /**
     * A list of the inputs that are never flashed to the session on validation exceptions.
     *
     * @var array<int, string>
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     */
    public function register(): void
    {
        $this->reportable(function (Throwable $e) {
            if (app()->bound('sentry') && $this->shouldReport($e)) {
                app('sentry')->captureException($e);
            }
        });
    }

    /**
     * Determine if the exception handler response should be JSON.
     *
     * @param  Request  $request
     */
    protected function shouldReturnJson($request, Throwable $e): bool
    {
        // Whole application is API based, so always return json
        return $request->expectsJson();
    }

    /**
     * Get the default exception context variables for logging.
     */
    protected function exceptionContext(Throwable $e): array
    {
        $context = parent::exceptionContext($e);

        // Ensure that UUID is always appended for the exception
        if (! array_has($context, 'uuid')) {
            $context['uuid'] = $this->currentExceptionUUID($e);
        }

        return $context;
    }

    /**
     * Convert the given exception to an array.
     */
    protected function convertExceptionToArray(Throwable $e): array
    {
        $error = parent::convertExceptionToArray($e);

        // If the error message is a generic one, we should provide
        // a more clear one, as well as appending UUID to the message
        if (! config('app.debug') && $this->isGenericErrorMessage($error)) {
            $error = $this->convertToInternalServerError($e, $error);
        }

        return $this->encapsulateError($error);
    }

    /**
     * Convert a validation exception into a JSON response.
     *
     * @param  Request  $request
     */
    protected function invalidJson($request, ValidationException $exception): JsonResponse
    {
        $response = parent::invalidJson($request, $exception);
        $response->setData(
            $this->encapsulateError($response->getData()),
        );

        return $response;
    }

    /**
     * Verifies whether given generated error message is a generic one or not
     */
    private function isGenericErrorMessage(array $error): bool
    {
        return array_get($error, 'message') === 'Server Error';
    }

    /**
     * Converts generic error message to internal server error message
     */
    private function convertToInternalServerError(Throwable $e, array $error): array
    {
        return [
            'uuid' => $this->currentExceptionUUID($e),
            'message' => __('exceptions.internal_server_error'),
        ];
    }

    /**
     * Retrieves UUID belonging to the current request exception
     */
    private function currentExceptionUUID(Throwable $e): string
    {
        // If throwable is descendant of AppException, we try
        // to retrieve its UUID. It may not be set however, so
        // we cannot immediately return.
        $uuid = $e instanceof AppException ? $e->getUUID() : null;

        // If no UUID of the exception is currently set, then
        // we need to fetch one from the request, and generate
        // one if not already available
        if (! $uuid) {
            if (! request()->attributes->has(self::REQUEST_PARAM_EXCEPTION_UUID)) {
                request()->attributes->set(self::REQUEST_PARAM_EXCEPTION_UUID, Str::uuid());
            }
            $uuid = request()->attributes->get(self::REQUEST_PARAM_EXCEPTION_UUID);
        }

        return $uuid;
    }

    /**
     * Takes error and encapsulates it into common error structure
     *
     * @param  mixed  $error  error payload to be encapsulated
     */
    private function encapsulateError(mixed $error): array
    {
        return [
            'error' => $error,
        ];
    }
}
