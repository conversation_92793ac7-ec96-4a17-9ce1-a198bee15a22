<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Psr\Log\LoggerInterface;
use Throwable;

abstract class AppException extends Exception
{
    public function __construct(
        string $message = '',
        int $code = 0,
        ?Throwable $previous = null,
        protected ?string $devMessage = null,
        protected string $logLevel = 'debug',
        protected ?string $type = null,
        protected ?string $uuid = null,
    ) {
        parent::__construct($message, $code, $previous);

        $this->devMessage = $this->devMessage ?? 'No dev message provided';
        $this->type = $this->type ?? str_before(snake_case(class_basename($this)), '_exception');
        $this->uuid = $this->uuid ?? Str::uuid();
    }

    /**
     * Report the exception.
     */
    public function report(LoggerInterface $logger): ?bool
    {
        $context = array_filter([
            'uuid' => $this->uuid,
            'userId' => auth()->id(),
            'exception' => $this,
        ]);

        $logger->{$this->logLevel}($this->devMessage, $context);

        return null;
    }

    /**
     * Render the exception into an HTTP response.
     */
    public function render(Request $request): JsonResponse|bool
    {
        return response()->json([
            'error' => [
                'type' => $this->type,
                'uuid' => $this->uuid,
                'message' => $this->message,
            ],
        ], $this->code);
    }

    /**
     * Returns UUID of the exception
     */
    public function getUUID(): ?string
    {
        return $this->uuid;
    }

    /**
     * Whether this exception is considered to be a client error
     */
    public function isClientError(): bool
    {
        return $this->code >= 400 && $this->code <= 499;
    }

    /**
     * Whether this exception is considered to be a server error
     */
    public function isServerError(): bool
    {
        return $this->code >= 500 && $this->code <= 599;
    }
}
