<?php

namespace App\Exceptions;

use Illuminate\Database\Eloquent\Model;

class ModelCannotBeDeletedException extends AppException
{
    public function __construct(Model $model)
    {
        $modelName = get_class($model);
        $modelId = $model->getKey();

        parent::__construct(
            message: __('exceptions.model_cannot_be_deleted'),
            code: 409,
            devMessage: "Model [{$modelName}] with id [{$modelId}] cannot be deleted at the moment.",
        );
    }
}
