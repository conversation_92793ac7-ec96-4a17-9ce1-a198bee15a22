<?php

namespace App\Exceptions;

use App\Enums\TenderCandidateStatus;
use App\Models\Candidate;
use App\Models\TenderPosition;

class InvalidTenderCandidateNextStatusException extends AppException
{
    public function __construct(TenderPosition $position, Candidate $candidate, TenderCandidateStatus $fromStatus, TenderCandidateStatus $toStatus)
    {
        parent::__construct(
            message: __('exceptions.invalid_candidate_next_status'),
            code: 409,
            devMessage: "Candidate [$candidate->id] of tender position [$position->id] cannot be moved from [$fromStatus->value] status to [$toStatus->value] status.",
        );
    }
}
