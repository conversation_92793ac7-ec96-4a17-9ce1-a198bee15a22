<?php

namespace App\Services;

use App\Enums\UserRole;
use App\Mail\Auth\NewCompanyRegistration;
use App\Mail\Auth\ThankYouForRegistration;
use App\Models\Company;
use App\Repositories\CompaniesRepository;
use App\Repositories\UsersRepository;
use Libs\Warehouse\Warehouse;

class RegistrationService
{
    public function __construct(
        private CompaniesRepository $companiesRepository,
        private UsersRepository $usersRepository,
        private NotificationService $notificationService,
        private Warehouse $warehouse,
    ) {}

    public function register(array $companyData, array $companyDocuments, array $userData): void
    {
        in_transaction(function () use ($companyData, $companyDocuments, $userData) {
            $company = $this->companiesRepository->store($companyData);
            $this->attachDocuments($company, $companyDocuments);
            $userData['roles'] = [UserRole::Vendor->value];
            $user = $this->usersRepository->storeUser($company, $userData);

            $this->notificationService->notifyStaff(new NewCompanyRegistration($company, $user));
            $this->notificationService->notifyCompanyUsers($company, new ThankYouForRegistration($company));

            // FIXME: confirm mail, do we really need it?
        });
    }

    private function attachDocuments(Company $company, array $companyDocuments): void
    {
        foreach ($companyDocuments as $file) {
            $documentResource = $this->warehouse->store('company_document', $file);
            $company->document_resources()->attach($documentResource->id);
        }
    }
}
