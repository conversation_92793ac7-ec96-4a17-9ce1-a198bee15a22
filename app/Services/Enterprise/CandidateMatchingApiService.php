<?php
declare(strict_types=1);

namespace App\Services\Enterprise;

use App\Enums\Enterprise\TenderMatchingCompaniesFilter;
use App\Models\Candidate;
use App\Models\CandidateExperience;
use App\Models\Tender;
use App\Models\TenderPosition;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Psr\Log\LoggerInterface;

class CandidateMatchingApiService
{
    protected PendingRequest $http;
    protected LoggerInterface $log;

    public function __construct(string $baseUrl, string $apiKey)
    {
        $this->http = Http::baseUrl($baseUrl)
            ->withHeader('Nio-Api-Key', $apiKey)
            ->timeout(300)
            ->asJson()
            ->acceptJson();

        $this->log = Log::channel('candidate_matching_api');
    }

    /**
     * Perform a request to the matching API to get top matches based on tender and company data.
     *
     * @throws ConnectionException
     * @throws RequestException
     */
    public function requestMatch(Collection $payload): array
    {
        $uuid = Str::uuid();
        $endpoint = 'match';
        $this->log->debug("POST RQ :: ({$uuid}) - [{$endpoint}] {$payload->toJson()}");

        /** @var Response $response */
        $response = $this->http
            ->post($endpoint, $payload)
            ->throw(
                fn(Response $response, RequestException $e) => $this->log->error(
                    "POST :: ({$uuid}) - {$response->status()} = {$e->getMessage()}"
                )
            );

        $this->log->debug("POST RS :: ({$uuid}) - {$response->status()} = {$response->body()}");

        return [
            'status' => $response->status(),
            'data' => $response->collect()->recursive(),
        ];

    }

    public function createPayload(
        TenderPosition $tenderPosition,
        Candidate $candidate,
    ): Collection
    {
        return collect([
            'technologies' => [
                'requested_technologies' => $tenderPosition->technologies->pluck('uuid'),
                'mandatory_technologies' => $tenderPosition->technologies()
                    ->wherePivot('is_mandatory', true)
                    ->pluck('uuid'),
                'candidate_technologies' => $candidate->skills->each->technology->pluck('technology.uuid')->filter()->values(),
            ],
            'rates' => [
                'weight' => 0,
                'requested_hourly_rate' => [
                    'min' => $tenderPosition->price,
                    'max' => $tenderPosition->price_to,
                ],
                'candidate_hourly_rate' => [
                    'min' => $candidate->rate,
                    'max' => $candidate->rate,
                ],
            ],
            'experience' => [
                'requested_position' => [
                    'name' => $tenderPosition->name,
                    'description' => $tenderPosition->description,
                ],
                'candidate_experience' => $candidate->experiences?->map(function (CandidateExperience $experience) {
                    return [
                        'name' => $experience->name,
                        'description' => $experience->description,
                    ];
                }),
            ],
            'seniority' => [
                'requested_seniority' => $tenderPosition->seniorities->first()->seniority->value,
                'candidate_seniority' => $candidate->seniority->value,
            ],
        ]);
    }
}
