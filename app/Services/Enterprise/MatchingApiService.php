<?php
declare(strict_types=1);

namespace App\Services\Enterprise;

use App\Enums\Enterprise\TenderMatchingCompaniesFilter;
use App\Models\Tender;
use App\Models\TenderPosition;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Psr\Log\LoggerInterface;

class MatchingApiService
{
    protected PendingRequest $http;
    protected LoggerInterface $log;

    public function __construct(string $baseUrl, string $apiKey)
    {
        $this->http = Http::baseUrl($baseUrl)
            ->withHeader('Nio-Api-Key', $apiKey)
            ->timeout(300)
            ->asJson()
            ->acceptJson();

        $this->log = Log::channel('matching_api');
    }

    /**
     * Perform a request to the matching API to get top matches based on tender and company data.
     *
     * @throws ConnectionException
     * @throws RequestException
     */
    public function requestMatches(Collection $payload, TenderMatchingCompaniesFilter $companiesFilter): array
    {
        $uuid = Str::uuid();
        $endpoint = match ($companiesFilter) {
            TenderMatchingCompaniesFilter::Selection => 'matches/platform',
            TenderMatchingCompaniesFilter::Marketplace => 'matches/marketplace',
            TenderMatchingCompaniesFilter::All => throw new \Exception('Not implemented'),
        };
        $this->log->debug("POST RQ :: ({$uuid}) - [{$endpoint}] {$payload->toJson()}");

        /** @phpstan-var Response $response */
        $response = $this->http
            ->post($endpoint, $payload)
            ->throw(
                fn(Response $response, RequestException $e) => $this->log->error(
                    "POST :: ({$uuid}) - {$response->status()} = {$e->getMessage()}"
                )
            );

        $this->log->debug("POST RS :: ({$uuid}) - {$response->status()} = {$response->body()}");

        return [
            'status' => $response->status(),
            'data' => $response->collect()->recursive(),
        ];

    }

    public function createPayload(
        Tender $tender,
        TenderMatchingCompaniesFilter $companiesFilter,
        array $companyUuids,
    ): Collection
    {
        $payload = collect([
            'tender' => [
                'project_description' => $tender->matching_description ?? $tender->description,
                'location' => $tender->rfp->locations->pluck('id_string'),
                'positions' => $tender->positions
                    ->map(
                        fn(TenderPosition $position): array => [
                            'role_name' => $position->name,
                            'technologies' => $position->technologies->pluck('uuid'),
                            'mandatory_technologies' => $position->technologies()
                                ->wherePivot('is_mandatory', true)
                                ->pluck('uuid'),
                        ]
                    ),
            ]
        ]);

        match ($companiesFilter) {
            TenderMatchingCompaniesFilter::Selection => $payload->put('include_company_ids', $companyUuids),
            TenderMatchingCompaniesFilter::Marketplace => $payload->put('exclude_company_ids', $companyUuids)->put('limit', 10),
            TenderMatchingCompaniesFilter::All => throw new \Exception('Not implemented'),
        };
        return $payload;
    }
}
