<?php
declare(strict_types=1);

namespace App\Services\Enterprise;

use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Psr\Log\LoggerInterface;

class AssistantApiService
{
    protected PendingRequest $http;

    protected LoggerInterface $log;

    public function __construct(string $baseUrl, string $apiKey)
    {
        $this->http = Http::baseUrl($baseUrl)
            ->withHeader('Nio-Api-Key', $apiKey)
            ->timeout(300)
            ->asJson()
            ->acceptJson();

        $this->log = Log::channel('assistant_api');
    }

    /**
     * Fetch RFP info and scopes.
     *
     * @throws RequestException|ConnectionException
     */
    public function extractRfp(string $rfpText): Collection
    {
        $payload = collect([
            'user_text' => $rfpText,
        ]);

        $response = $this->makeRequest('rfp', $payload);

        return $response->collect()->recursive();
    }

    /**
     * Fetch resources extracted from the RFP.
     *
     * @throws RequestException|ConnectionException
     */
    public function extractResources(string $userText, string $description): array
    {
        $payload = collect([
            'user_text' => $userText,
            'detailed_description' => $description
        ]);

        $response = $this->makeRequest('resources/extract', $payload);

        return $response->collect()->toArray();
    }

    /**
     * Fetch suggested resources on top of existing resources.
     *
     * @throws RequestException|ConnectionException
     */
    public function suggestResources(string $userText, string $description, array $existingResources): array
    {
        $payload = collect([
            'user_text' => $userText,
            'detailed_description' => $description,
            'extracted_resources' => $existingResources
        ]);

        $response = $this->makeRequest('resources/suggest', $payload);

        return $response->collect()->toArray();
    }

    /**
     * Fetch processed description.
     *
     * @throws RequestException|ConnectionException
     */
    public function description(string $userText, string $description): ?string
    {
        $payload = collect([
            'user_text' => $userText,
            'detailed_description' => $description,
        ]);

        $response = $this->makeRequest('description', $payload);

        return $response->json('description');
    }

    /**
     * @throws RequestException
     * @throws ConnectionException
     */
    private function makeRequest(string $endpoint, Collection $payload): Response
    {
        $uuid = Str::uuid();
        $this->log->debug("POST RQ :: ({$uuid}) - [{$endpoint}] " . $payload->toJson());

        /** @var Response $response */
        $response = $this->http->post(
            $endpoint,
            $payload
        )->throw(
            fn(Response $response, RequestException $e) => $this->log->error(
                "POST :: ({$uuid}) - " . $response->status() . ' = ' . $e->getMessage()
            )
        );

        $this->log->debug("POST RS :: ({$uuid}) - " . $response->status() . ' = ' . $response->body());

        return $response;
    }
}
