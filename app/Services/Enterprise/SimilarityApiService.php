<?php
declare(strict_types=1);

namespace App\Services\Enterprise;

use App\Enums\Enterprise\SimilarityApiCollection;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Http;

class SimilarityApiService
{
    private PendingRequest $http;

    public function __construct(string $baseUrl)
    {
        $this->http = Http::baseUrl($baseUrl)
            ->asJson()
            ->acceptJson();
    }

    /**
     * Get alternatives based on a slug
     *
     * @throws RequestException
     * @throws ConnectionException
     */
    public function search(array $texts, SimilarityApiCollection $collectionName): array
    {
        $response = $this->http->post(
            'similarity-search',
            [
                'texts' => $texts,
                'collection_name' => $collectionName
            ]
        )->throw();

        if (!$response->ok()) {
            return [];
        }

        $allResults = $response->collect('results');

        return $allResults->isEmpty() ? [] : $this->processResponse($allResults);

    }

    private function processResponse(Collection $allResults): array
    {
        return $allResults
            ->map(static function (array $singleSearchTermResult): array {
                return collect($singleSearchTermResult['result'])
                    ->map(static function (array $item): array {
                        return [
                            'id' => $item['id'] ?? null,
                            'score' => $item['score'] ?? null,
                            'payload' => $item['payload'] ?? null,
                        ];
                    })
                    ->filter(static function (array $item): bool {
                        // Filter out items without a valid score or payload
                        return isset($item['score'], $item['payload']);
                    })
                    ->values()
                    ->toArray();
            })
            ->values()
            ->toArray();
    }
}
