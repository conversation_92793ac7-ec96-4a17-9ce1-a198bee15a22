<?php

namespace App\Services;

use App\Models\Company;
use App\Models\User;
use App\Repositories\UsersRepository;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailer;

class NotificationService
{
    private readonly string $nioStaffMail;

    public function __construct(
        private readonly Mailer $mailer,
    ) {
        $this->nioStaffMail = config('mail.addresses.nio_staff');
    }

    public function notifyCompanyUsers(int|string|Company $company, Mailable $mail): void
    {
        app(UsersRepository::class)->query()->ofCompany($company)->each(
            fn ($user) => $this->mailer->to($user->email)->send(clone $mail)
        );
    }

    public function notifyStaff(Mailable $mail): void
    {
        // Thankfully, we can send one e-mail and Google Workspace will broadcast it to the rest
        $this->mailer->to($this->nioStaffMail)->send($mail);
    }

    public function notifyUser(User|int|null $user, Mailable $mail): void
    {
        // Allow passing of null values for convenience of the caller
        if (! $user) {
            return;
        }

        $user = is_int($user) ? app(UsersRepository::class)->findById($user) : $user;

        $this->mailer->to($user->email)->send($mail);
    }
}
