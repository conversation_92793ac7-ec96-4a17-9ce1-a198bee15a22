<?php

namespace App\Services;

use App\Http\Filtering\Filterer;
use App\Http\Resources\Admin\BenchSpecialistReviewResource;
use App\Http\Resources\Admin\CompanyIndexResource;
use App\Http\Resources\Admin\SolutionIndexResource;
use App\Http\Resources\Admin\VendorReviewResource;
use App\Models\BenchSpecialist;
use App\Models\Company;
use App\Models\Solution;
use App\Models\Vendor;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ReviewingService
{
    public function __construct(
        private readonly Filterer $filterer,
    ) {}

    private array $reviewables = [
        'company' => [
            'model' => Company::class,
            'resource' => CompanyIndexResource::class,
        ],
        'vendor' => [
            'model' => Vendor::class,
            'resource' => VendorReviewResource::class,
            'scope' => 'vendorScope',
        ],
        'solution' => [
            'model' => Solution::class,
            'resource' => SolutionIndexResource::class,
        ],
        'bench_specialist' => [
            'model' => BenchSpecialist::class,
            'resource' => BenchSpecialistReviewResource::class,
            'with' => ['candidate'],
        ],
    ];

    public function selectAndPaginateAwaitingApprovals(): LengthAwarePaginator
    {
        // First, fetch raw results
        $results = $this->filterer->paginate($this->queryAwaitingApprovals());

        // Next, batch fetch models of each type
        $models = $results
            ->groupBy('type')
            ->map(function ($modelResults, $type) {
                $ids = $modelResults->map(fn ($result) => $result->merge_into_id ?? $result->id);
                $relationships = [
                    ...($this->reviewables[$type]['with'] ?? []),
                    'unapproved_change',
                ];

                return $this->reviewables[$type]['model']::whereIn('id', $ids)
                    ->with($relationships)
                    ->get()
                    ->mapWithKeys(fn ($model) => [$model->id => $model]);
            });

        // Lastly, replace results with models
        return $results->through(function ($result) use ($models) {
            $type = $result['type'];
            $config = $this->reviewables[$type];

            $model = $models->get($type)->get($result['merge_into_id'] ?? $result['id']);
            $model->__type = $type;
            $model->__config = $config;

            return $model;
        });
    }

    public function getAwaitingApprovalsCount(): int
    {
        return $this->queryAwaitingApprovals()->count();
    }

    private function queryAwaitingApprovals(): Builder
    {
        return collect($this->reviewables)
            ->map(fn ($reviewable, $type) => $reviewable['model']::select([
                'id', 'merge_into_id', 'updated_at', DB::raw("'$type' as type"),
            ])
                ->when($reviewable['scope'] ?? null, fn (Builder $query) => $this->{$reviewable['scope']}($query))
                ->withUnapprovedChanges()
                ->where(fn (Builder $query) => $query
                    ->whereNotNull('merge_into_id')
                    ->orWhere(fn ($subQuery) => $subQuery->awaitingApproval())
                )
            )
            ->reduce(fn ($query, $modelQuery) => $query ? $query->union($modelQuery) : $modelQuery)
            ->oldest('updated_at');
    }

    /**
     * Custom query builder scope for Vendor model
     *
     * @noinspection PhpUnusedPrivateMethodInspection
     */
    private function vendorScope(Builder $query): Builder
    {
        return $query->whereHas('vendor');
    }
}
