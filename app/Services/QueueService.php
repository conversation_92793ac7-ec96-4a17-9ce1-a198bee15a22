<?php

declare(strict_types=1);

namespace App\Services;

use App\Exceptions\QueueException;
use App\Queues\FailerQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use PhpAmqpLib\Exception\AMQPExceptionInterface;
use PhpAmqpLib\Exception\AMQPIOException;
use Throwable;

use function app;
use function dispatch;
use function property_exists;

class QueueService
{
    /**
     * @throws QueueException
     */
    public static function dispatch(ShouldQueue $job): void
    {
        if (! property_exists($job, 'queue') || ! property_exists($job, 'connection')) {
            throw new QueueException('ShouldQueue not queueable.');
        }

        try {
            dispatch($job);
        } catch (AMQPIOException|AMQPExceptionInterface $amqp) {
            try {
                $queue = new FailerQueue;
                app('queue.failer')->log(
                    $job->connection ?: $queue->getConnectionName(),
                    $job->queue,
                    $queue->push($job, $job),
                    $amqp
                );
            } catch (Throwable $e) {
                throw new QueueException($e->getMessage());
            }
        }
    }
}
