<?php

namespace App\Scopes;

use App\Enums\UserRole;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Scope;

class AccessibleTenderScope implements Scope
{
    public function apply(Builder $builder, Model $model): void
    {
        // Ensure that only tenders with companies that
        // still exist in database will be shown.
        $builder->whereHas('company');

        // We do not wish to block access to tenders when
        // running from CLI command. But at the same time,
        // do not block tests from working properly
        if (app()->runningInConsole() && ! app()->runningUnitTests()) {
            return;
        }

        $user = auth()->user();

        // Some day we might want to expose tender to everybody,
        // but until then, all tenders are inaccessible without
        // proper authentication.
        if (! $user) {
            $builder->whereRaw('1 = 0');

            return;
        }

        // All tenders are visible to any admin, no scope required
        if (UserRole::SuperAdmin->has($user)) {
            return;
        }

        // Only a selected vendors have access to tenders.
        // Also, companies must see own tenders.
        $builder->where(fn ($subQuery) => $subQuery
            ->where('company_id', $user->company_id)
            ->orWhereHas('allowed_vendors', fn ($vendorQuery) => $vendorQuery->where('company_id', $user->company_id))
        );

        // There is one unhandled edge case - this will still show tenders to companies that
        // were initially allowed to view tender but then were revoked as vendors. But this
        // is such an improbable case that it is not worth slowing down the query even more.
    }
}
