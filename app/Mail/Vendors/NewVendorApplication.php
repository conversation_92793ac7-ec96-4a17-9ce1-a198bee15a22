<?php

namespace App\Mail\Vendors;

use App\Models\Company;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class NewVendorApplication extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        private Company $company,
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        return $this
            ->subject(__('vendors.new_vendor_application.subject', ['company' => $this->company->name]))
            ->markdown('mail.vendors.new-vendor-application', [
                'company' => $this->company,
                'url' => $this->getViewVendorFrontendUrl(),
            ]);
    }

    private function getViewVendorFrontendUrl(): string
    {
        $baseUrl = rtrim(config('app.frontend_url'), '/');
        $pageUrl = "/admin/companies/{$this->company->public_id}/vendor";

        return $baseUrl.$pageUrl;
    }
}
