<?php

namespace App\Mail\Vendors;

use App\Models\Tender;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class VendorMessage extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct( 
        public readonly Tender $tender,
        public string $message
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        return $this
            ->subject(__('vendors.vendor_message.subject'))
            ->markdown('mail.vendors.vendor-message', [
                'message' => $this->message,
                'url'     => $this->getViewVendorFrontendUrl(),
            ]);
    }

    private function getViewVendorFrontendUrl(): string
    {
        $baseUrl = rtrim(config('app.frontend_url'), '/');
        $pageUrl = "/tenders/{$this->tender->slug}";

        return $baseUrl . $pageUrl;
    }
}
