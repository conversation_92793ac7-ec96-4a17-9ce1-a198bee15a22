<?php

namespace App\Mail\Tenders;

use App\Models\Tender;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CandidateMessage extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct( 
        public readonly Tender $tender,
        public string $message
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        return $this
            ->subject(__('candidates.candidate_message.subject'))
            ->markdown('mail.tenders.candidate-message', [
                'message' => $this->message,
                'url'     => $this->getViewVendorFrontendUrl(),
            ]);
    }

    private function getViewVendorFrontendUrl(): string
    {
        $baseUrl = rtrim(config('app.frontend_url'), '/');
        $pageUrl = "/tenders/{$this->tender->slug}";

        return $baseUrl . $pageUrl;
    }
}
