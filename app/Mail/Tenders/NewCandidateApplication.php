<?php

namespace App\Mail\Tenders;

use App\Models\Candidate;
use App\Models\Company;
use App\Models\Tender;
use App\Models\TenderPosition;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class NewCandidateApplication extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        private Tender $tender,
        private TenderPosition $position,
        private Company $company,
        private Candidate $candidate,
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        return $this
            ->subject(__('tenders.new_candidate_application.subject', ['tender' => $this->tender->name]))
            ->markdown('mail.tenders.new-candidate-application', [
                'tender' => $this->tender,
                'position' => $this->position,
                'candidate' => $this->candidate,
                'company' => $this->company,
                'url' => $this->getViewTenderCandidatesFrontendUrl(),
            ]);
    }

    private function getViewTenderCandidatesFrontendUrl(): string
    {
        $baseUrl = rtrim(config('app.frontend_url'), '/');
        $pageUrl = "/admin/candidates/{$this->candidate->slug}?fromTender={$this->tender->slug}";

        return $baseUrl.$pageUrl;
    }
}
