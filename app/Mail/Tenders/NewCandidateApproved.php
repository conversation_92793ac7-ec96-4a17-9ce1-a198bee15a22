<?php

namespace App\Mail\Tenders;

use App\Models\Candidate;
use App\Models\Tender;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class NewCandidateApproved extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        private Tender         $tender,
        private Candidate      $candidate,
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        return $this
            ->subject(__('tenders.new_candidate_approved.subject', ['tender' => $this->tender->name]))
            ->markdown('mail.tenders.new-candidate-approved', [
                'tender'    => $this->tender,
                'candidate' => $this->candidate,
                'company'   => $this->tender->company,
                'position'  => $this->candidate->positions->first(),
                'url'       => $this->getViewTenderCandidatesFrontendUrl(),
            ]);
    }

    private function getViewTenderCandidatesFrontendUrl(): string
    {
        $baseUrl = rtrim(config('app.frontend_url'), '/');
        $pageUrl = "/tenders/{$this->tender->slug}";

        return $baseUrl . $pageUrl;
    }
}
