<?php

namespace App\Mail\Tenders;

use App\Models\Candidate;
use App\Models\Tender;
use App\Models\TenderPosition;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CandidateStatusUpdated extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        private readonly Tender $tender,
        private readonly TenderPosition $position,
        private readonly Candidate $candidate,
        private readonly array $pivot,
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        return $this
            ->subject(__('tenders.candidate_status_updated.subject', [
                'candidate' => $this->candidate->name,
                'tender' => $this->tender->name,
            ]))
            ->markdown('mail.tenders.candidate-status-updated', [
                'tender' => $this->tender,
                'position' => $this->position,
                'candidate' => $this->candidate,
                'pivot' => $this->pivot,
                'url' => $this->getViewTenderCandidatesFrontendUrl(),
            ]);
    }

    private function getViewTenderCandidatesFrontendUrl(): string
    {
        $baseUrl = rtrim(config('app.frontend_url'), '/');
        $pageUrl = "/tenders/{$this->tender->slug}/candidates";

        return $baseUrl.$pageUrl;
    }
}
