<?php

namespace App\Mail\Tenders;

use App\Models\Tender;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class NewTenderAvailable extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        private readonly Tender $tender,
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        return $this
            ->subject(__('tenders.new_tender_available.subject'))
            ->markdown('mail.tenders.new-tender-available', [
                'tender' => $this->tender,
                'deadline' => $this->tender->submissions_deadline->format(__('global.dates.full')),
                'url' => $this->getViewTenderFrontendUrl(),
            ]);
    }

    private function getViewTenderFrontendUrl(): string
    {
        $baseUrl = rtrim(config('app.frontend_url'), '/');
        $pageUrl = "/tenders/{$this->tender->slug}";

        return $baseUrl.$pageUrl;
    }
}
