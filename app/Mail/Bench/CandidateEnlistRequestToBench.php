<?php

namespace App\Mail\Bench;

use App\Models\BenchSpecialist;
use App\Models\Company;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class CandidateEnlistRequestToBench extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        private readonly Company $company,
        private readonly BenchSpecialist $benchSpecialist,
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        return $this
            ->subject(__('bench.candidate_enlist_request.subject', [
                'company' => $this->company->name,
                'candidate' => $this->benchSpecialist->candidate->internal_name,
            ]))
            ->markdown('mail.bench.candidate-enlist-request', [
                'company' => $this->company,
                'benchSpecialist' => $this->benchSpecialist,
                'benchSpecialistUrl' => $this->getViewBenchSpecialistFrontendUrl(),
            ]);
    }

    private function getViewBenchSpecialistFrontendUrl(): string
    {
        $baseUrl = rtrim(config('app.frontend_url'), '/');
        $pageUrl = "/admin/bench-specialists/{$this->benchSpecialist->public_id}";

        return $baseUrl.$pageUrl;
    }
}
