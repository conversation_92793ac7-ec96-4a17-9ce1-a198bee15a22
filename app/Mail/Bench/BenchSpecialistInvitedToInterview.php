<?php

namespace App\Mail\Bench;

use App\Models\BenchSpecialist;
use App\Models\Company;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class BenchSpecialistInvitedToInterview extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        private readonly Company $company,
        private readonly BenchSpecialist $benchSpecialist,
        private readonly string $message,
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        return $this
            ->subject(__('bench.specialist_invited_to_interview.subject', [
                'company' => $this->company->name,
                'benchSpecialist' => $this->benchSpecialist->candidate->internal_name,
            ]))
            ->markdown('mail.bench.specialist-invited-to-interview', [
                'company' => $this->company,
                'benchSpecialist' => $this->benchSpecialist,
                'message' => $this->message,
                'companyUrl' => $this->getViewCompanyFrontendUrl(),
                'benchSpecialistUrl' => $this->getViewBenchSpecialistFrontendUrl(),
            ]);
    }

    private function getViewCompanyFrontendUrl(): string
    {
        $baseUrl = rtrim(config('app.frontend_url'), '/');
        $pageUrl = "/admin/companies/{$this->company->public_id}";

        return $baseUrl.$pageUrl;
    }

    private function getViewBenchSpecialistFrontendUrl(): string
    {
        $baseUrl = rtrim(config('app.frontend_url'), '/');
        $pageUrl = "/admin/bench-specialists/{$this->benchSpecialist->public_id}";

        return $baseUrl.$pageUrl;
    }
}
