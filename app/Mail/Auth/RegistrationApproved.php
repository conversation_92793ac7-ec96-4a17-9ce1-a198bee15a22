<?php

namespace App\Mail\Auth;

use App\Models\Company;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class RegistrationApproved extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        private Company $company,
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        return $this
            ->subject(__('auth.registration_approved.subject'))
            ->markdown('mail.auth.registration-approved', [
                'company' => $this->company,
                'url' => $this->getLogInFrontendUrl(),
            ]);
    }

    private function getLogInFrontendUrl(): string
    {
        return rtrim(config('app.frontend_url'), '/');
    }
}
