<?php

namespace App\Mail\Auth;

use App\Models\Company;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class NewCompanyRegistration extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        private Company $company,
        private User $user,
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        return $this
            ->subject(__('auth.new_company_registration.subject', ['company' => $this->company->name]))
            ->markdown('mail.auth.new-company-registration', [
                'company' => $this->company,
                'user' => $this->user,
                'url' => $this->getViewCompanyFrontendUrl(),
            ]);
    }

    private function getViewCompanyFrontendUrl(): string
    {
        $baseUrl = rtrim(config('app.frontend_url'), '/');
        $pageUrl = "/admin/companies/{$this->company->public_id}";

        return $baseUrl.$pageUrl;
    }
}
