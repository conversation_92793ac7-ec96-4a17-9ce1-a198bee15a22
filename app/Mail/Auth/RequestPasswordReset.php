<?php

namespace App\Mail\Auth;

use App\Models\PasswordReset;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class RequestPasswordReset extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        private readonly PasswordReset $passwordReset,
    )
    {
        //
    }

    public function build(): self
    {
        return $this
            ->subject(
                __('auth.request_password_reset.subject')
            )
            ->markdown('mail.auth.request-password-reset', [
                'url' => url()->route(
                    'reset-password.show',
                    [
                        'token' => $this->passwordReset->token,
                    ]
                ),
                'date' => $this->passwordReset->expires_at->format(__('global.dates.time')),
            ]);
    }
}
