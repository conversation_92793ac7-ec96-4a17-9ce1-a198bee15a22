<?php

namespace App\Mail\Solutions;

use App\Models\Company;
use App\Models\Solution;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class SolutionContactRequested extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     */
    public function __construct(
        private readonly Company $company,
        private readonly Solution $solution,
        private readonly string $message,
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        return $this
            ->subject(__('solutions.solution_contact_requested.subject', [
                'company' => $this->company->name,
                'solution' => $this->solution->name,
            ]))
            ->markdown('mail.solutions.solution-contact-requested', [
                'company' => $this->company,
                'solution' => $this->solution,
                'message' => $this->message,
                'companyUrl' => $this->getViewCompanyFrontendUrl(),
                'solutionUrl' => $this->getViewSolutionFrontendUrl(),
            ]);
    }

    private function getViewCompanyFrontendUrl(): string
    {
        $baseUrl = rtrim(config('app.frontend_url'), '/');
        $pageUrl = "/admin/companies/{$this->company->public_id}";

        return $baseUrl.$pageUrl;
    }

    private function getViewSolutionFrontendUrl(): string
    {
        $baseUrl = rtrim(config('app.frontend_url'), '/');
        $pageUrl = "/admin/solutions/{$this->solution->public_id}";

        return $baseUrl.$pageUrl;
    }
}
