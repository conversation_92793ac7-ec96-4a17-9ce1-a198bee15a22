<?php

namespace App\Observers;

use App\Exceptions\QueueException;
use App\Jobs\SendCompany;
use App\Models\Company;
use App\Services\QueueService;

class CompanyObserver
{
    /**
     * @throws QueueException
     */
    public function created(Company $company): void
    {
        static::toQueue($company);
    }

    /**
     * @throws QueueException
     */
    public function updated(Company $company): void
    {
        static::toQueue($company);
    }

    /**
     * @throws QueueException
     */
    private static function toQueue(Company $company): void
    {
        $job = new SendCompany(
            $company->id,
            $company->name,
            $company->hq,
            $company->country->value,
            $company->website,
            $company->linkedin,
            $company->about,
            $company->notes,
            $company->presentation_url,
        );

        QueueService::dispatch($job);
    }
}
