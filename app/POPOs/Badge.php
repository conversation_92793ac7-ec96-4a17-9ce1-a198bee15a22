<?php

namespace App\POPOs;

use App\Enums\Country;
use JsonSerializable;

class Badge implements JsonSerializable
{
    public function __construct(
        public string $type,
        public string $label,
        public ?array $extra = null,
    ) {}

    public static function text(string $text): self
    {
        return new self('text', $text);
    }

    public static function primaryText(string $text): self
    {
        return new self('text', $text, [
            'type' => 'primary',
        ]);
    }

    public static function warningText(string $text): self
    {
        return new self('text', $text, [
            'type' => 'warning',
        ]);
    }

    public static function country(Country $country, ?string $label = null): self
    {
        return new self('country', $label ?? $country->trans(), [
            'country' => $country->value,
        ]);
    }

    public function jsonSerialize()
    {
        if ($this->extra) {
            return [
                'type' => $this->type,
                'label' => $this->label,
                'extra' => $this->extra,
            ];
        }

        return [
            'type' => $this->type,
            'label' => $this->label,
        ];
    }
}
