<?php

namespace App\Repositories;

use App\Enums\TenderStatus;
use App\Models\Company;
use App\Models\Tender;
use App\Models\TenderProject;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Libs\Warehouse\Warehouse;

class TendersRepository
{
    public function __construct(
        private CompaniesRepository $companiesRepository,
        private Warehouse $warehouse,
    ) {}

    /**
     * @return EloquentCollection<Tender>
     */
    public function all(): EloquentCollection
    {
        return $this->query()->get();
    }

    public function findBySlugOrFail(string $slug): Tender
    {
        return $this->query()
            ->with([
                'cover_resource', 'company',
                'project.main_industry', 'project.industries', 'project.technologies', 'project.client',
                'positions.employee_position', 'positions.seniorities', 'positions.technologies',
            ])
            ->findBySlugOrFail($slug);
    }

    public function published(): Builder
    {
        return $this->query()->published();
    }

    public function own(): Builder
    {
        return $this->query()->where('company_id', auth()->user()?->company_id);
    }

    public function publishedOrOwn(): Builder
    {
        return $this->query()->where(fn (Builder $query) => $query
            ->published()
            ->orWhere('company_id', auth()->user()?->company_id)
        );
    }

    public function query(): Builder
    {
        return Tender::query()->with('project');
    }

    /**
     * Calculates and retrieves tenders stats for a given company
     *
     * @return Collection<int>
     */
    public function companyStats(string|int $slug): Collection
    {
        $defaults = collect(TenderStatus::valueArray())
            ->mapWithKeys(fn (string $state) => [$state => 0]);

        $values = $this->published()
            ->ofCompany($slug)
            ->select('status', DB::raw('count(*) as total'))
            ->groupBy('status')
            ->pluck('total', 'status');

        return $defaults->merge($values);
    }

    public function store(array $data): Tender
    {
        return in_transaction(function () use ($data) {
            /** @var Company $company */
            $company = $this->companiesRepository->query()->findBySlugOrFail($data['company_id']);
            $data = $this->privatizeResourcesId($data);

            // Set default submission deadline if not provided
            if (!isset($data['submissions_deadline'])) {
                $data['submissions_deadline'] = now()->addDays(7);
            }

            /** @var Tender $tender */
            $tender = $company->tenders()->create($data);

            $projectData = $this->projectData($data);

            /** @var TenderProject $project */
            $project = $tender->project()->create($projectData);
            $project->industries()->attach($projectData['industries'] ?? []);
            $project->technologies()->attach($projectData['technologies'] ?? []);

            return $tender;
        });
    }

    public function update(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            /** @var Tender $tender */
            $tender = $this->query()->findBySlugOrFail($slug);
            $data = $this->privatizeResourcesId($data);

            $tender->fill($data);
            $tender->save();

            $projectData = $this->projectData($data);

            $project = $tender->project;
            $project->fill($projectData);
            $project->save();
            $project->industries()->sync($projectData['industries'] ?? []);
            $project->technologies()->sync($projectData['technologies'] ?? []);
        });
    }

    public function destroy(string $slug): void
    {
        /** @var Tender $tender */
        $tender = $this->query()->findBySlugOrFail($slug);
        $tender->delete();
    }

    private function projectData($data): array
    {
        $projectData = $data['project'];

        if ($projectData['client_id']) {
            $projectData['client_name'] = null;
        }

        return $projectData;
    }

    private function privatizeResourcesId(array $data): array
    {
        if ($data['cover_resource_id'] ?? false) {
            $coverResource = $this->warehouse->find($data['cover_resource_id']);
            $data['cover_resource_id'] = $coverResource->id;
        }

        return $data;
    }
}
