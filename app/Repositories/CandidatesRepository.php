<?php

namespace App\Repositories;

use App\Enums\TenderCandidateStatus;
use App\Mail\Tenders\CandidateMessage;
use App\Enums\CandidateParseStatus;
use App\Exceptions\Handler as ExceptionHandler;
use App\Models\Candidate;
use App\Models\CandidateAssignment;
use App\Models\CandidateExperience;
use App\Models\CandidateSkill;
use App\Models\Company;
use App\Models\TenderPosition;
use App\Models\Vendor;
use App\Models\User;
use App\Models\Tender;
use App\POPOs\CandidateParseResult;
use App\Services\NotificationService;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Validation\Factory as ValidationFactory;
use Illuminate\Validation\Validator;
use Libs\Vision\Parser\Exceptions\ResumeParsingException;
use Libs\Vision\Parser\ResumeParser;
use Libs\Warehouse\Warehouse;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

readonly class CandidatesRepository
{
    private const MAIN_RELATIONSHIPS = ['skills.experiences', 'experiences', 'cv_resource'];

    public function __construct(
        private ValidationFactory   $validationFactory,
        private Warehouse           $warehouse,
        private ResumeParser        $resumeParser,
        private VendorsRepository   $vendorsRepository,
        private ExceptionHandler    $exceptionHandler,
        public NotificationService $notificationService,
    ) {}

    /**
     * @return Collection<Candidate>
     */
    public function all(): Collection
    {
        return $this->query()->get();
    }

    public function query(): Builder
    {
        return Candidate::query()->whereHas('company');
    }

    public function queryBuilder(): QueryBuilder
    {
        return QueryBuilder::for($this->query());
    }

    public function finished(): Builder
    {
        return $this->query()->where('finished', true);
    }

    public function original(): Builder
    {
        return $this->query()->where('clone', false);
    }

    public function ownOriginal(): Builder
    {
        return $this->original()->where('vendor_id', auth()->user()->vendor?->id);
    }

    public function finishedOriginal(): Builder
    {
        return $this->finished()->where('clone', false);
    }

    public function ownFinishedOriginal(): Builder
    {
        return $this->finishedOriginal()->where('vendor_id', auth()->user()->vendor?->id);
    }

    public function drafted(): Builder
    {
        return $this->query()->where('finished', false);
    }

    public function draftedCloned(): Builder
    {
        return $this->drafted()->where('clone', true);
    }

    public function cloned(): Builder
    {
        return $this->query()->where('clone', true);
    }

    public function clonedOfTender(string $tenderSlug): Builder
    {
        return $this->cloned()->whereHas('positions', fn (Builder $positionsQuery) => $positionsQuery->whereHas('tender', fn (Builder $tenderQuery) => $tenderQuery->slug($tenderSlug))
        );
    }

    public function clonedFinishedOfTender(string $tenderSlug): Builder
    {
        return $this->clonedOfTender($tenderSlug)->where('finished', true);
    }

    public function clonedOfBenchSpecialist(string $specialistSlug): Builder
    {
        return $this->cloned()->whereHas('benchSpecialist', fn (Builder $specialistQuery) => $specialistQuery->slug($specialistSlug));
    }

    public function clonedFinishedOfBenchSpecialist(string $specialistSlug): Builder
    {
        return $this->clonedOfBenchSpecialist($specialistSlug)->where('finished', true);
    }

    public function ownClonedFinishedOfTender(string $tenderSlug): Builder
    {
        return $this
            ->clonedFinishedOfTender($tenderSlug)
            ->whereRelation('positions.tender', 'company_id', auth()->user()->company_id);
    }

    public function activeForTender(Tender $tender): Builder
    {
        return $this->query()
            ->whereHas('positions', function (Builder $query) use ($tender) {
                $query->where('tender_positions.tender_id', $tender->id)
                    ->whereNotIn('status', [TenderCandidateStatus::AwaitingApproval, TenderCandidateStatus::Rejected]);
            });
    }

    public function activeBench(): QueryBuilder
    {
        return $this->queryBuilder()
            ->with([
                'company',
                'activeBenchSpecialist',
                'skills.technology',
            ])
            ->allowedFilters([
                AllowedFilter::partial('search', 'name'),
                AllowedFilter::partial('profession'),
                AllowedFilter::exact('seniority'),
                AllowedFilter::exact('company', 'company.id'),
                AllowedFilter::exact('technologies', 'skills.technology_id'),
                AllowedFilter::scope('min_rate'),
                AllowedFilter::scope('max_rate'),
                AllowedFilter::exact('country'),
                AllowedFilter::scope('available_from'),
                AllowedFilter::scope('available_to'),
            ])
            ->whereHas('activeBenchSpecialist');
    }

    public function assignedToCompany(int $companyId): QueryBuilder
    {
        return $this->queryBuilder()
            ->with([
                'company',
                'activeAssignment.company',
                'activeAssignment.tender',
                'activeAssignment.tenderPosition',
                'activeAssignment.manager',
                'skills.technology',
            ])
            ->allowedFilters([
                AllowedFilter::partial('search', 'name'),
                AllowedFilter::partial('profession'),
                AllowedFilter::exact('seniority'),
                AllowedFilter::exact('company', 'company.id'),
                AllowedFilter::exact('technologies', 'skills.technology_id'),
                AllowedFilter::scope('min_rate'),
                AllowedFilter::scope('max_rate'),
                AllowedFilter::exact('country'),
                AllowedFilter::exact('tender', 'activeAssignment.tender_id'),
                AllowedFilter::exact('manager', 'activeAssignment.manager_id'),
                AllowedFilter::scope('engaged_from'),
                AllowedFilter::scope('engaged_to'),
            ])
            ->whereHas('activeAssignment', function (Builder $query) use ($companyId): void {
                $query->where('company_id', $companyId);
            });
    }

    public function findBySlugOrFail(string $slug): Candidate
    {
        return $this->query()
            ->with('vendor.company', ...self::MAIN_RELATIONSHIPS)
            ->findBySlugOrFail($slug);
    }

    public function findOriginalBySlugOrFail(string $slug): Candidate
    {
        return $this->original()
            ->with('vendor.company', ...self::MAIN_RELATIONSHIPS)
            ->findBySlugOrFail($slug);
    }

    public function findOwnOriginalBySlugOrFail(string $slug): Candidate
    {
        return $this->ownOriginal()
            ->with(...self::MAIN_RELATIONSHIPS)
            ->findBySlugOrFail($slug);
    }

    public function findFinishedOriginalBySlugOrFail(string $slug): Candidate
    {
        return $this->finishedOriginal()
            ->with('vendor.company', ...self::MAIN_RELATIONSHIPS)
            ->findBySlugOrFail($slug);
    }

    public function findOwnFinishedOriginalBySlugOrFail(string $slug): Candidate
    {
        return $this->ownFinishedOriginal()
            ->with(...self::MAIN_RELATIONSHIPS)
            ->findBySlugOrFail($slug);
    }

    public function store(array $data, bool $finished = true): Candidate
    {
        return in_transaction(function () use ($data, $finished) {
            $vendor = $this->vendorsRepository->findBySlugOrFail($data['vendor_id']);

            return $this->storeOfVendor($vendor, $data, $finished);
        });
    }

    public function storeOwn(array $data, bool $finished = true): Candidate
    {
        return in_transaction(function () use ($data, $finished) {
            abort_unless($vendor = auth()->user()->vendor, 404);

            return $this->storeOfVendor($vendor, $data, $finished);
        });
    }

    private function storeOfVendor(Vendor $vendor, array $data, bool $finished = true): Candidate
    {
        return in_transaction(function () use ($vendor, $data, $finished) {
            $data = $this->privatizeResourcesId($data);

            /** @var Candidate $candidate */
            $candidate = $vendor->candidates()->make($data);
            $candidate->finished = $finished;
            $candidate->save();

            $this->syncCandidateRelationships($candidate, $data);
            $candidate->refresh();

            return $candidate;
        });
    }

    public function parse(array $data): CandidateParseResult
    {
        return in_transaction(function () use ($data) {
            $identifier = 'manual_admin-'.auth()->user()->getAuthIdentifier().'-'.str_random(10);

            return $this->parseCandidate($identifier, $data);
        });
    }

    public function parseOwn(array $data): CandidateParseResult
    {
        return in_transaction(function () use ($data) {
            abort_unless($vendor = auth()->user()->vendor, 404);
            $data = $this->privatizeResourcesId($data);

            // First, we need to determine if candidate with that CV
            // already exists for the vendor. If so, we will reuse it.
            // We are including unfinished records that were
            // previously not saved or not submitted.
            /** @var Candidate $existingCandidate */
            $existingCandidate = $this->ownOriginal()->where('cv_resource_id', $data['cv_resource_id'])->first();
            if ($existingCandidate) {
                return new CandidateParseResult(CandidateParseStatus::Already, $existingCandidate->slug);
            }

            $identifier = 'vendor-'.$vendor->public_id.'-user-'.auth()->user()->getAuthIdentifier().'-'.str_random(10);

            return $this->parseCandidate($identifier, $data + ['vendor_id' => $vendor->slug]);
        });
    }

    private function parseCandidate(string $identifier, array $data): CandidateParseResult
    {
        return in_transaction(function () use ($identifier, $data) {
            $cv = $this->warehouse->find($data['cv_resource_id']);

            try {
                $parsedData = $this->resumeParser->parseResume($cv, [
                    'identifier' => $identifier,
                    'fallbackName' => 'Anonymous',
                ]);
            } catch (Exception $e) {
                // An error occurred. If this error is ResumeParsingException, and it is a server
                // error, we will store the CV in an empty candidate profile and return it for
                // manual filling to allow functionality to work in degraded state
                if ($e instanceof ResumeParsingException && $e->isServerError()) {
                    // First of all, we need to report this exception so that we are aware of it
                    $this->exceptionHandler->report($e);

                    $candidateData = $data + ['internal_name' => 'Anonymous', 'name' => 'Anonymous'];
                    /** @var Candidate $candidate */
                    $candidate = tap($this->store($candidateData, finished: false))->save();

                    return new CandidateParseResult(CandidateParseStatus::Failed, $candidate->slug);
                }

                // Otherwise, we need to rethrow the exception to let it be handled properly
                throw $e;
            }

            // Document has been parsed successfully, however, we now need to verify
            // if it is of any use at all - if all required fields were filled in.
            if (! $this->areNecessaryParsedDataFieldsValid($parsedData->data)) {
                return new CandidateParseResult(CandidateParseStatus::Empty, null);
            }

            $candidateData = $parsedData->data + $data;
            /** @var Candidate $candidate */
            $candidate = tap($this
                ->store($candidateData, finished: false)
                ->forceFill(['cv_parsed_raw_data' => $parsedData]))
                ->save();

            $validator = $this->makeFullParsedDataValidator($parsedData->data);

            return new CandidateParseResult(
                $validator->passes() ? CandidateParseStatus::Full : CandidateParseStatus::Partial,
                $candidate->slug,
            );
        });
    }

    public function update(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            $candidate = $this->findBySlugOrFail($slug);
            $this->updateCandidate($candidate, $data);
        });
    }

    public function updateOfTender(string $tenderSlug, string $slug, array $data): void
    {
        in_transaction(function () use ($tenderSlug, $slug, $data) {
            /** @var Candidate $candidate */
            $candidate = $this->clonedOfTender($tenderSlug)->findBySlugOrFail($slug);
            $this->updateCandidate($candidate, $data);
        });
    }

    public function updateOfBenchSpecialist(string $tenderSlug, string $slug, array $data): void
    {
        in_transaction(function () use ($tenderSlug, $slug, $data) {
            /** @var Candidate $candidate */
            $candidate = $this->clonedOfBenchSpecialist($tenderSlug)->findBySlugOrFail($slug);
            $this->updateCandidate($candidate, $data);
        });
    }

    public function updateOwn(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            /** @var Candidate $candidate */
            $candidate = $this->ownOriginal()->findBySlugOrFail($slug);
            $this->updateCandidate($candidate, $data);
        });
    }

    private function updateCandidate(Candidate $candidate, array $data): void
    {
        in_transaction(function () use ($candidate, $data) {
            $data = $this->privatizeResourcesId($data);

            $candidate->fill($data);
            // Here, the assumption is that unfinished candidates are only
            // produces upon creation from parsed data from CV, and will
            // only be allowed to be updated with the correct data model
            $candidate->finished = true;
            $candidate->save();

            $this->syncCandidateRelationships($candidate, $data);
            $candidate->refresh();
        });
    }

    public function updateOrCreateOwn(string|int|null $slug, array $data): Candidate
    {
        return in_transaction(function () use ($slug, $data) {
            /** @var Candidate $candidate */
            if ($slug && $candidate = $this->ownOriginal()->findBySlug($slug)) {
                $this->updateCandidate($candidate, $data);

                return $candidate;
            }

            return $this->storeOwn($data);
        });
    }

    public function clone(Candidate $candidate): Candidate
    {
        // Prepare current relationships for cloning
        $experiences = $candidate->experiences->mapWithKeys(fn (CandidateExperience $experience, int $index) => [
            $experience->id => [
                'id' => null,
                'pseudo_id' => $index,
            ] + $experience->getAttributes(),
        ]);
        $skills = $candidate->skills->map(fn (CandidateSkill $skill) => $skill->getAttributes() + [
            'new_experiences' => $skill->experiences->map(fn (CandidateExperience $experience) => $experiences[$experience->id]['pseudo_id'])->toArray(),
        ]);
        $relationshipsData = [
            'experiences' => $experiences->values()->toArray(),
            'skills' => $skills->toArray(),
        ];

        // Unsetting relations so that they won't
        // be replicated automatically
        $candidate->unsetRelations();
        $clone = $candidate->replicate();

        // Associating clone with the original candidate
        $clone->origin_id = $candidate->id;
        $clone->clone = true;
        $clone->save();

        // Sync relationships to clone
        $this->syncCandidateRelationships($clone, $relationshipsData);
        $clone->refresh();

        return $clone;
    }

    public function assignOwn(Candidate $candidate, array $data): CandidateAssignment
    {
        return in_transaction(function () use ($candidate, $data) {
            $clone = $this->clone($candidate);
            // TODO(Marian Rusnak): Check access (company, tender, tender position).
            return $clone->assignment()->create([
                'company_id' => isset($data['company_id'])
                    ? Company::query()->select('id')->findBySlugOrFail($data['company_id'])->id
                    : null,
                'tender_id' => isset($data['tender_id'])
                    ? Tender::query()->select('id')->findBySlugOrFail($data['tender_id'])->id
                    : null,
                'tender_position_id' => isset($data['tender_position_id'])
                    ? TenderPosition::query()->select('id')->findBySlugOrFail($data['tender_position_id'])->id
                    : null,
                'start_date' => $data['start_date'] ?? null,
                'end_date' => $data['end_date'] ?? null,
                'status' => TenderCandidateStatus::Hired,
            ]);
        });
    }

    public function unassignOwn(Candidate $candidate): void
    {
        $candidate->activeAssignment()->delete();
    }

    public function destroy(string $slug): void
    {
        /** @var Candidate $candidate */
        $candidate = $this->query()->findBySlugOrFail($slug);
        $candidate->delete();
    }

    public function destroyOwn(string $slug): void
    {
        /** @var Candidate $candidate */
        $candidate = $this->ownOriginal()->findBySlugOrFail($slug);
        $candidate->delete();
    }

    public function destroyOfTender(string $tenderSlug, string $slug): void
    {
        $this->clonedOfTender($tenderSlug)
            ->findBySlugOrFail($slug)
            ->delete();
    }

    /**
     * @param Collection<User>|array<User> $users
     */
    public function notifyUsers(Collection|array $users, string $tenderSlug, string $message): void
    {
        in_transaction(function () use ($users, $tenderSlug, $message) {
            $tender = Tender::findBySlugOrFail($tenderSlug);
            foreach ($users as $user) {
                $this->notificationService->notifyUser($user, new CandidateMessage($tender, $message));
            }
        });
    }

    private function privatizeResourcesId(array $data): array
    {
        if ($data['cv_resource_id'] ?? false) {
            $cvResource = $this->warehouse->find($data['cv_resource_id']);
            $data['cv_resource_id'] = $cvResource->id;
        }

        return $data;
    }

    private function syncCandidateRelationships(Candidate $candidate, array $data): void
    {
        $pseudoToRealId = $this->syncCandidateExperiencesRelationship($candidate, $data['experiences'] ?? []);
        $existingExperiencesByPublicId = $candidate->experiences->keyBy('public_id');

        $candidate->skills()->delete();
        collect($data['skills'] ?? [])->each(function (array $skillData) use ($candidate, $pseudoToRealId, $existingExperiencesByPublicId) {
            /** @var CandidateSkill $skill */
            $skill = $candidate->skills()->create($skillData);

            $newExperiencesIds = collect($skillData['new_experiences'] ?? [])->map(fn (int $pseudoId) => $pseudoToRealId[$pseudoId])->toArray();
            $existingIds = collect($skillData['experiences'] ?? [])->map(fn (string $publicId) => $existingExperiencesByPublicId[$publicId]->id)->toArray();
            $experienceIds = array_merge($existingIds, $newExperiencesIds);
            $skill->experiences()->sync($experienceIds);
        });
    }

    public function syncCandidateExperiencesRelationship(Candidate $candidate, array $data): array
    {
        $data = collect($data)->map(fn (array $experience, int $index) => $experience + ['order' => $index]);
        $pseudoToRealIds = [];

        $keepIds = $data->whereNotNull('id')->pluck('id');
        $existingByPublicId = $candidate->experiences->keyBy('public_id');

        // Delete experiences that were removed from the list
        $existingByPublicId->whereNotIn('public_id', $keepIds)->each(fn (CandidateExperience $experience) => $experience->delete());

        // Sync the rest - update existing and create new
        $data->each(function (array $experienceData) use ($candidate, $existingByPublicId, &$pseudoToRealIds) {
            if ($experienceData['id'] ?? false) {
                $existingByPublicId
                    ->get($experienceData['id'])
                    ->fill($experienceData)
                    ->save();
            } else {
                /** @var CandidateExperience $experience */
                $experience = $candidate->experiences()->create($experienceData);
                $pseudoToRealIds[$experienceData['pseudo_id']] = $experience->id;
            }
        });

        return $pseudoToRealIds;
    }

    private function areNecessaryParsedDataFieldsValid(array $data): bool
    {
        return $this->validationFactory->make($data, [
            'internal_name' => 'required',
            'name' => 'required',
        ])->passes();
    }

    private function makeFullParsedDataValidator(array $data): Validator
    {
        return $this->validationFactory->make($data, [
            'internal_name' => 'required',
            'name' => 'required',
            'country' => 'required',
            'city' => 'nullable',

            'profession' => 'required',
            // 'seniority'                    => 'required',
            'last_job_title' => 'required',
            'years_of_experience' => 'required',
            'highest_education' => 'nullable',
            'field_of_study' => 'nullable',

            'skills.*.technology_id' => 'required',
            'skills.*.years_of_experience' => 'nullable',
        ]);
    }
}
