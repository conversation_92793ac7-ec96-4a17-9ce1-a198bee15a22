<?php

namespace App\Repositories;

use App\Models\Industry;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class IndustriesRepository
{
    /**
     * @return Collection<Industry>
     */
    public function all(): Collection
    {
        return $this->query()->get();
    }

    public function query(): Builder
    {
        return Industry::query();
    }

    public function findBySlugOrFail(string $slug): Industry
    {
        return $this->query()
            ->with('parent')
            ->findBySlugOrFail($slug);
    }

    public function store(array $data): Industry
    {
        return Industry::create($data);
    }

    public function update(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            /** @var Industry $industry */
            $industry = $this->query()->findBySlugOrFail($slug);
            $industry->fill($data);
            $industry->save();
        });
    }

    public function destroy(string $slug): void
    {
        /** @var Industry $industry */
        $industry = $this->query()->findBySlugOrFail($slug);
        $industry->delete();
    }
}
