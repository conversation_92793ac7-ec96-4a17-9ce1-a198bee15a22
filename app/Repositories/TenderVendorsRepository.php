<?php

namespace App\Repositories;

use App\Mail\Vendors\VendorMessage;
use App\Mail\Tenders\NewTenderAvailable;
use App\Models\Vendor;
use App\Models\User;
use App\Models\Tender;
use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

readonly class TenderVendorsRepository
{
    public function __construct(
        private TendersRepository $tendersRepository,
        private VendorsRepository $vendorsRepository,
        private NotificationService $notificationService,
    ) {}

    public function vendorsQuery(string $slug): Builder
    {
        $tender = $this->tendersRepository->findBySlugOrFail($slug);

        return $this->vendorsRepository
            ->published()
            ->where('company_id', '!=', $tender->company_id)
            ->with('company')
            ->with('tenders', fn ($subQuery) => $subQuery->wherePivot('tender_id', $tender->id));
    }

    public function updateAllowed(string $tenderSlug, string $vendorSlug, bool $allowed): void
    {
        in_transaction(function () use ($tenderSlug, $vendorSlug, $allowed) {
            $vendor = $this->vendorsRepository->findBySlugOrFail($vendorSlug);

            $this->bulkUpdateAllowed($tenderSlug, [$vendor->id], $allowed);
        });
    }

    public function bulkUpdateAllowed(string $tenderSlug, Collection|array $vendorIds, bool $allowed): void
    {
        in_transaction(function () use ($tenderSlug, $vendorIds, $allowed) {
            $tender = $this->tendersRepository->findBySlugOrFail($tenderSlug);

            $vendorData = collect($vendorIds)->mapWithKeys(fn ($vendorId) => [
                $vendorId => [
                    'allowed' => $allowed,
                ],
            ]);

            $tender->vendors()->syncWithoutDetaching($vendorData);

            // TODO: This method is simple but will notify multiple times
            //  if vendor in bulk was already allowed. Improve this.
            if ($allowed) {
                $this->vendorsRepository
                    ->published()
                    ->with('company')
                    ->whereIn('id', $vendorIds)
                    ->get()
                    ->each(fn (Vendor $vendor) => $this->notificationService->notifyCompanyUsers(
                        $vendor->company,
                        new NewTenderAvailable($tender),
                    ));
            }
        });
    }

    /**
     * @param Collection<User>|array<User> $users
     */
    public function notifyUsers(Collection|array $users, string $tenderSlug, string $message): void
    {
        in_transaction(function () use ($users, $tenderSlug, $message) {
            $tender = Tender::findBySlugOrFail($tenderSlug);
            foreach ($users as $user) {
                $this->notificationService->notifyUser($user, new VendorMessage($tender, $message));
            }
        });
    }
}
