<?php

namespace App\Repositories;

use App\Enums\UserRole;
use App\Mail\Solutions\SolutionContactRequested;
use App\Models\Solution;
use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;
use Libs\Overseer\ChangesApproved;
use Libs\Overseer\ChangesRejected;
use Libs\Overseer\NoPendingChangesException;
use Libs\Overseer\RequestedChange;
use Libs\Overseer\ReviewStatus;
use Libs\Warehouse\Warehouse;

readonly class SolutionsRepository
{
    public function __construct(
        private Warehouse $warehouse,
        private VendorsRepository $vendorsRepository,
        private NotificationService $notificationService,
    ) {}

    /**
     * @return Collection<Solution>
     */
    public function all(): Collection
    {
        return $this->query()->get();
    }

    public function findBySlugOrFail(string $slug): Solution
    {
        return $this->query()
            ->with(
                'unapproved_change',
                'cover_resource', 'main_industry', 'industries', 'technologies', 'client',
                'media.resource', 'vendor.company',
            )
            ->findBySlugOrFail($slug);
    }

    public function published(): Builder
    {
        return $this->query()->published();
    }

    public function own(): Builder
    {
        return $this->query()->where('vendor_id', auth()->user()->vendor?->id);
    }

    public function publishedOrOwn(): Builder
    {
        return $this->query()->where(function ($query) {
            $query->published();

            $user = auth()->user();

            if (UserRole::Vendor->has($user) && $user->vendor) {
                $query->orWhere('vendor_id', $user->vendor->id);
            }
        });
    }

    public function query(): Builder
    {
        return Solution::query();
    }

    public function store(array $data): Solution
    {
        return in_transaction(function () use ($data) {
            $vendor = $this->vendorsRepository->findBySlugOrFail($data['vendor_id']);
            $data = $this->privatizeCoverResourceId($data);

            /** @var Solution $solution */
            $solution = $vendor->solutions()->create($data);
            $this->processPublishStatus($solution, $data['as_draft'] ?? true);
            $this->syncSolutionRelationships($solution, $data);

            $solution->refresh();

            return $solution;
        });
    }

    public function storeOwn(array $data): Solution
    {
        return in_transaction(function () use ($data) {
            abort_unless($vendor = auth()->user()->vendor, 404);
            $data = $this->privatizeCoverResourceId($data);

            /** @var Solution $solution */
            $solution = $vendor->solutions()->create($data);
            $this->processOwnPublishStatus($solution, $data['as_draft'] ?? false);
            $this->syncSolutionRelationships($solution, $data);

            if ($solution->is_awaiting_approval) {
                $this->notificationService->notifyStaff(new RequestedChange(auth()->user()->company, $solution));
            }

            $solution->refresh();

            return $solution;
        });
    }

    public function contactAsCurrentCompany(string $slug, string $message): void
    {
        /** @var User $user */
        $user = auth()->user();

        /** @var Solution $solution */
        $solution = $this->published()->findBySlugOrFail($slug);
        abort_if($solution->vendor_id === $user->vendor?->id, 404);

        $this->notificationService->notifyStaff(new SolutionContactRequested($user->company, $solution, $message));
    }

    public function update(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            $solution = $this->findBySlugOrFail($slug);
            $data = $this->privatizeCoverResourceId($data);

            $solution->fill($data)->save();
            $this->processPublishStatus($solution, $data['as_draft'] ?? true);
            $this->syncSolutionRelationships($solution, $data);
        });
    }

    public function updateOwn(string $slug, array $data): ReviewStatus
    {
        return in_transaction(function () use ($slug, $data) {
            abort_unless($vendor = auth()->user()->vendor, 404);
            /** @var Solution $solution */
            $solution = $vendor->solutions()->with('unapproved_change')->findBySlugOrFail($slug);
            $data = $this->privatizeCoverResourceId($data);

            $alreadyAwaitingApproval = $solution->is_awaiting_approval || $solution->unapproved_change;

            // Create change request if solution is already published.
            // Otherwise, simply update the original record
            if ($solution->is_published) {
                $relationships = [
                    'industries' => collect($data['industries']),
                    'technologies' => collect($data['technologies']),
                    'client' => $data['in_house'] ? null : $this->clientData($data),
                    'media' => $this->prepareSolutionMediaForReviewRequest($data['media']),
                ];

                $status = $solution->requestReview($data, $relationships);
            } else {
                $solution->fill($data)->save();
                $this->processOwnPublishStatus($solution, $data['as_draft'] ?? false);
                $this->syncSolutionRelationships($solution, $data);

                $status = ReviewStatus::Accepted;
            }

            if ($status === ReviewStatus::Accepted && ! $solution->is_draft && ! $alreadyAwaitingApproval) {
                $this->notificationService->notifyStaff(new RequestedChange(auth()->user()->company, $solution));
            }

            return $status;
        });
    }

    /**
     * @throws NoPendingChangesException
     */
    public function approveChange(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            /** @var Solution $solution */
            $solution = $this->query()->withExists('unapproved_change')->findBySlugOrFail($slug);
            $data = $this->privatizeCoverResourceId($data);

            $unapprovedChange = $solution->is_awaiting_approval
                ? $solution
                : $solution->unapproved_change;

            if (! $unapprovedChange) {
                throw new NoPendingChangesException($solution);
            }

            $unapprovedChange->fill($data);
            $this->syncSolutionRelationships($unapprovedChange, $data);
            $unapprovedChange->publish();

            $this->notificationService->notifyCompanyUsers($solution->vendor->company, new ChangesApproved($solution));
        });
    }

    public function rejectUnapprovedChange(string $slug): void
    {
        in_transaction(function () use ($slug) {
            /** @var Solution $solution */
            $solution = $this->query()->withExists('unapproved_change')->findBySlugOrFail($slug);

            if ($solution->unapproved_change_exists) {
                $solution->unapproved_change()->delete();
            } else {
                $solution->asDraft()->save();
            }

            $this->notificationService->notifyCompanyUsers($solution->vendor->company, new ChangesRejected($solution));
        });
    }

    public function discardOwnUnapprovedChange(string $slug): void
    {
        in_transaction(function () use ($slug) {
            abort_unless($vendor = auth()->user()->vendor, 404);
            /** @var Solution $solution */
            $solution = $vendor->solutions()->withExists('unapproved_change')->findBySlugOrFail($slug);

            if ($solution->unapproved_change_exists) {
                $solution->unapproved_change()->delete();
            } elseif ($solution->is_awaiting_approval) {
                $solution->asDraft()->save();
            }
        });
    }

    public function destroy(string $slug): void
    {
        $this->findBySlugOrFail($slug)->delete();
    }

    public function destroyOwn(string $slug): void
    {
        in_transaction(function () use ($slug) {
            abort_unless($vendor = auth()->user()->vendor, 404);
            $vendor->solutions()->findBySlugOrFail($slug)->delete();
        });
    }

    private function privatizeCoverResourceId(array $data): array
    {
        $resource = $this->warehouse->find($data['cover_resource_id']);
        $data['cover_resource_id'] = $resource->id;

        return $data;
    }

    private function processPublishStatus(Solution $solution, bool $asDraft): void
    {
        if ($asDraft) {
            $solution->asDraft();
        } else {
            $solution->publish();
        }

        $solution->save();
    }

    private function processOwnPublishStatus(Solution $solution, bool $tryAsDraft): void
    {
        if ($solution->is_draft && $tryAsDraft) {
            $solution->asDraft();
        } else {
            $solution->awaitApproval();
        }

        $solution->save();
    }

    private function syncSolutionRelationships(Solution $solution, array $data): void
    {
        $solution->industries()->sync($data['industries'] ?? []);
        $solution->technologies()->sync($data['technologies'] ?? []);

        if (! $data['in_house']) {
            $clientData = $this->clientData($data);

            if ($solution->client) {
                $solution->client->fill($clientData)->save();
            } else {
                $solution->client()->create($clientData);
            }
        } else {
            $solution->client()->delete();
        }

        if (array_key_exists('media', $data)) {
            /** @var SolutionMediaRepository $solutionMediaRepository */
            $solutionMediaRepository = app(SolutionMediaRepository::class);
            $solutionMediaRepository->syncManyForSolution($solution, $data['media']);
        }
    }

    private function clientData($data): array
    {
        $clientData = $data['client'];

        if ($clientData['id']) {
            $clientData['client_id'] = $clientData['id'];
            $clientData['name'] = null;
        }

        return $clientData;
    }

    private function prepareSolutionMediaForReviewRequest(array $media): SupportCollection
    {
        return collect($media)->map(function ($mediumData, $index) {
            $mediumData['order'] = $index;

            return $mediumData;
        });
    }
}
