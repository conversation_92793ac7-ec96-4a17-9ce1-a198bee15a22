<?php

namespace App\Repositories;

use App\Enums\TenderCandidateStatus;
use App\Enums\TenderStatus;
use App\Exceptions\CandidateAlreadyAppliedToTenderPositionException;
use App\Exceptions\CannotApplyToOwnTenderException;
use App\Exceptions\InvalidTenderCandidateNextStatusException;
use App\Exceptions\TenderHasEndedException;
use App\Exceptions\TenderNotOpenException;
use App\Facades\CandidateMatchingApi;
use App\Jobs\RunCandidateMatching;
use App\Mail\Tenders\CandidateStatusUpdated;
use App\Mail\Tenders\NewCandidateApplication;
use App\Mail\Tenders\NewCandidateApproved;
use App\Models\Candidate;
use App\Models\Tender;
use App\Models\TenderPosition;
use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;

readonly class TenderPositionsRepository
{
    public function __construct(
        private TendersRepository $tendersRepository,
        private CandidatesRepository $candidatesRepository,
        private NotificationService $notificationService,
    ) {}

    public function query(): Builder
    {
        return TenderPosition::query()->with('seniorities');
    }

    public function findBySlugOrFail(string $slug): TenderPosition
    {
        return $this->query()->findBySlugOrFail($slug);
    }

    public function ofTender(string|Tender $tender): Builder
    {
        if (is_string($tender)) {
            $tender = $this->tendersRepository->findBySlugOrFail($tender);
        }

        return $this->query()->whereBelongsTo($tender);
    }

    public function ofPublishedOrOwnTender(string|Tender $tender): Builder
    {
        if (is_string($tender)) {
            $tender = $this->tendersRepository->publishedOrOwn()->findBySlugOrFail($tender);
        }

        return $this->query()->whereBelongsTo($tender);
    }

    public function store(string $tenderSlug, array $data): TenderPosition
    {
        return in_transaction(function () use ($tenderSlug, $data) {
            $tender = $this->tendersRepository->findBySlugOrFail($tenderSlug);

            /** @var TenderPosition $position */
            $position = $tender->positions()->create($data);

            $this->syncTenderPositionRelationships($position, $data);

            return $position;
        });
    }

    public function update(string $tenderSlug, string $slug, array $data): void
    {
        in_transaction(function () use ($tenderSlug, $slug, $data) {
            /** @var TenderPosition $position */
            $position = tap($this->ofTender($tenderSlug)
                ->findBySlugOrFail($slug)
                ->fill($data))
                ->save();

            $this->syncTenderPositionRelationships($position, $data);
        });
    }

    /**
     * @throws InvalidTenderCandidateNextStatusException
     */
    public function updateCandidateStatus(
        string $tenderSlug, string $candidateSlug, TenderCandidateStatus $status, ?string $rejectionReason = null,
    ): void {
        in_transaction(function () use ($tenderSlug, $candidateSlug, $status, $rejectionReason) {
            /** @var Candidate $candidate */
            $candidate = $this->candidatesRepository
                ->clonedFinishedOfTender($tenderSlug)
                ->with('positions')
                ->findBySlugOrFail($candidateSlug);

            $this->updateStatusOfFetchedCandidate($candidate, $status, rejectionReason: $rejectionReason, force: true);

            if ($status === TenderCandidateStatus::Approved)
            {
                $tender = Tender::with('company.users')->findBySlugOrFail($tenderSlug);
                $this->notificationService->notifyCompanyUsers($tender->company, new NewCandidateApproved($tender, $candidate));
            }
        });
    }

    /**
     * @throws InvalidTenderCandidateNextStatusException
     * @throws TenderHasEndedException
     */
    public function updateOwnTenderCandidateStatus(
        string $tenderSlug, string $candidateSlug, TenderCandidateStatus $status, ?string $rejectionReason,
    ): void {
        in_transaction(function () use ($tenderSlug, $candidateSlug, $status, $rejectionReason) {
            /** @var Tender $tender */
            $tender = $this->tendersRepository->own()->findBySlugOrFail($tenderSlug);
            if ($tender->status === TenderStatus::Ended) {
                throw new TenderHasEndedException($tender);
            }

            /** @var Candidate $candidate */
            $candidate = $this->candidatesRepository
                ->ownClonedFinishedOfTender($tenderSlug)
                ->with('positions')
                ->findBySlugOrFail($candidateSlug);

            if ($status->isProtected()) {
                // Cloned candidates have only one position assigned
                /** @var TenderPosition $position */
                $position = $candidate->positions->first();
                throw new InvalidTenderCandidateNextStatusException($position, $candidate, $position->pivot->status, $status);
            }

            $this->updateStatusOfFetchedCandidate($candidate, $status, rejectionReason: $rejectionReason);
        });
    }

    /**
     * @throws InvalidTenderCandidateNextStatusException
     */
    private function updateStatusOfFetchedCandidate(
        Candidate $candidate, TenderCandidateStatus $status, ?string $rejectionReason = null, bool $force = false,
    ): void {
        in_transaction(function () use ($candidate, $status, $rejectionReason, $force) {
            // Cloned candidates have only one position assigned
            /** @var TenderPosition $position */
            $position = $candidate->positions->first();

            /** @var TenderCandidateStatus $oldStatus */
            $oldStatus = $position->pivot->status;
            $oldRejectionReason = $position->pivot->rejection_reason;

            // If no change in the status or rejection reason is required, we can ignore the request
            if ($oldStatus === $status && $oldRejectionReason === $rejectionReason) {
                return;
            }

            if (! $force && ! $oldStatus->isNextStatus($status)) {
                throw new InvalidTenderCandidateNextStatusException($position, $candidate, $oldStatus, $status);
            }

            $pivotData = [
                'status' => $status,
                'rejection_reason' => $rejectionReason ?: null,
            ];
            $position->candidates()->updateExistingPivot($candidate->id, $pivotData, touch: false);

            // Always notify clients of effective status change, so that they are up-to-date with application
            $this->notificationService->notifyCompanyUsers(
                $candidate->company,
                new CandidateStatusUpdated($position->tender, $position, $candidate, $pivotData),
            );
        });
    }

    public function updateOwnTenderCandidateNote(string $tenderSlug, string $candidateSlug, ?string $note): void
    {
        in_transaction(function () use ($tenderSlug, $candidateSlug, $note) {
            /** @var Candidate $candidate */
            $candidate = $this->candidatesRepository
                ->ownClonedFinishedOfTender($tenderSlug)
                ->with('positions')
                ->findBySlugOrFail($candidateSlug);

            // Cloned candidates have only one position assigned
            /** @var TenderPosition $position */
            $position = $candidate->positions->first();

            $position->candidates()->updateExistingPivot($candidate->id, ['client_note' => $note ?: null], touch: false);
        });
    }

    public function destroy(string $tenderSlug, string $id): void
    {
        $this->ofTender($tenderSlug)
            ->findBySlugOrFail($id)
            ->delete();
    }

    /**
     * @throws CandidateAlreadyAppliedToTenderPositionException
     * @throws CannotApplyToOwnTenderException
     */
    public function apply(string $tenderSlug, string $slug, array $data): void
    {
        in_transaction(function () use ($tenderSlug, $slug, $data) {
            $candidate = $this->candidatesRepository->findFinishedOriginalBySlugOrFail($data['candidate_id']);
            $this->applyCandidate($tenderSlug, $slug, $candidate);
        });
    }

    /**
     * @throws CandidateAlreadyAppliedToTenderPositionException
     * @throws TenderNotOpenException
     * @throws CannotApplyToOwnTenderException
     */
    public function applyAsVendor(string $tenderSlug, string $slug, array $data): void
    {
        in_transaction(function () use ($tenderSlug, $slug, $data) {
            /** @var Tender $tender */
            $tender = $this->tendersRepository->published()->findBySlugOrFail($tenderSlug);
            if ($tender->status !== TenderStatus::Open) {
                throw new TenderNotOpenException($tender);
            }

            $candidate = $this->candidatesRepository->updateOrCreateOwn($data['candidate_id'] ?? null, $data);
            $this->applyCandidate($tenderSlug, $slug, $candidate, notifyStaff: true);
        });
    }

    /**
     * @throws CandidateAlreadyAppliedToTenderPositionException
     * @throws CannotApplyToOwnTenderException
     */
    private function applyCandidate(string $tenderSlug, string $slug, Candidate $candidate, bool $notifyStaff = false): void
    {
        in_transaction(function () use ($tenderSlug, $slug, $candidate, $notifyStaff) {
            /** @var TenderPosition $position */
            $position = $this
                ->ofTender($tenderSlug)
                ->with('tender')
                ->findBySlugOrFail($slug);

            if ($position->tender->company_id === $candidate->vendor->company_id) {
                throw new CannotApplyToOwnTenderException($position->tender);
            }

            if ($position->candidates()->where('origin_id', $candidate->id)->exists()) {
                throw new CandidateAlreadyAppliedToTenderPositionException($position, $candidate);
            }

            $clone = $this->candidatesRepository->clone($candidate);
            $position->candidates()->attach($clone, [
                'company_id' => $position->tender->company_id,
                'tender_id' => $position->tender_id,
                'status' => TenderCandidateStatus::Approved,
            ]);

            RunCandidateMatching::dispatch($position, $clone);

            if ($notifyStaff) {
                $this->notificationService->notifyStaff(
                    new NewCandidateApplication($position->tender, $position, $clone->company, $clone),
                );
            }
        });
    }

    public function runCandidateMatching(TenderPosition $position, Candidate $candidate): void
    {
        $candidateMatchingApiPayload = CandidateMatchingApi::createPayload($position, $candidate);

        $position->candidates()->updateExistingPivot($candidate->id, [
            'matching_api_payload' => $candidateMatchingApiPayload,
        ]);

        try {
            $candidateMatchingResponse = CandidateMatchingApi::requestMatch($candidateMatchingApiPayload);
        } catch (ConnectionException|RequestException $e) {
            $candidateMatchingResponse = [
                'status' => $e->getCode(),
                'data' => null,
            ];
        }

        $candidateMatchingResponseData = $candidateMatchingResponse['data'] ?? collect();

        $position->candidates()->updateExistingPivot($candidate->id, [
            'matching_api_response' => $candidateMatchingResponseData->isEmpty() ? null : $candidateMatchingResponseData,
            'matching_api_response_status' => $candidateMatchingResponse['status'],
        ]);
    }

    private function syncTenderPositionRelationships(TenderPosition $position, $data): void
    {
        $seniorities = collect($data['seniorities'])->map(fn ($seniority) => [
            'seniority' => $seniority,
        ]);
        $position->seniorities()->delete();
        $position->seniorities()->createMany($seniorities);

        $position->technologies()->sync($data['technologies'] ?? []);
    }
}
