<?php

namespace App\Repositories;

use App\Enums\UserRole;
use App\Exceptions\ResetTokenExpiredException;
use App\Mail\Auth\PasswordWasReset;
use App\Mail\Auth\RequestPasswordReset;
use App\Models\Company;
use App\Models\PasswordReset;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Mail\Mailer;
use Libs\Warehouse\Warehouse;

class UsersRepository
{
    public function __construct(
        private CompaniesRepository $companiesRepository,
        private Warehouse           $warehouse,
        private Mailer              $mailer,
    ) {}

    public function all(): Collection
    {
        return $this->query()->get();
    }

    public function findById(int $id): User
    {
        return $this->query()->findOrFail($id);
    }

    public function findByEmail(string $email): User
    {
        return $this->query()->where('email', $email)->firstOrFail();
    }

    public function query(): Builder
    {
        return User::query()->with([
            'company',
            'roles' => fn ($query) => $query->where('guard_name', 'web')
        ]);
    }

    public function users(): Builder
    {
        return $this->query()
            ->withoutRole(UserRole::SuperAdmin)
            ->whereHas('company');
    }

    public function superAdmins(): Builder
    {
        return $this->query()->role(UserRole::SuperAdmin);
    }

    public function updateOwnProfile(array $data): void
    {
        in_transaction(function () use ($data) {
            $data = $this->privatizeResourcesId($data);

            auth()->user()
                ->fill($data)
                ->save();
        });
    }

    public function storeUser(string|Company $companySlug, array $data): User
    {
        return in_transaction(function () use ($companySlug, $data) {
            $data = $this->privatizeResourcesId($data);

            $company = is_string($companySlug)
                ? $this->companiesRepository->query()->findBySlugOrFail($companySlug)
                : $companySlug;

            $user = $company->users()->make($data);
            $user->save();

            if (array_key_exists('roles', $data)) {
                $allowedRoles = $this->filterAllowedRoles($user, $data['roles']);
                $user->syncRoles($allowedRoles);
            }

            return $user;
        });
    }

    public function updateUser(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            $data = $this->privatizeResourcesId($data);

            $user = $this->users()->findBySlugOrFail($slug);
            $user->update($data);

            if (array_key_exists('roles', $data)) {
                $allowedRoles = $this->filterAllowedRoles($user, $data['roles']);
                $user->syncRoles($allowedRoles);
            }
        });
    }

    public function destroyUser(string $slug): void
    {
        $this->users()
            ->findBySlugOrFail($slug)
            ->delete();
    }

    public function storeSuperAdmin(array $data): User
    {
        return in_transaction(function () use ($data) {
            $data = $this->privatizeResourcesId($data);

            $user = User::make($data);
            $user->save();
            $user->assignRole(UserRole::SuperAdmin);

            return $user;
        });
    }

    public function updateSuperAdmin(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            $data = $this->privatizeResourcesId($data);

            $this->superAdmins()
                ->findBySlugOrFail($slug)
                ->fill($data)
                ->save();
        });
    }

    public function destroySuperAdmin(string $slug): void
    {
        $this->superAdmins()
            ->findBySlugOrFail($slug)
            ->delete();
    }

    public function sendPasswordResetEmail(string $email): void
    {
        in_transaction(function () use ($email) {
            if ($this->users()->where('email', $email)->exists()) {
                $passwordReset = PasswordReset::createFor($email);
                $this->mailer->to($email)->send(new RequestPasswordReset($passwordReset->fresh()));
            }
        });
    }

    /**
     * @throws ResetTokenExpiredException
     */
    public function resetPassword(string $token, string $password): User
    {
        return in_transaction(function () use ($token, $password): User {
            if ($passwordReset = PasswordReset::findBy($token)) {
                /** @var User $user */
                if ($user = $this->users()->where('email', $passwordReset->email)->first()) {
                    $user->password = $password;
                    $user->save();
                    $passwordReset->delete();

                    $this->mailer->to($user->email)->send(new PasswordWasReset);

                    return $user;
                }
            }

            throw new ResetTokenExpiredException;
        });
    }

    public function filterAllowedRoles(User $user, array $roles): \Illuminate\Support\Collection
    {
        return collect($roles)
            ->map(fn (string|UserRole $role): UserRole => $role instanceof UserRole ? $role : UserRole::from($role))
            ->filter(fn (UserRole $role): bool => in_array($role, UserRole::allowedRoles($user->company)))
            ->values();
    }

    private function privatizeResourcesId(array $data): array
    {
        if ($data['avatar_resource_id'] ?? false) {
            $avatarResource = $this->warehouse->find($data['avatar_resource_id']);
            $data['avatar_resource_id'] = $avatarResource->id;
        }

        return $data;
    }
}
