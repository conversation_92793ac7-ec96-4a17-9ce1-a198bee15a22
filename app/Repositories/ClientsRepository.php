<?php

namespace App\Repositories;

use App\Models\Client;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Libs\Warehouse\Warehouse;

class ClientsRepository
{
    public function __construct(
        private Warehouse $warehouse,
    ) {}

    /**
     * @return Collection<Client>
     */
    public function all(): Collection
    {
        return $this->query()->get();
    }

    public function query(): Builder
    {
        return Client::query();
    }

    public function findBySlugOrFail(string $slug): Client
    {
        return $this->query()->with('logo_resource')->findBySlugOrFail($slug);
    }

    public function store(array $data): Client
    {
        return in_transaction(function () use ($data) {
            $data = $this->privatizeResourcesId($data);

            return Client::create($data);
        });
    }

    public function update(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            /** @var Client $client */
            $client = $this->query()->findBySlugOrFail($slug);
            $data = $this->privatizeResourcesId($data);

            $client->fill($data);
            $client->save();
        });
    }

    public function destroy(string $slug): void
    {
        /** @var Client $client */
        $client = $this->query()->findBySlugOrFail($slug);
        $client->delete();
    }

    private function privatizeResourcesId(array $data): array
    {
        if ($data['logo_resource_id'] ?? false) {
            $logoResource = $this->warehouse->find($data['logo_resource_id']);
            $data['logo_resource_id'] = $logoResource->id;
        }

        return $data;
    }
}
