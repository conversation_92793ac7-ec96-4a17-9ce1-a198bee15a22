<?php

namespace App\Repositories;

use App\Exceptions\CannotMakeCompanyVendorWithoutProfileException;
use App\Mail\Auth\RegistrationApproved;
use App\Mail\Auth\RegistrationRejected;
use App\Models\Company;
use App\Models\User;
use App\Models\Workspace;
use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Libs\Overseer\ChangesApproved;
use Libs\Overseer\ChangesRejected;
use Libs\Overseer\NoPendingChangesException;
use Libs\Overseer\RequestedChange;
use Libs\Overseer\ReviewStatus;
use Libs\Warehouse\Resource;
use Libs\Warehouse\Warehouse;
use Spatie\QueryBuilder\AllowedFilter;
use Spatie\QueryBuilder\QueryBuilder;

class CompaniesRepository
{
    public function __construct(
        private NotificationService $notificationService,
        private Warehouse           $warehouse,
    )
    {
    }

    /**
     * @return Collection<Company>
     */
    public function all(): Collection
    {
        return $this->query()->get();
    }

    public function findUsersCompany(): Company
    {
        /** always Company @noinspection PhpIncompatibleReturnTypeInspection */
        return $this
            ->query()
            ->with([
                'document_resources',
                'unapproved_change',
                'unapproved_change.document_resources',
            ])
            ->findOrFail(auth()->user()->company_id);
    }

    public function findUsersCompanyWithVendor(): Company
    {
        /** always Company @noinspection PhpIncompatibleReturnTypeInspection */
        return $this
            ->query()
            ->with(
                'vendor.industries', 'vendor.technologies',
                'vendor.clients.client', 'vendor.employee_positions', 'vendor.unapproved_change',
                'vendor.unapproved_change.industries', 'vendor.unapproved_change.technologies',
                'vendor.unapproved_change.clients.client', 'vendor.unapproved_change.employee_positions',
            )
            ->findOrFail(auth()->user()->company_id);
    }

    public function findBySlugOrFail(string $slug): Company
    {
        return $this->query()
            ->with(
                'logo_resource', 'cover_resource',
                'public_logo_resource', 'public_cover_resource',
                'document_resources',
                'unapproved_change',
                'unapproved_change.logo_resource', 'unapproved_change.cover_resource',
                'unapproved_change.public_logo_resource', 'unapproved_change.public_cover_resource',
                'unapproved_change.document_resources',
                'vendor.industries', 'vendor.technologies',
                'vendor.clients.client', 'vendor.employee_positions',
                'vendor.unapproved_change.industries', 'vendor.unapproved_change.technologies',
                'vendor.unapproved_change.clients.client', 'vendor.unapproved_change.employee_positions',
            )
            ->findBySlugOrFail($slug);
    }

    public function findVendorsByName(string $name, ?array $select = ['*']): ?Company
    {
        return Company::query()
            ->select($select)
            ->where('is_vendor', 1)
            ->where('name', 'like', "%$name%")
            ->first();

    }

    public function findCompaniesByWorkspace(Workspace $workspace): QueryBuilder
    {
        $query = $this->byWorkspace($this->queryBuilder(), $workspace);

        return $this->withImages(
            $query->whereIn('id', $workspace->companies->pluck('id'))
                ->with([
                    'vendor.technologies',
                    'vendor.industries',
                    'vendor.main_industry',
                ])
                ->allowedFilters([
                    AllowedFilter::partial('search', 'name'),
                    AllowedFilter::exact('category'),
                    AllowedFilter::exact('status', 'publish_status'),
                    AllowedFilter::exact('country'),
                    AllowedFilter::exact('technologies', 'vendor.technologies.id'),
                    AllowedFilter::exact('industries', 'vendor.industries.id'),
                    AllowedFilter::exact('main_industry', 'vendor.main_industry_id'),
                ])
                ->allowedSorts(['name'])
        );
    }

    public function query(): Builder
    {
        return Company::query();
    }

    public function queryBuilder(): QueryBuilder
    {
        return QueryBuilder::for($this->query());
    }

    public function vendors(): Builder
    {
        return $this->query()->with('vendor')->vendors();
    }

    public function byWorkspace(QueryBuilder $query, Workspace $workspace): QueryBuilder
    {
        return $query->whereIn('id', $workspace->companies->pluck('id'));
    }

    public function byName(Builder $query, string $slug): Builder
    {
        return $query->where('name', 'like', "%$slug%");
    }

    public function withImages(QueryBuilder $query): QueryBuilder
    {
        return $query->with(['logo_resource', 'cover_resource']);
    }

    public function store(array $data): Company
    {
        return in_transaction(function () use ($data) {
            $data = $this->privatizeResourcesId($data);

            return Company::create($data);
        });
    }

    public function update(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            $data = $this->privatizeResourcesId($data);

            $this->query()
                ->findBySlugOrFail($slug)
                ->fill($data)
                ->save();
        });
    }

    public function updateOwn(array $data): ReviewStatus
    {
        return in_transaction(function () use ($data) {
            /** @var Company $company */
            $company = $this->query()->findOrFail(auth()->user()->company_id);
            $data = $this->privatizeResourcesId($data);

            $alreadyAwaitingApproval = $company && ($company->is_awaiting_approval || $company->unapproved_change);

            $relationships = [
                'document_resources' => collect($data['company_document_resource_ids'] ?? [])
                    ->map(fn (string $id): array => ['resource_id' => $id])
                    ->all(),
            ];
            $status = $company->requestReview($data, $relationships);

            if ($status === ReviewStatus::Accepted && !$company->is_draft && !$alreadyAwaitingApproval) {
                $this->notificationService->notifyStaff(new RequestedChange($company, $company));
            }

            return $status;
        });
    }

    public function discardOwnUnapprovedChange(): void
    {
        in_transaction(fn() => $this->query()
            ->findOrFail(auth()->user()->company_id)
            ->unapproved_change()
            ->delete()
        );
    }

    /**
     * @throws NoPendingChangesException
     */
    public function approveChange(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            $company = $this->findBySlugOrFail($slug);
            $data = $this->privatizeResourcesId($data);

            $isFreshlyRegistered = $company->is_awaiting_approval;
            $unapprovedChange = $isFreshlyRegistered
                ? $company
                : $company->unapproved_change;

            if (!$unapprovedChange) {
                throw new NoPendingChangesException($company);
            }

            $unapprovedChange->fill($data)->publish();

            $this->notificationService->notifyCompanyUsers(
                $company,
                $isFreshlyRegistered ? new RegistrationApproved($company) : new ChangesApproved($company),
            );
        });
    }

    public function rejectUnapprovedChange(string $slug): void
    {
        in_transaction(function () use ($slug) {
            /** @var Company $company */
            $company = $this->query()->findBySlugOrFail($slug);

            if ($company->is_awaiting_approval) {
                /** @var User $user */
                $user = $company->users()->first();
                $company->delete();

                // We can safely assume that there is only one user of a company
                // that has not yet had its registration approved. We need to send
                // the mail manually as once company is deleted it is not possible
                // to fetch the list of their users.
                $this->notificationService->notifyUser($user, new RegistrationRejected($company));
            } else {
                $company->unapproved_change()->delete();
                $this->notificationService->notifyCompanyUsers($company, new ChangesRejected($company));
            }
        });
    }

    /**
     * @throws CannotMakeCompanyVendorWithoutProfileException
     */
    public function updateIsVendor(string $slug, bool $isVendor): void
    {
        in_transaction(function () use ($slug, $isVendor) {
            $company = $this->findBySlugOrFail($slug);

            // FIXME: use model casts for strict comparison?
            if ($company->is_vendor == $isVendor) {
                return;
            }

            if ($isVendor && (!$company->vendor || $company->vendor->is_awaiting_approval)) {
                throw new CannotMakeCompanyVendorWithoutProfileException($company);
            }

            $company->is_vendor = $isVendor;
            $company->save();
        });
    }

    public function updateNotes(string $slug, ?string $notes): void
    {
        in_transaction(function () use ($slug, $notes) {
            $company = $this->findBySlugOrFail($slug);
            $company->notes = $notes;
            $company->save();
        });
    }

    public function destroy(string $slug): void
    {
        $this->findBySlugOrFail($slug)->delete();
    }

    private function privatizeResourcesId(array $data): array
    {
        if ($data['logo_resource_id'] ?? false) {
            $logoResource = $this->warehouse->find($data['logo_resource_id']);
            $data['logo_resource_id'] = $logoResource->id;
        }
        if ($data['cover_resource_id'] ?? false) {
            $coverResource = $this->warehouse->find($data['cover_resource_id']);
            $data['cover_resource_id'] = $coverResource->id;
        }

        if ($data['company_document_resource_ids'] ?? false) {
            $data['company_document_resource_ids'] = collect($data['company_document_resource_ids'])
                ->map(fn(string $resourceId): Resource => $this->warehouse->find($resourceId))
                ->pluck('id')
                ->toArray();
        }

        return $data;
    }
}
