<?php

namespace App\Repositories;

use App\Enums\SolutionMediumType;
use App\Models\Solution;
use App\Models\SolutionMedium;
use Illuminate\Database\Eloquent\Builder;

class SolutionMediaRepository
{
    public function __construct(
        private SolutionsRepository $solutionsRepository,
    ) {}

    public function query(): Builder
    {
        return SolutionMedium::query();
    }

    public function ofSolution(string|Solution $solution): Builder
    {
        if (is_string($solution)) {
            $solution = $this->solutionsRepository->findBySlugOrFail($solution);
        }

        return $this->query()->whereBelongsTo($solution);
    }

    public function store(Solution|string $solutionSlug, array $data): SolutionMedium
    {
        return in_transaction(function () use ($solutionSlug, $data) {
            $solution = $solutionSlug instanceof Solution
                ? $solutionSlug
                : $this->solutionsRepository->findBySlugOrFail($solutionSlug);

            return $this->createForType($solution, SolutionMediumType::from($data['type']), $data);
        });
    }

    public function syncManyForSolution(Solution $solution, array $data): void
    {
        in_transaction(function () use ($solution, $data) {
            $data = collect($data)->map(fn (array $medium, int $index) => $medium + ['order' => $index]);
            $keepIds = $data->whereNotNull('id')->pluck('id');
            $existingById = $solution->media->keyBy('id');

            // Delete media that were removed from the list
            $existingById->whereNotIn('id', $keepIds)->each(fn (SolutionMedium $medium) => $medium->delete());

            // Sync the rest - update existing and create new
            $data->each(function (array $mediumData) use ($solution, $existingById) {
                if ($mediumData['id'] ?? false) {
                    $existingById
                        ->get($mediumData['id'])
                        ->fill($mediumData)
                        ->save();
                } else {
                    $this->createForType($solution, SolutionMediumType::from($mediumData['type']), $mediumData);
                }
            });
        });
    }

    public function update(string $solutionSlug, string $id, array $data): void
    {
        in_transaction(function () use ($solutionSlug, $id, $data) {
            $this
                ->ofSolution($solutionSlug)
                ->find($id)
                ->fill($data)
                ->save();
        });
    }

    public function destroy(string $solutionSlug, string $id): void
    {
        in_transaction(function () use ($solutionSlug, $id) {
            $this
                ->ofSolution($solutionSlug)
                ->find($id)
                ->delete();
        });
    }

    private function createForType(Solution $solution, SolutionMediumType $type, array $data): SolutionMedium
    {
        $fillData = compact('type') + array_only($data, ['name', 'order']);

        /** @var SolutionMedium $medium */
        $medium = $solution->media()->make($fillData);
        $type->driver()->save($medium, $data['source']);
        $medium->save();

        return $medium;
    }
}
