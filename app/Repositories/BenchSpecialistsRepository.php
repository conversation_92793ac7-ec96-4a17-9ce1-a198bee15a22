<?php

namespace App\Repositories;

use App\Exceptions\BenchSpecialistEnlistmentExpiredException;
use App\Exceptions\CandidateAlreadyEnlistedOnTheBenchException;
use App\Mail\Bench\BenchSpecialistInvitedToInterview;
use App\Mail\Bench\CandidateEnlistRequestToBench;
use App\Models\BenchSpecialist;
use App\Models\Candidate;
use App\Models\User;
use App\Models\Vendor;
use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Libs\Overseer\ChangesApproved;
use Libs\Overseer\ChangesRejected;
use Libs\Overseer\NoPendingChangesException;
use Libs\Overseer\PublishStatus;

readonly class BenchSpecialistsRepository
{
    public function __construct(
        private CandidatesRepository $candidatesRepository,
        private NotificationService $notificationService,
    ) {}

    public function query(): Builder
    {
        return BenchSpecialist::query()->whereHas('candidate.company');
    }

    /**
     * @return Collection<BenchSpecialist>
     */
    public function all(): Collection
    {
        return $this->query()->get();
    }

    public function findBySlugOrFail(string $slug): BenchSpecialist
    {
        return $this->query()
            ->with('candidate')
            ->findBySlugOrFail($slug);
    }

    public function published(): Builder
    {
        return $this->query()->published();
    }

    public function active(): Builder
    {
        return $this->query()->active();
    }

    public function publishedActive(): Builder
    {
        return $this->published()->active();
    }

    public function publishedActiveOfOthers(): Builder
    {
        return $this->publishedActive()->whereRelation('candidate', 'vendor_id', '!=', auth()->user()->vendor?->id);
    }

    public function own(): Builder
    {
        return $this->query()->whereRelation('candidate', 'vendor_id', auth()->user()->vendor?->id);
    }

    public function ownActive(): Builder
    {
        return $this->own()->active();
    }

    /**
     * @param array<string> $data
     * @return array<BenchSpecialist>
     */
    public function enlist(array $data, ?Vendor $vendor = null): array
    {
        return in_transaction(function () use ($data, $vendor) {
            $specialists = [];

            try {
                foreach ($data['candidate_ids'] ?? [] as $id) {
                    /** @var Candidate $candidate */
                    $candidate = $this->candidatesRepository
                        ->finishedOriginal()
                        ->when(
                            $vendor?->exists,
                            fn(Builder $query): Builder => $query->where('vendor_id', $vendor->id)
                        )
                        ->findBySlugOrFail($id);

                    $specialists[] = $this->enlistCandidate($candidate, $data);
                }
            } catch (CandidateAlreadyEnlistedOnTheBenchException) {
                //
            }

            return $specialists;
        });
    }

    /**
     * @throws CandidateAlreadyEnlistedOnTheBenchException
     */
    public function enlistOwn(string $slug, array $data): BenchSpecialist
    {
        return in_transaction(function () use ($slug, $data) {
            /** @var Candidate $candidate */
            $candidate = $this->candidatesRepository->ownFinishedOriginal()->findBySlugOrFail($slug);
            $specialist = $this->enlistCandidate($candidate, $data);

            $this->notificationService->notifyStaff(new CandidateEnlistRequestToBench($candidate->company, $specialist));

            return $specialist;
        });
    }

    /**
     * @throws CandidateAlreadyEnlistedOnTheBenchException
     */
    private function enlistCandidate(Candidate $candidate, array $data): BenchSpecialist
    {
        return in_transaction(function () use ($candidate, $data) {
            if ($this->active()->whereRelation('candidate', 'origin_id', $candidate->id)->first()) {
                throw new CandidateAlreadyEnlistedOnTheBenchException($candidate);
            }

            $clone = $this->candidatesRepository->clone($candidate);

            $specialistData = [
                'available_from' => $data['available_from'],
                'available_to' => now()->parse($data['available_from'])->addMonth()->endOfDay(),
            ];

            return $clone->benchSpecialist()->create($specialistData);
        });
    }

    public function unlistOwn(string $slug): void
    {
        in_transaction(function () use ($slug) {
            /** @var BenchSpecialist $specialist */
            $specialist = $this->ownActive()->findBySlugOrFail($slug);

            if ($specialist->publish_status === PublishStatus::AwaitingApproval) {
                $specialist->delete();
            } else {
                $specialist->available_to = now();
                $specialist->save();
            }
        });
    }

    /**
     * @throws BenchSpecialistEnlistmentExpiredException
     */
    public function update(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            /** @var BenchSpecialist $specialist */
            $specialist = $this->query()->findBySlugOrFail($slug);

            if ($specialist->is_published && $specialist->available_to->isPast()) {
                throw new BenchSpecialistEnlistmentExpiredException($specialist);
            }

            $specialistData = [
                'available_from' => $data['available_from'],
                'available_to' => now()->parse($data['available_from'])->addMonth()->endOfDay(),
            ];

            $specialist->fill($specialistData)->save();
        });
    }

    /**
     * @throws NoPendingChangesException
     */
    public function approveChange(string $slug): void
    {
        in_transaction(function () use ($slug) {
            /** @var BenchSpecialist $specialist */
            $specialist = $this->query()->with('unapproved_change')->findBySlugOrFail($slug);

            $unapprovedChange = $specialist->is_awaiting_approval
                ? $specialist
                : $specialist->unapproved_change;

            if (! $unapprovedChange) {
                throw new NoPendingChangesException($specialist);
            }

            $unapprovedChange->publish();

            $this->notificationService->notifyCompanyUsers($specialist->candidate->company, new ChangesApproved($specialist));
        });
    }

    public function rejectUnapprovedChange(string $slug): void
    {
        in_transaction(function () use ($slug) {
            /** @var BenchSpecialist $specialist */
            $specialist = $this->query()->findBySlugOrFail($slug);
            $company = $specialist->candidate->company;

            if ($specialist->is_awaiting_approval) {
                $specialist->delete();
            } else {
                $specialist->unapproved_change()->delete();
            }

            $this->notificationService->notifyCompanyUsers($company, new ChangesRejected($specialist));
        });
    }

    public function destroy(string $slug): void
    {
        /** @var BenchSpecialist $specialist */
        $specialist = $this->query()->findBySlugOrFail($slug);
        $specialist->delete();
    }
}
