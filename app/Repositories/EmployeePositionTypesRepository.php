<?php

namespace App\Repositories;

use App\Models\EmployeePosition;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class EmployeePositionTypesRepository
{
    /**
     * @return Collection<EmployeePosition>
     */
    public function all(): Collection
    {
        return $this->query()->get();
    }

    public function query(): Builder
    {
        return EmployeePosition::query();
    }

    public function findBySlugOrFail(string $slug): EmployeePosition
    {
        return $this->query()->findBySlugOrFail($slug);
    }

    public function store(array $data): EmployeePosition
    {
        $data['seniorities'] = $this->compactSeniorities($data['seniorities']);

        return EmployeePosition::create($data);
    }

    public function update(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            $data['seniorities'] = $this->compactSeniorities($data['seniorities']);
            /** @var EmployeePosition $position */
            $position = $this->query()->findBySlugOrFail($slug);
            $position->fill($data);
            $position->save();
        });
    }

    public function destroy(string $slug): void
    {
        /** @var EmployeePosition $position */
        $position = $this->query()->findBySlugOrFail($slug);
        $position->delete();
    }

    private function compactSeniorities(?array $seniorities): ?string
    {
        return $seniorities ? implode(',', $seniorities) : null;
    }
}
