<?php

namespace App\Repositories;

use App\Models\CompanyNote;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class CompanyNotesRepository
{
    public function __construct(
        private CompaniesRepository $companiesRepository,
    ) {}

    /**
     * @return Collection<CompanyNote>
     */
    public function all(): Collection
    {
        return $this->query()->get();
    }

    public function findByCompanySlugOrFail(string $slug): CompanyNote
    {
        return $this->query()
            ->ofCompany($slug)
            ->firstOrFail();
    }

    public function query(): Builder
    {
        return CompanyNote::query();
    }

    public function store(string $slug, array $data): CompanyNote
    {
        return in_transaction(function () use ($slug, $data) {
            $company = $this->companiesRepository->findBySlugOrFail($slug);

            return $company->notes()->forceCreate($data + [
                'user_id' => auth()->user()->id,
            ]);
        });
    }

    public function update(string $slug, int $id, array $data): void
    {
        in_transaction(function () use ($id, $slug, $data) {
            $this->query()
                ->ofCompany($slug)
                ->whereId($id)
                ->firstOrFail()
                ->fill($data)
                ->save();
        });
    }

    public function destroy(string $slug, int $id): void
    {
        $this->query()
            ->ofCompany($slug)
            ->whereId($id)
            ->delete();
    }
}
