<?php

namespace App\Repositories\Enterprise;

use Illuminate\Http\UploadedFile;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Str;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use App\Imports\Enterprise\CompaniesImport;
use App\Jobs\Enterprise\ProcessCompanyImport;
use App\Data\Enterprise\CompanyImportDto;
use App\Http\Requests\Enterprise\CompanyImportUpdateRequest;
use App\Exceptions\Enterprise\UnsupportedFileException;

class CompaniesImportRepository
{

    /**
     * Process an uploaded file and extract company data.
     *
     * @param UploadedFile $file
     * @return Collection|CompanyImportDto[]
     * @throws UnsupportedFileException If the file extension is unsupported.
     * @throws \Exception
     */
    public function processFile(UploadedFile $file): Collection
    {

        $extension = strtolower($file->getClientOriginalExtension());

        return match ($extension) {
            'csv', 'xls', 'xlsx' => $this->processExcelFile($file),
            default => throw new UnsupportedFileException($extension),
        };
    }


    /**
     * Process an Excel file (XLS/XLSX) and return company data.
     *
     * @param UploadedFile $file
     * @return Collection|CompanyImportDto[]
     * @throws \Exception If an error occurs during file processing.
     */
    protected function processExcelFile(UploadedFile $file): Collection
    {
        try {
            $import = new CompaniesImport();

            Excel::import($import, $file);

            // Retrieve Collection of companies from import
            $companyImportDtos = $this->getDtosFromCompanyNames($import->getCompanyNames());
        } catch (\Exception $e) {
            throw new \Exception('An error occurred while processing the file: ' . $e->getMessage());
        }

        $validator = Validator::make(
            ['companies_count' => $companyImportDtos->count()],
            ['companies_count' => 'integer|min:1'],
            ['companies_count.min' => 'At least two companies are required.']
        );

        if ($validator->fails()) {
            throw ValidationException::withMessages($validator->errors()->toArray());
        }

        $maxCompanies = config('services.companies_import.company_upload_max');
        // return collection of CompanyImportDto
        return $this->sendDtosToJobs(empty($maxCompanies) ? $companyImportDtos : $companyImportDtos->take((int) $maxCompanies));
    }

    /**
     * @param string[] $companyNames
     * @return Collection|CompanyImportDto[]
     */
    protected function getDtosFromCompanyNames(array $companyNames): Collection
    {
        return collect($companyNames)->map(function ($companyName) {
            return new CompanyImportDto(
                $companyName,
                $this->generateUid()
            );
        });
    }

     /**
     * Dispatch jobs to process companies.
     *
     * @param Collection|CompanyImportDto[] $companyImportDtos
     * @return Collection|CompanyImportDto[]
     * @throws \Exception If an error occurs during job dispatch.
     */
    protected function sendDtosToJobs(Collection $companyImportDtos): Collection
    {
        try {

            foreach ($companyImportDtos as $companyImportDto) {
                ProcessCompanyImport::dispatch($companyImportDto);
            }
            return $companyImportDtos;

        } catch (\Exception $e) {
            throw new \Exception('An error occurred while sending companies into queue: ' . $e->getMessage());
        }
    }

    /**
     * Update company's data and dispatch job.
     *
     * @param CompanyImportUpdateRequest|CompanyImportDto $input
     */
    public function updateCompanyImport(CompanyImportUpdateRequest|CompanyImportDto $input): void
    {
        $companyImportDto = $input instanceof CompanyImportUpdateRequest
            ? CompanyImportDto::fromRequest($input)
            : $input;

        ProcessCompanyImport::dispatch($companyImportDto);
    }

    /**
     * Generate a unique identifier for a company.
     *
     * @return string A unique identifier (UUID).
     */
    protected function generateUid(): string
    {
        return Str::uuid()->toString();
    }
}
