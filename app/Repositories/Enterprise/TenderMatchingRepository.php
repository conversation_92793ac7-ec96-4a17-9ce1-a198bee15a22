<?php

namespace App\Repositories\Enterprise;

use App\Enums\Enterprise\TenderMatchingCompaniesFilter;
use App\Facades\MatchingApi;
use App\Models\Company;
use App\Models\Tender;
use App\Models\TenderMatch;
use App\Models\TenderMatchCompany;
use App\Models\Workspace;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;

class TenderMatchingRepository
{
    public function runMatching(
        Tender $tender,
        Workspace $workspace,
        TenderMatchingCompaniesFilter $companiesFilter,
    ): TenderMatch
    {
        $companyUuids = $workspace->companies()->pluck('uuid')->filter()->values()->toArray();
        $matchingApiPayload = MatchingApi::createPayload($tender, $companiesFilter, $companyUuids);

        $tenderMatch = $tender->matches()->create([
            'companies_filter' => $companiesFilter->value,
            'matching_api_payload' => $matchingApiPayload
        ]);

        try {
            $matchingResponse = MatchingApi::requestMatches($matchingApiPayload, $companiesFilter);
        } catch (ConnectionException|RequestException $e) {
            $matchingResponse = [
                'status' => $e->getCode(),
                'data' => null,
            ];
        }

        $matchingResponseData = $matchingResponse['data'] ?? collect();
        if ($matchingResponseData->isNotEmpty()) {
            $tenderMatch->matching_api_response = $matchingResponseData;
        }

        $tenderMatch->matching_api_response_status = $matchingResponse['status'];
        $tenderMatch->save();

        foreach ($matchingResponseData as $companyMatching) {
            /** @var Collection $companyMatching */
            $company = Company::query()->where('uuid', $companyMatching->get('company_id'))->first();

            if (!$company) {
                Log::warning('Matched company not found in database: ' . $companyMatching->get('company_id'));
                continue;
            }

            TenderMatchCompany::create([
                'match_id' => $tenderMatch->id,
                'company_id' => $company->id,
                'score' => $companyMatching->get('score'),
            ]);
        }

        return $tenderMatch;
    }
}
