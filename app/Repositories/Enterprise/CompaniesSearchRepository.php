<?php

namespace App\Repositories\Enterprise;

use App\Facades\SimilarityApi;
use App\Models\Company;
use Illuminate\Database\Eloquent\Builder;

class CompaniesSearchRepository
{
    public function recommended(): array
    {
        return (array)cache()->remember(
            'CompaniesSearch.Recommended',
            now()->addMinute(),
            fn() => self::retrieveAsCompanySearchItem(
                Company::where('is_vendor', 1)
                    ->whereHas('vendor.solutions')
                    ->inRandomOrder()
                    ->take(3)
            )
        );
    }


    // Retrieve data from cache if it exists for the same payload.
    // Otherwise, fetch matching vendors from the database.
    // If fewer than 10 matches are found, use the similarity API to get additional vendor names with a high similarity score and fetch them from the database.
    // Remove duplicates (if the same vendor is returned by both the database and the similarity API).
    // Cache the final response for future requests.
    /**
     * @throws \Exception
     */
    public function search(string $companyName): array
    {

        try {
            return cache()->remember(
                $this->getCompaniesCacheKey($companyName),
                now()->addMinutes((int)config('cache.cache_lifetime_company_search')),
                function () use ($companyName): array {
//                    $maxSize = 10;
                    $companies = $this->findCompaniesByName($companyName);

                    // TODO(Marian Rusnak): Replace similarity API with vector DB search.
//                    if (count($companies) >= $maxSize) {
//                        return $companies;
//                    }
//
//                    $alternativeNames = array_column(
//                        array_filter(
//                            SimilarityApi::alternatives($companyName),
//                            fn(array $alternativeCompany): bool => (float)$alternativeCompany['score'] >= 0.95
//                        ),
//                        'original_text'
//                    );
//
//                    $uniqueAlternativeNames = array_diff(
//                        $alternativeNames,
//                        array_column($companies, 'name')
//                    );
//
//                    $additionalCompanies = $this->findCompaniesByExactNames($uniqueAlternativeNames);
//
//                    if ($additionalCompanies) {
//                        $remainingSize = $maxSize - count($companies);
//                        $companies = array_merge($companies, array_splice($additionalCompanies, 0, $remainingSize));
//                    }

                    return $companies;
                }
            );
        } catch (\Exception $e) {
            throw new \Exception('An error occurred while searching for companies: ' . $e->getMessage());
        }
    }

    private function findCompaniesByName(string $name): array
    {
        return self::retrieveAsCompanySearchItem(
            Company::where('is_vendor', 1)
                ->where('name', 'like', "%{$name}%")
                ->take(10)
        );
    }

    private function findCompaniesByExactNames(array $names): ?array
    {
        if (empty($names)) {
            return null;
        }

        return self::retrieveAsCompanySearchItem(
            Company::where('is_vendor', 1)
                ->whereIn('name', $names)
        );
    }

    private function getCompaniesCacheKey(string $companyName): string
    {
        return "CompaniesSearch." . md5($companyName) . ".CompanyName";
    }

    private static function retrieveAsCompanySearchItem(Builder $companyQuery): array
    {
        return $companyQuery
            ->get(['public_id', 'name', 'country', 'hq'])
            ->map(fn(Company $company): array => [
                'id' => $company->public_id,
                'name' => $company->name,
                'region' => $company->country->transWithCity($company->hq),
            ])
            ->toArray();
    }
}
