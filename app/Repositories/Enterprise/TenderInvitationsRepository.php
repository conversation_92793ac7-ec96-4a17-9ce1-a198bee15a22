<?php

namespace App\Repositories\Enterprise;

use App\Enums\Enterprise\TenderMatchingCompaniesFilter;
use App\Enums\TenderCandidateStatus;
use App\Mail\Tenders\NewTenderAvailable;
use App\Models\Candidate;
use App\Models\Company;
use App\Models\Tender;
use App\Models\TenderMatchCompany;
use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Libs\Overseer\PublishStatus;

class TenderInvitationsRepository
{
    public function __construct(
        private NotificationService $notificationService,
    ) {}

    public function getInvitedCompanies(Tender $tender, User $user): Collection
    {
        $workspace = $user->workspaces()->firstOrFail();
        $workspaceCompanyIds = $workspace->companies()->pluck('id');
        $matchDetailsByCompany = $this->queryMatchedCompanies($tender)
            ->whereIn('company_id', $tender->invitedCompanies()->select('company_id'))
            ->get()
            ->keyBy('company_id');

        $candidatesByCompany = Candidate::query()
            ->with(['vendor'])
            ->whereHas('positions', function (Builder $query) use ($tender) {
                $query->where('tender_positions.tender_id', $tender->id)
                    ->whereNotIn('status', [
                        TenderCandidateStatus::AwaitingApproval, TenderCandidateStatus::Rejected
                    ]);
            })
            ->get()
            ->groupBy('vendor.company_id');

        return $tender->invitedCompanies()
            ->with([
                'vendor',
                'vendor.industries:name',
                'vendor.main_industry',
                'vendor.technologies:name',
                'vendor.clients',
                'vendor.employee_positions:name',
                'users',
            ])
            ->orderBy('name')
            ->get()
            ->map(function (Company $company) use ($workspaceCompanyIds, $matchDetailsByCompany, $candidatesByCompany) {
                return $this->decorateCompany(
                    $company,
                    isInWorkspace: $workspaceCompanyIds->contains($company->id),
                    matchDetails: $matchDetailsByCompany->get($company->id),
                    candidatesCount: $candidatesByCompany->get($company->id)?->count() ?? 0,
                );
            });
    }

    public function getSingleInvitedCompany(Tender $tender, User $user, int $companyId): Company
    {
        $company = $tender->invitedCompanies()->findOrFail($companyId);

        $workspace = $user->workspaces()->firstOrFail();
        $isInWorkspace = !!$workspace->companies()->find($companyId);
        $matchDetails = $this->queryMatchedCompanies($tender)
            ->where('company_id', $companyId)
            ->first();

        return $this->decorateCompany($company, $isInWorkspace, $matchDetails);
    }

    public function sendInvitations(Tender $tender): void
    {
        $tender->publish_status = PublishStatus::Published;
        $tender->save();

        $companiesToNotify = $tender->invitedCompanies()
            ->wherePivotNull('sent_at')
            ->get();

        foreach ($companiesToNotify as $company) {
            $tender->vendors()->attach($company->vendor->id, [
                'allowed' => true,
                'notified' => true,
            ]);
            $this->notificationService->notifyCompanyUsers($company, new NewTenderAvailable($tender));
            $company->invitation->update(['sent_at' => now()]);
        }
    }

    public function markAsViewedIfInvited(Tender $tender, User $user): void
    {
        $tenderInvitation = $tender->invitedCompanies()
            ->where('company_id', $user->company_id)
            ->first()
            ?->invitation;
        if ($tenderInvitation && !$tenderInvitation->first_viewed_at) {
            $tenderInvitation->update(['first_viewed_at' => now()]);
        }
    }

    /**
     * @param Tender $tender
     * @return object
     */
    private function queryMatchedCompanies(Tender $tender): Builder
    {
        $latestMatch = $tender->matches()
            ->where('companies_filter', TenderMatchingCompaniesFilter::Selection)
            ->latest()
            ->firstOrFail();

        return TenderMatchCompany::query()
            ->with(['tenderMatch', 'company'])
            ->where('match_id', $latestMatch->id);
    }

    private function decorateCompany(
        Company $company,
        bool $isInWorkspace,
        ?TenderMatchCompany $matchDetails,
        $candidatesCount = null
    ): Company
    {
        $company->is_in_workspace = $isInWorkspace;
        $company->match_details = $matchDetails;
        $company->candidates_count = $candidatesCount;
        return $company;
    }
}
