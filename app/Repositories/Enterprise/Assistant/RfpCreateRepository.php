<?php

namespace App\Repositories\Enterprise\Assistant;

use App\Facades\AssistantApi;
use App\Http\Resources\Enterprise\Assistant\AssistantApiRfpResource;
use App\Models\Industry;
use App\Models\Language;
use App\Models\Location;
use App\Models\Rfp;
use App\Models\RfpPosition;
use App\Models\Technology;
use Arr;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Facades\Storage;
use function json_encode;

#TODO: refactor

class RfpCreateRepository
{
    /**
     * @throws RequestException
     * @throws ConnectionException
     */
    public function extractInfo(Rfp $rfp): void
    {
        if ($rfp->rfp_api_called) {
            return;
        }

        $files = $rfp->files;
        $text = $this->filesToTextFromStorage($files ?? []);

        $response = AssistantApi::extractRfp($text);

        $rfp->update([
            'rfp_api_called' => true,
            'title' => $response->get('title'),
            'start_date' => data_get($response, 'timeline.start_date'),
            'end_date' => data_get($response, 'timeline.end_date'),
            'timezone' => $response->get('timezone'),
            'description' => data_get($response, 'project.detailed_description'),
        ]);

        $responsePrimaryIndustry = data_get($response, 'project.primary_industry');
        if ($responsePrimaryIndustry) {
            $primaryIndustry = Industry::where('id_string', $responsePrimaryIndustry)->first();
            if ($primaryIndustry) {
                $rfp->update([
                    'primary_industry_id' => $primaryIndustry->id,
                ]);
            }
        }

        $responseLocation = $response->get('location');
        if ($responseLocation) {
            $locationIds = Location::query()->whereIn('id_string', $responseLocation)->pluck('id');
            if ($locationIds->isNotEmpty()) {
                $rfp->locations()->sync($locationIds);
            }
        }
    }

    /**
     * @throws RequestException
     * @throws ConnectionException
     */
    public function extractResources(Rfp $rfp): void
    {
        if ($rfp->resources_extract_api_called) {
            return;
        }

        $files = $rfp->files;
        $text = $this->filesToTextFromStorage($files ?? []);

        $payload = sprintf(
            'ORIGINAL RFP: %s\n\nSTRUCTURED RFP: %s',
            $text,
            json_encode(
                Arr::except(
                    AssistantApiRfpResource::make($rfp)->resolve(),
                    ['project.description']
                )
            ),
        );

        $response = AssistantApi::extractResources($payload, $rfp->description);

        // Store the raw extract response
        $rfp->update([
            'resources_extract_api_response' => $response
        ]);

        // Process the response to create positions
        $this->saveExtractedRfpPositions($rfp, $response);
    }

    /**
     * @throws RequestException
     * @throws ConnectionException
     */
    public function suggestResources(Rfp $rfp): void
    {
        if (!$rfp->resources_extract_api_called || $rfp->resources_suggest_api_called) {
            return;
        }

        $files = $rfp->files;
        $text = $this->filesToTextFromStorage($files ?? []);

        $payload = sprintf(
            'ORIGINAL RFP: %s\n\nSTRUCTURED RFP: %s',
            $text,
            json_encode(
                Arr::except(
                    AssistantApiRfpResource::make($rfp)->resolve(),
                    ['project.description']
                )
            ),
        );

        $response = AssistantApi::suggestResources(
            $payload,
            $rfp->description,
            $rfp->resources_extract_api_response['resources'] ?? []
        );

        // Store the estimate response
        $rfp->update([
            'resources_suggest_api_called' => true,
            'resources_suggest_api_response' => $response
        ]);
    }

    private function filesToTextFromStorage(array $files): string
    {
        $parsedText = '';

        foreach ($files as $filePath) {
            if (Storage::disk('public')->exists($filePath)) {
                $filePathFull = Storage::disk('public')->path($filePath);
                if (file_exists($filePathFull)) {
                    $extension = pathinfo($filePath, PATHINFO_EXTENSION);
                    $parsedText .= $this->fileContentToText($filePathFull, $extension);
                }
            }
        }

        return $parsedText;
    }

    private function fileContentToText(string $filePath, string $extension): string
    {
        return match ($extension) {
            'txt' => file_get_contents($filePath),
            'pdf' => $this->extractTextUsingPython($filePath, 'pdf'),
            'xlsx' => $this->extractTextUsingPython($filePath, 'xlsx'),
            default => "[Unsupported file format: $extension]",
        };
    }

    private function extractTextUsingPython(string $filePath, string $extension): string
    {
        $result = Process::run('python3 ' . base_path("scripts/{$extension}-to-md.py") . ' ' . $filePath);

        if ($result->failed()) {
            throw new \RuntimeException($result->errorOutput());
        }

        return $result->output();
    }

    private function saveExtractedRfpPositions(Rfp $rfp, array $resourcesApiResponse): void
    {
        $resources = collect($resourcesApiResponse['resources'] ?? [])->recursive();

        in_transaction(function () use ($rfp, $resources) {
            foreach ($resources as $position) {
                /** @var Collection $position */
                $rfpPosition = RfpPosition::create([
                    'rfp_id' => $rfp->id,
                    'job_title' => $position->get('job_title'),
                    'number_of_resources' => $position->get('number_of_resources'),
                    'seniority_level' => $position->get('seniority_level'),
                    'rate_min' => data_get($position, 'hourly_rate_expectations.min'),
                    'rate_max' => data_get($position, 'hourly_rate_expectations.max'),
                    'workload' => $position->get('workload'),
                ]);

                // Process languages
                $languages = $position->get('languages');
                if ($languages?->isNotEmpty()) {
                    $languageIds = Language::query()
                        ->whereIn('id_string', $languages)
                        ->pluck('id');

                    if ($languageIds->isNotEmpty()) {
                        $rfpPosition->languages()->sync($languageIds);
                    }
                }

                // Process technologies
                $technologies = $position->get('technologies');
                if ($technologies?->isNotEmpty()) {
                    $technologyIds = Technology::query()
                        ->whereIn('uuid', $technologies)
                        ->pluck('id');

                    if ($technologyIds->isNotEmpty()) {
                        $rfpPosition->technologies()->syncWithPivotValues(
                            $technologyIds,
                            ['is_mandatory' => false]
                        );
                    }
                }
            }

            $rfp->update(['resources_extract_api_called' => true]);
        });
    }
}
