<?php

namespace App\Repositories\Enterprise\Assistant;

use App\Enums\LengthType;
use App\Enums\Seniority;
use App\Enums\ServiceType;
use App\Enums\TenderProcessingType;
use App\Enums\TenderStatus;
use App\Jobs\Enterprise\StoreTenderDescription;
use App\Models\Rfp;
use App\Models\Tender;
use App\Models\TenderPosition;
use App\Models\TenderProject;
use App\Repositories\Enterprise\Assistant\Exceptions\TenderExistsForRfpException;
use Libs\Overseer\PublishStatus;
use Libs\Warehouse\Warehouse;

class RfpTenderRepository
{
    /**
     * @throws TenderExistsForRfpException
     */
    public function __construct(
        private  Warehouse $warehouse,
    ){}

    public function createTenderFromRfp(Rfp $rfp): Tender
    {
        if ($rfp->tender()->exists()) {
            throw new TenderExistsForRfpException();
        }

        return in_transaction(function () use ($rfp): Tender {
            $tender = $this->createTender($rfp);

            $this->createTenderProject($rfp, $tender);
            $this->createTenderPositions($rfp, $tender);
            StoreTenderDescription::dispatch($rfp, $tender);

            return $tender;
        });
    }

    private function createTender(Rfp $rfp): Tender
    {
        $defaultTenderCoverPath = storage_path('app/default-tender-cover.png');
        $resource = $this->warehouse->store('tender_cover_image', $defaultTenderCoverPath);

        $company = $rfp->author->company;
        $tender = $rfp->tender()->create([
            'name' => $rfp->title,
            'description' => $rfp->description,
            'about' => $rfp->description,
            'company_id' => $company->id,
            'anonymous_company' => true,
            'publish_status' => PublishStatus::Draft,
            'status' => TenderStatus::Open,
            'submissions_deadline' => \now()->addDays(7),
            'processing_type' => TenderProcessingType::Immediate,
            'service_type' => ServiceType::Resources,
            'length_type' => LengthType::Unspecified,
            'cover_resource_id' => $resource->id,
            'country' => $company->country // TODO(Martin Majernicek): Use country from RFP
        ]);

        $rfp->tender()->associate($tender);
        $rfp->save();

        return $tender;
    }

    private function createTenderProject(Rfp $rfp, Tender $tender): void
    {
        $project = $tender->project()->create([
            'in_house' => true,
            'anonymous' => true,
            'name' => $rfp->title,
            'about' => $rfp->description,
            'main_industry_id' => $rfp->primary_industry_id,
        ]);
        $this->setTechnologiesForProject($rfp, $project);
    }

    private function setTechnologiesForProject(Rfp $rfp, TenderProject $project)
    {
        $technologies = $rfp->rfpPositions->pluck('technologies')->flatten()->unique();
        $tools = $rfp->rfpPositions->pluck('tools')->flatten()->unique();
        $technologiesAndTools = $technologies->merge($tools);
        $project->technologies()->sync($technologiesAndTools->pluck('id'));
    }

    private function createTenderPositions(Rfp $rfp, Tender $tender): void
    {
        foreach ($rfp->rfpPositions as $rfpPosition) {
            $technologiesAndTools = $rfpPosition->technologies->merge($rfpPosition->tools);

            $mandatoryTechnologies = $rfpPosition->technologies()->wherePivot('is_mandatory', true)->get();
            $mandatoryTools = $rfpPosition->tools()->wherePivot('is_mandatory', true)->get();
            $mandatoryTechnologiesAndTools = $mandatoryTechnologies->merge($mandatoryTools);
            $requirements = $technologiesAndTools->pluck('name')->join(', ');
            $notes = !empty($rfpPosition->notes) ? trim((string) $rfpPosition->notes) : '';

            if ($notes !== '') {
                $requirements = $requirements
                    ? $requirements . ' | Notes: ' . $notes
                    : 'Notes: ' . $notes;
            }

            $tenderPosition = $tender->positions()->create([
                'name' => $rfpPosition->job_title,
                'description' => $rfpPosition->job_title,
                'requirements' => $requirements,
                'must_have_requirements' => $mandatoryTechnologiesAndTools->pluck('name')->join(', '),
                'price' => $rfpPosition->rate_min,
                'price_to' => $rfpPosition->rate_max,
                'start_date' => $rfp->start_date,
                'length_type' => LengthType::Months,
                'length' => (int)$rfp->start_date->diffInMonths($rfp->end_date, true),
                'possible_extension' => 1,
                'workload' => round(TenderPosition::FULL_WORKLOAD * $rfpPosition->workload / 100),
                'work_location' => $rfpPosition->work_location,
                'count' => $rfpPosition->number_of_resources,
            ]);

            if (!empty($rfpPosition->seniority_level)) {
                $tenderPosition->seniorities()->create([
                    'seniority' => Seniority::from($rfpPosition->seniority_level->value),
                ]);
            }

            $tenderPosition->technologies()->sync($technologiesAndTools->pluck('id'));

            foreach ($mandatoryTechnologiesAndTools->pluck('id') as $mandatoryTechnologyId) {
                $tenderPosition->technologies()->updateExistingPivot($mandatoryTechnologyId, [
                    'is_mandatory' => true,
                ]);
            }
        }
    }
}
