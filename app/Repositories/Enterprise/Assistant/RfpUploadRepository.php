<?php

namespace App\Repositories\Enterprise\Assistant;

use App\Enums\Enterprise\AssistantStep;
use App\Enums\Enterprise\IndustryType;
use App\Enums\Enterprise\TechnologyType;
use App\Http\Requests\Enterprise\Assistant\RfpUpdateRequest;
use App\Http\Requests\Enterprise\Assistant\RfpUploadRequest;
use App\Http\Resources\Enterprise\Assistant\RfpInfoFormResource;
use App\Http\Resources\Enterprise\Assistant\RfpResourcesFormResource;
use App\Models\Industry;
use App\Models\Language;
use App\Models\Location;
use App\Models\Rfp;
use App\Models\Technology;
use App\Repositories\Enterprise\Assistant\Exceptions\TenderExistsForRfpException;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class RfpUploadRepository
{
    public function storeRfp(RfpUploadRequest $request): Rfp
    {
        $filePaths = [];

        if ($request->hasFile('file')) {
            foreach ($request->file('file') as $file) {
                $safeFileName = time() . '_' . Str::slug(pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME), '_') . '.' . $file->getClientOriginalExtension();
                $filePaths[] = $file->storeAs('rfps', $safeFileName, 'public');
            }
        }


        $text = $request->input('text');
        if (!empty($text)) {
            $pdfFileName = time() . "_rfp.pdf";
            $pdfFilePath = "rfps/{$pdfFileName}";
            Storage::disk('public')->put($pdfFilePath, Pdf::loadHTML('<p>' . e($text) . '</p>')->output());
            $filePaths[] = $pdfFilePath;
        }

        return Rfp::create([
            'text' => $text,
            'files' => $filePaths,
            'author_id' => $request->user()->id,
            'step' => AssistantStep::Info
        ]);
    }

    public function updateRfp(RfpUpdateRequest $request, Rfp $rfp): array
    {
        return in_transaction(function () use ($request, $rfp): array {
            if ($rfp->tender()->exists()) {
                throw new TenderExistsForRfpException();
            }

            $rfpData = $request->input('info', []);

            if ($request->has('info.timeline.start_date')) {
                $rfpData['start_date'] = $request->input('info.timeline.start_date');
            }

            if ($request->has('info.timeline.end_date')) {
                $rfpData['end_date'] = $request->input('info.timeline.end_date');
            }

            if ($request->has('info.primary_industry')) {
                $industry = Industry::where('id_string', $request->input('info.primary_industry'))
                    ->where('type', IndustryType::Primary)
                    ->first();
                $rfpData['primary_industry_id'] = $industry?->id ?? null;
            }

            if (!empty($rfpData)) {
                $rfp->update($rfpData);
            }

            if ($request->has('info.location')) {
                $locationIds = Location::whereIn('id_string', $request->input('info.location'))->pluck('id')->toArray();
                $rfp->locations()->sync($locationIds);
            }


            if ($request->has('resources')) {
                $receivedIds = collect($request->input('resources'))->pluck('id')->filter()->toArray();
                $rfp->rfpPositions()->whereNotIn('id', $receivedIds)->delete();

                foreach ($request->input('resources') as $resource) {
                    $this->updateOrCreateRfpPosition($rfp, $resource);
                }
            }

            $response = [];
            if ($request->has('info')) {
                $response['info'] = new RfpInfoFormResource($rfp);
            }
            if ($request->has('resources')) {
                $response['resources'] = new RfpResourcesFormResource($rfp);
            }
            return $response;
        });
    }

    private function updateOrCreateRfpPosition(Rfp $rfp, array $resource): void
    {
        $positionId = $resource['id'] ?? null;

        $positionData = array_filter([
            'job_title' => $resource['job_title'] ?? null,
            'number_of_resources' => $resource['number_of_resources'] ?? null,
            'seniority_level' => $resource['seniority_level'] ?? null,
            'workload' => $resource['workload'] ?? null,
            'rate_min' => $resource['hourly_rate_expectations'][0] ?? null,
            'rate_max' => $resource['hourly_rate_expectations'][1] ?? null,
            'work_location' => $resource['work_location'] ?? null,
            'notes' => $resource['notes'] ?? null,
        ], fn($value) => !is_null($value));

        $position = $rfp->rfpPositions()->updateOrCreate(['id' => $positionId], $positionData);

        if (array_key_exists('languages', $resource)) {
            $languageIds = Language::whereIn('id_string', $resource['languages'])->pluck('id')->toArray();
            $position->languages()->sync($languageIds);
        }

        $techIds = [];
        $toolIds = [];

        if (array_key_exists('technologies', $resource)) {
            $techIds = Technology::whereIn('uuid', $resource['technologies'])
                ->where('type', TechnologyType::Technology)
                ->pluck('id')
                ->toArray();
        }

        if (array_key_exists('key_tools', $resource)) {
            $toolIds = Technology::whereIn('uuid', $resource['key_tools'])
                ->where('type', TechnologyType::Tool)
                ->pluck('id')
                ->toArray();
        }

        $position->technologies()->syncWithPivotValues(
            array_merge($techIds, $toolIds),
            ['is_mandatory' => false]
        );

        if (array_key_exists('mandatory_technology', $resource)) {
            $mandatoryTechId = Technology::where('uuid', $resource['mandatory_technology'])
                ->where('type', TechnologyType::Technology)
                ->value('id');

            $position->technologies()->updateExistingPivot($mandatoryTechId, ['is_mandatory' => true]);
        }
    }
}
