<?php

namespace App\Repositories\Enterprise\Assistant;

use App\Models\Rfp;
use App\Repositories\Enterprise\Assistant\Exceptions\TenderExistsForRfpException;

class RfpDeleteRepository
{
    public function delete(Rfp $rfp): void
    {
        in_transaction(function () use ($rfp) {
            if ($rfp->tender()->exists()) {
                throw new TenderExistsForRfpException();
            }
            $rfp->delete();
        });
    }
}
