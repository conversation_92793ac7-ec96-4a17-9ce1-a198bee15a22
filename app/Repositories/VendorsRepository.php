<?php

namespace App\Repositories;

use App\Enums\UserRole;
use App\Mail\Vendors\NewVendorApplication;
use App\Models\Vendor;
use App\Services\NotificationService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Libs\Overseer\ChangesApproved;
use Libs\Overseer\ChangesRejected;
use Libs\Overseer\NoPendingChangesException;
use Libs\Overseer\RequestedChange;
use Libs\Overseer\ReviewStatus;

readonly class VendorsRepository
{
    public function __construct(
        private CompaniesRepository $companiesRepository,
        private NotificationService $notificationService,
    ) {}

    /**
     * @return Collection<Vendor>
     */
    public function all(): Collection
    {
        return $this->query()->get();
    }

    public function query(): Builder
    {
        return Vendor::query();
    }

    public function ofExistingCompany(): Builder
    {
        return $this->query()->whereHas('company');
    }

    public function findBySlugOrFail(string $slug): Vendor
    {
        return $this->ofExistingCompany()
            ->with('company', 'industries', 'technologies', 'clients.client', 'employee_positions')
            ->findBySlugOrFail($slug);
    }

    public function findByCompanySlugOrFail(string $slug): Vendor
    {
        /** Always returns Vendor @noinspection PhpIncompatibleReturnTypeInspection */
        return $this->query()
            ->with(
                'company', 'industries', 'technologies', 'clients.client', 'employee_positions',
                'unapproved_change.industries', 'unapproved_change.technologies', 'unapproved_change.clients.client',
                'unapproved_change.employee_positions',
            )
            ->whereHas('company', fn ($subQuery) => $subQuery->slug($slug))
            ->firstOrFail();
    }

    public function published(): Builder
    {
        return $this->ofExistingCompany()->published();
    }

    public function publishedPublic(): Builder
    {
        return $this->published()->public();
    }

    public function publishedPublicOrOwn(): Builder
    {
        return $this->published()->where(function (Builder $query) {
            $user = auth()->user();

            if (! UserRole::SuperAdmin->has($user)) {
                $query->public();
            }
            if (UserRole::Vendor->has($user) && $user->company_id) {
                $query->orWhere('company_id', $user->company_id);
            }
        });
    }

    public function update(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            $company = $this->companiesRepository->findBySlugOrFail($slug);

            $vendor = $company->vendor
                ? tap($company->vendor->fill($data)->asPublished())->save()
                : $company->vendor()->create($data);

            $this->syncVendorRelationships($vendor, $data);
        });
    }

    public function updateOwn(array $data): ReviewStatus
    {
        return in_transaction(function () use ($data) {
            $company = auth()->user()->company;

            /** @var Vendor $vendor */
            $vendor = $this->query()
                ->ofCompany($company->id)
                ->first();

            $alreadyAwaitingApproval = $vendor && ($vendor->is_awaiting_approval || $vendor->unapproved_change);

            // First time creation & updates of it.
            if (! $vendor || $vendor->is_awaiting_approval) {
                $new = ! $vendor;
                $vendor = $vendor ?? $company->vendor()->create($data);

                $vendor->awaitApproval()->save();
                $this->syncVendorRelationships($vendor, $data);

                $status = ReviewStatus::Accepted;
            } else {
                $relationships = [
                    'industries' => collect($data['industries']),
                    'technologies' => collect($data['technologies']),
                    'clients' => collect($data['clients'])->map(
                        fn ($client) => [
                            'client_id' => $client['id'] ?? null,
                            'name' => ($client['id'] ?? null) ? null : $client['name'],
                        ],
                    ),
                    'employee_positions' => collect($data['employee_positions'] ?? []),
                ];

                $status = $vendor->requestReview($data, $relationships);
            }

            if ($status === ReviewStatus::Accepted && ! $vendor->is_draft && ! $alreadyAwaitingApproval) {
                $mail = ($new ?? false)
                    ? new NewVendorApplication($company)
                    : new RequestedChange($company, $vendor);
                $this->notificationService->notifyStaff($mail);
            }

            return $status;
        });
    }

    public function discardOwnUnapprovedChange(): void
    {
        in_transaction(fn () => $this->query()
            ->ofCompany(auth()->user()->company_id)
            ->firstOrFail()
            ->unapproved_change()
            ->delete()
        );
    }

    /**
     * @throws NoPendingChangesException
     */
    public function approveChange(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            /** @var Vendor $vendor */
            $vendor = $this->query()
                ->ofCompany($slug)
                ->firstOrFail();

            $isNewRegistration = $vendor->is_awaiting_approval;

            $unapprovedChange = $vendor->is_awaiting_approval
                ? $vendor
                : $vendor->unapproved_change;

            if (! $unapprovedChange) {
                throw new NoPendingChangesException($vendor);
            }

            $unapprovedChange->fill($data);
            $this->syncVendorRelationships($unapprovedChange, $data);
            $unapprovedChange->publish();

            if ($isNewRegistration) {
                /** @noinspection PhpUnhandledExceptionInspection this is never thrown as profile was just created */
                $this->companiesRepository->updateIsVendor($vendor->company->slug, true);
            }

            $this->notificationService->notifyCompanyUsers($vendor->company, new ChangesApproved($vendor));
        });
    }

    public function rejectUnapprovedChange(string $slug): void
    {
        in_transaction(function () use ($slug) {
            /** @var Vendor $vendor */
            $vendor = $this->query()
                ->ofCompany($slug)
                ->firstOrFail();

            if ($vendor->is_awaiting_approval) {
                $vendor->delete();
            } else {
                $vendor->unapproved_change()->delete();
            }

            $this->notificationService->notifyCompanyUsers($vendor->company, new ChangesRejected($vendor));
        });
    }

    private function syncVendorRelationships(Vendor $vendor, array $data): void
    {
        $vendor->industries()->sync(collect($data['industries']));
        $vendor->technologies()->sync(collect($data['technologies']));

        $clients = collect($data['clients'])->map(
            fn ($client) => [
                'client_id' => $client['id'] ?? null,
                'name' => ($client['id'] ?? null) ? null : $client['name'],
            ],
        );
        $vendor->clients()->delete();
        $vendor->clients()->createMany($clients);

        $vendor->employee_positions()->sync(collect($data['employee_positions'] ?? []));
    }
}
