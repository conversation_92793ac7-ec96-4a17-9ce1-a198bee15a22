<?php

namespace App\Repositories;

use App\Models\Technology;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Libs\Warehouse\Warehouse;

class TechnologiesRepository
{
    public function __construct(
        private Warehouse $warehouse,
    ) {}

    /**
     * @return Collection<Technology>
     */
    public function all(): Collection
    {
        return $this->query()->get();
    }

    /**
     * @return Collection<Technology>
     */
    public function allByEmsiId(): Collection
    {
        return $this->query()
            ->whereNotNull('emsi_id')
            ->get()
            ->keyBy('emsi_id');
    }

    public function query(): Builder
    {
        return Technology::query();
    }

    public function findBySlugOrFail(string $slug): Technology
    {
        return $this->query()
            ->with('parent')
            ->findBySlugOrFail($slug);
    }

    public function store(array $data): Technology
    {
        return in_transaction(function () use ($data) {
            $data = $this->privatizeResourcesId($data);

            return Technology::create($data);
        });
    }

    public function update(string $slug, array $data): void
    {
        in_transaction(function () use ($slug, $data) {
            /** @var Technology $technology */
            $technology = $this->query()->findBySlugOrFail($slug);
            $data = $this->privatizeResourcesId($data);

            $technology->fill($data);
            $technology->save();
        });
    }

    public function destroy(string $slug): void
    {
        /** @var Technology $technology */
        $technology = $this->query()->findBySlugOrFail($slug);
        $technology->delete();
    }

    private function privatizeResourcesId(array $data): array
    {
        if ($data['logo_resource_id'] ?? false) {
            $logoResource = $this->warehouse->find($data['logo_resource_id']);
            $data['logo_resource_id'] = $logoResource->id;
        }

        return $data;
    }
}
