<?php

use App\Exceptions\BrokenCircuitException;
use Carbon\CarbonInterval;
use Illuminate\Cache\RateLimiter;

/**
 * Helper function to help prevent unwanted execution of a given action if it keeps failing.
 * Example usage includes invoking remote service that may be unavailable.
 *
 * @param  string  $id  identifier of the action
 * @param  Closure  $action  action to be executed
 * @param  CarbonInterval|null  $decay  expiration time of failed attempt [default: 5 minutes]
 * @param  int  $threshold  how many failed attempts before breaking the circuit [default: 5]
 * @return mixed whatever action returns
 *
 * @throws BrokenCircuitException
 */
function circuit_breaker(string $id, Closure $action, ?CarbonInterval $decay = null, int $threshold = 5): mixed
{
    /** @var RateLimiter $limiter */
    $limiter = app(RateLimiter::class);
    $decay = $decay ?: CarbonInterval::minutes(5);

    try {
        if ($limiter->tooManyAttempts($id, $threshold)) {
            throw new BrokenCircuitException($id, $decay);
        }

        return $action();
    } catch (Exception $exception) {
        $limiter->hit($id, now()->add($decay));
        throw $exception;
    }
}
