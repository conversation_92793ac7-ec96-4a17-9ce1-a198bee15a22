<?php

/**
 * Source: https://www.php.net/manual/en/function.array-merge-recursive.php#92195
 */
if (! function_exists('array_merge_recursive_distinct')) {
    function array_merge_recursive_distinct(array &$first, array &$second): array
    {
        $merged = $first;

        foreach ($second as $key => &$value) {
            if (is_array($value) && isset($merged[$key]) && is_array($merged[$key])) {
                $merged[$key] = array_merge_recursive_distinct($merged[$key], $value);
            } else {
                $merged[$key] = $value;
            }
        }

        return $merged;
    }
}
