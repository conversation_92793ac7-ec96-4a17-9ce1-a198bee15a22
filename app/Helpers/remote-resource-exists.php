<?php

/**
 * WARNING: the only way for server to see if a given page is 2xx
 * or not is to physically visit given URL. Because of that, be
 * careful what address you are passing to the function!
 * Read more: https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29/
 *
 * @param  string  $url  URL of the resource to be checked
 * @return bool true if page returns 200 status,
 *              false otherwise
 */
function remote_resource_exists(string $url): bool
{
    [$protocolAndStatus] = @get_headers($url);
    [, $statusCode] = explode(' ', $protocolAndStatus);

    return ((int) ($statusCode / 100)) === 2;
}
