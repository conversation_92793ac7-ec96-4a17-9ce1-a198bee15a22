<?php

use Symfony\Component\HtmlSanitizer\HtmlSanitizer;

/**
 * Escapes dangerous HTML tags and leaves only selected ones
 *
 * @param  ?string  $html  text containing HTML
 * @return ?string escaped text
 */
function sanitize_html(?string $html): ?string
{
    if (! $html) {
        return $html;
    }

    /** @var HtmlSanitizer $sanitizer */
    $sanitizer = app('sanitizer.default');

    return $sanitizer->sanitize($html);
}
