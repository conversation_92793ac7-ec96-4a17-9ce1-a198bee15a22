<?php

/**
 * Helper to translate numbers into more readable format.
 *
 * E.g. 1 234 567 => 1.2 mil. | 1 million 234 thousands
 *
 * @param  int|float  $number  any number
 * @param  bool  $short  whether format is short instead of long [default: true]
 */
function format_large_number(int|float $number, bool $short = true): string
{
    // FIXME: rewrite to config driven approach!
    // FIXME: refactor completely as this was written for a different purpose originally
    //  for this application, we don't need to support decimal points at all
    if ($short) {
        if ($number / 1_000_000_000 >= 1) {
            $base = $number / 1_000_000_000;
            $suffix = 'billions';
        } elseif ($number / 1_000_000 >= 1) {
            $base = $number / 1_000_000;
            $suffix = 'millions';
        } else {
            $base = intval($number / 1000);
            $suffix = 'thousands';
        }

        return format_money($base, 0).trans_choice("global.numbers.abv.{$suffix}", $base);
    } else {
        $parts = [];
        $rootLang = 'global.numbers.';

        if ($number / 1_000_000_000 >= 1) {
            $base = intval($number / 1_000_000_000);
            $number -= ($base * $number);
            $parts[] = $base.' '.trans_choice($rootLang.'billions', $base);
        }

        if ($number / 1_000_000 >= 1) {
            $base = intval($number / 1_000_000);
            $number -= ($base * $number);
            $parts[] = $base.' '.trans_choice($rootLang.'millions', $base);
        }

        if ($number / 1000 >= 1) {
            $base = intval($number / 1000);
            $number -= ($base * $number);
            $parts[] = $base.' '.trans_choice($rootLang.'thousands', $base);
        }

        return implode(' ', $parts);
    }
}
