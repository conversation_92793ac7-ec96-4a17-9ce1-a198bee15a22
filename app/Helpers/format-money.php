<?php

/**
 * Converts money value to human-friendly format
 *
 * @param  int  $dec  [default: 2]
 * @param  ?string  $locale  [default: null]
 */
function format_money(float $money, int $dec = 2, ?string $locale = null): string
{
    $decPoint = __('global.money.dec_point', [], $locale);
    $thousandsSep = __('global.money.thousands_sep', [], $locale);

    return number_format($money, $dec, $decPoint, $thousandsSep);
}
