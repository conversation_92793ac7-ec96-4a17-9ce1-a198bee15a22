<?php

use Illuminate\Support\Facades\DB;

/**
 * Convenient method for running a block of code in transaction.
 *
 * @param  Closure  $callback  block of code to be run in transaction.
 * @param  int  $attempts  number of attempts to perform if transaction fails.
 *                         [default: 1]
 * @return mixed execution result
 */
function in_transaction(Closure $callback, int $attempts = 1): mixed
{
    return DB::transaction($callback, $attempts);
}
