<?php

use Illuminate\Validation\Rules\Password;

function password_rules(string $requiredRules = 'required'): array
{
    $passwordRules = Password::min(10)
        ->numbers()
        ->letters()
        ->mixedCase();

    // Ensure that rule to check uncompromised passwords will
    // only be applied in production environments
    if (app()->isProduction()) {
        $passwordRules = $passwordRules->uncompromised();
    }

    return [
        $requiredRules,
        'string',
        'confirmed',
        $passwordRules,
        'max:100',
    ];
}
