<?php

namespace App\Jobs\Enterprise;

use App\Data\Enterprise\CompanyImportDto;
use App\Enums\Enterprise\CompanyImportStatus;
use App\Enums\Enterprise\SimilarityApiCollection;
use App\Events\CompanyImportStatusUpdated;
use App\Facades\SimilarityApi;
use App\Models\Company;
use App\Repositories\CompaniesRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class ProcessCompanyImport implements ShouldQueue
{
    use Queueable;

    protected ?Company $company = null; // can be null
    protected CompaniesRepository $companiesRepository;

    public function __construct(
        protected CompanyImportDto $companyImportDto
    ) {}

    public function handle(CompaniesRepository $companiesRepository): void
    {
        // maybe the whole logic should be in VendorRepository

        $this->companiesRepository = $companiesRepository;
        // 1. condition, SQL
        $this->company = $this->companiesRepository->findVendorsByName($this->companyImportDto->name, ['public_id', 'name']);

        // 2. condition, Similarity Api
        $this->company ??= $this->findCompanyViaApi();

        // Correct formatting
        $this->finalizeVendorStatus();

        // Fire to FE
        $this->storeInCache($this->companyImportDto);
    }

    protected function findCompanyViaApi(): ?Company
    {
        $results = SimilarityApi::search([$this->companyImportDto->name], SimilarityApiCollection::Companies);

        if (!empty($results) && !empty($results[0]) && !empty($results[0][0])) {
            $similarCompanies = $results[0];
            foreach ($similarCompanies as $alternative) {
                $similarCompany = $this->companiesRepository->findVendorsByName($alternative['payload']['name'], ['public_id', 'name']);
                if ($similarCompany) {
                    if ((float)$alternative['score'] >= 0.95) {
                        $this->companyImportDto->alternatives = [];
                        return $similarCompany;
                    } elseif ((float)$alternative['score'] >= 0.6) {
                        $this->companyImportDto->alternatives[] = [
                            'name' => $similarCompany->name,
                            'company_id' => $similarCompany->public_id,
                        ];
                    }
                }

            }
        }

        // not found
        return null;
    }

    protected function finalizeVendorStatus(): void
    {

        if ($this->company) {
            // we have match, so Company is verified
            $this->companyImportDto->name = $this->company->name;
            $this->companyImportDto->status = CompanyImportStatus::Verified;
            $this->companyImportDto->company_id = $this->company->public_id;

            // cleanup, we dont need this.
            $this->companyImportDto->alternatives = [];

        } elseif (!empty($this->companyImportDto->alternatives)) {
            // we didnt find Company, but we got alternatives from SimilarityApi
            $this->companyImportDto->status = CompanyImportStatus::Unverified;

        } else {
            // Company was not found and alternatives are empty
            $this->companyImportDto->status = CompanyImportStatus::Unverified;
        }
    }

    private function storeInCache(CompanyImportDto $companyImportDto): void
    {
        cache()->put('company-imports.' . $companyImportDto->import_id, $companyImportDto, now()->addMinutes(15));
    }
}
