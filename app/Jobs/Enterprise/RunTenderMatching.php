<?php

namespace App\Jobs\Enterprise;

use App\Enums\Enterprise\TenderMatchingCompaniesFilter;
use App\Models\Tender;
use App\Models\Workspace;
use App\Repositories\Enterprise\TenderMatchingRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Log;

class RunTenderMatching implements ShouldQueue
{
    use Queueable;

    protected TenderMatchingRepository $tenderMatchingRepository;

    public function __construct(
        protected Tender $tender,
        protected Workspace $workspace,
        protected TenderMatchingCompaniesFilter $companiesFilter,
    ) {}

    public function handle(TenderMatchingRepository $tenderMatchingRepository): void
    {
        if ($this->companiesFilter === TenderMatchingCompaniesFilter::Marketplace && $this->workspace->marketplace_disabled) {
            return;
        }
        $this->tenderMatchingRepository = $tenderMatchingRepository;
        try {
            $this->tenderMatchingRepository->runMatching($this->tender, $this->workspace, $this->companiesFilter);
        } catch (\Exception $e) {
            Log::error('Tender matching failed', [
                'tender_id' => $this->tender->id,
                'companies_filter' => $this->companiesFilter,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }
}
