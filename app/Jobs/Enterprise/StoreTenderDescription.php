<?php

namespace App\Jobs\Enterprise;

use App\Facades\AssistantApi;
use App\Http\Resources\Enterprise\Assistant\AssistantApiRfpResource;
use App\Models\Rfp;
use App\Models\Tender;
use Arr;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\RequestException;
use function json_encode;

class StoreTenderDescription implements ShouldDispatchAfterCommit
{
    use Dispatchable;

    public function __construct(
        public Rfp $rfp,
        public Tender $tender
    ) {}

    /**
     * @throws RequestException
     * @throws ConnectionException
     */
    public function handle(): void
    {
        if ($this->tender->matching_description) {
            return;
        }

        $description = AssistantApi::description(
            json_encode(
                Arr::except(
                    AssistantApiRfpResource::make($this->rfp)->resolve(),
                    ['project.description']
                )
            ),
            $this->tender->description
        );

        $this->tender->update([
            'matching_description' => $description,
        ]);
    }
}
