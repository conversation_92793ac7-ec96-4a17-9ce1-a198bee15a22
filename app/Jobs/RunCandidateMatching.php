<?php

namespace App\Jobs;

use App\Enums\Enterprise\TenderMatchingCompaniesFilter;
use App\Models\Candidate;
use App\Models\Tender;
use App\Models\TenderPosition;
use App\Models\Workspace;
use App\Repositories\Enterprise\TenderMatchingRepository;
use App\Repositories\TenderPositionsRepository;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Log;

class RunCandidateMatching implements ShouldQueue
{
    use Queueable;

    public function __construct(
        protected TenderPosition $tenderPosition,
        protected Candidate $candidate,
    ) {}

    public function handle(TenderPositionsRepository $tenderPositionsRepository): void
    {
        try {
            $tenderPositionsRepository->runCandidateMatching($this->tenderPosition, $this->candidate);
        } catch (\Exception $e) {
            Log::error('Candidate matching failed', [
                'tender_position_id' => $this->tenderPosition->id,
                'candidate_id' => $this->candidate->id,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }
}
