<?php

namespace App\Jobs;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendCompany implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public readonly int    $id,
        public readonly string $name,
        public readonly ?string $hq,
        public readonly ?string $country,
        public readonly ?string $website,
        public readonly ?string $linkedin,
        public readonly ?string $about,
        public readonly ?string $notes,
        public readonly ?string $presentation_url,
    )
    {
        $this->onConnection('queue-companies');
        $this->onQueue('vendors');
    }
}
