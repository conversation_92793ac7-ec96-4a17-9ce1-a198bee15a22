<?php

namespace App\Drivers\Solutions\Media;

use App\Models\SolutionMedium;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\JsonResources\ImageResource;
use Libs\Warehouse\Warehouse;

class ImageMediumDriver implements MediumDriver
{
    public function __construct(
        private Warehouse $warehouse,
    ) {}

    public function save(SolutionMedium $medium, mixed $source): void
    {
        $medium->resource()->associate($this->warehouse->find($source));
    }

    public function get(SolutionMedium $medium): string|JsonResource
    {
        return ImageResource::main($medium->resource);
    }

    public function getCover(SolutionMedium $medium): string|JsonResource
    {
        return ImageResource::thumbnail($medium->resource, 'carousel_thumbnail');
    }
}
