<?php

namespace App\Drivers\Solutions\Media;

use App\Models\SolutionMedium;
use Illuminate\Http\Resources\Json\JsonResource;
use InvalidArgumentException;
use Libs\Warehouse\JsonResources\ImageResource;
use Libs\Warehouse\Warehouse;

class StreamableMediumDriver implements MediumDriver
{
    public function __construct(
        private Warehouse $warehouse,
    ) {}

    public function save(SolutionMedium $medium, mixed $source): void
    {
        if (! is_string($source)) {
            throw new InvalidArgumentException('Source of Streamable solution medium is not valid!');
        }

        $medium->source = $source;

        $resource = $this->warehouse->store('solution_medium_carousel_thumbnail_image', $this->thumbnailUrl($source));
        $medium->resource()->associate($resource);
    }

    public function get(SolutionMedium $medium): string
    {
        return $medium->getRawOriginal('source');
    }

    public function getCover(SolutionMedium $medium): string|JsonResource
    {
        return ImageResource::main($medium->resource);
    }

    private function thumbnailUrl(string $source): string
    {
        $id = urlencode($source);

        return "https://cdn-cf-east.streamable.com/image/$id.jpg";
    }
}
