<?php

namespace App\Drivers\Solutions\Media;

use App\Models\SolutionMedium;
use Illuminate\Http\Resources\Json\JsonResource;
use InvalidArgumentException;
use Libs\Warehouse\JsonResources\ImageResource;
use Libs\Warehouse\Warehouse;

class YouTubeMediumDriver implements MediumDriver
{
    public function __construct(
        private Warehouse $warehouse,
    ) {}

    public function save(SolutionMedium $medium, mixed $source): void
    {
        if (! is_string($source)) {
            throw new InvalidArgumentException('Source of YouTube solution medium is not valid!');
        }

        $medium->source = $source;

        $resource = $this->warehouse->store('solution_medium_carousel_thumbnail_image', $this->thumbnailUrl($source));
        $medium->resource()->associate($resource);
    }

    public function get(SolutionMedium $medium): string
    {
        return $medium->getRawOriginal('source');
    }

    public function getCover(SolutionMedium $medium): string|JsonResource
    {
        return ImageResource::main($medium->resource);
    }

    private function thumbnailUrl(string $source): string
    {
        $id = urlencode($source);
        $maxresurl = "https://img.youtube.com/vi/$id/maxresdefault.jpg";

        if (remote_resource_exists($maxresurl)) {
            return $maxresurl;
        }

        return "https://img.youtube.com/vi/$id/hqdefault.jpg";
    }
}
