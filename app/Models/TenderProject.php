<?php

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $tender_id
 * @property Tender $tender
 * @property bool $anonymous
 * @property string $name
 * @property string $about
 * @property string $escaped_about
 * @property bool $in_house
 * @property ?int $main_industry_id
 * @property ?Industry $main_industry
 * @property EloquentCollection $industries
 * @property EloquentCollection $technologies
 * @property ?int $client_id
 * @property ?Client $client
 * @property ?string $client_name
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class TenderProject extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'main_industry_id',
        'anonymous',
        'name',
        'about',
        'in_house',
        'client_id',
        'client_name',
    ];

    protected $casts = [
        'anonymous' => 'boolean',
        'in_house' => 'boolean',
    ];

    protected function escapedAbout(): Attribute
    {
        return Attribute::get(fn () => sanitize_html($this->about));
    }

    public function main_industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class);
    }

    public function industries(): BelongsToMany
    {
        return $this->belongsToMany(Industry::class);
    }

    public function technologies(): BelongsToMany
    {
        return $this->belongsToMany(Technology::class, 'tender_project_technology');
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }
}
