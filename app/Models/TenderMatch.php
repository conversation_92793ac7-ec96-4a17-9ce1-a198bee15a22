<?php

namespace App\Models;

use App\Enums\Enterprise\TenderMatchingCompaniesFilter;

use Illuminate\Database\Eloquent\Casts\AsCollection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class TenderMatch extends Model
{
    use HasUuids;

    protected $fillable = [
        'companies_filter',
        'matching_api_payload',
        'matching_api_response',
        'matching_api_response_status',
    ];

    protected $casts = [
        'companies_filter' => TenderMatchingCompaniesFilter::class,
        'matching_api_payload' => 'array',
        'matching_api_response' => AsCollection::class,
    ];

    public function tender(): BelongsTo
    {
        return $this->belongsTo(Tender::class);
    }

    public function matchedCompanies(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'tender_match_company', 'match_id', 'company_id')
            ->using(TenderMatchCompany::class)
            ->withPivot('score')
            ->orderByPivot('score', 'desc');
    }
}
