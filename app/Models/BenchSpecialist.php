<?php

namespace App\Models;

use App\Models\Traits\AutoSlugTrait;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Libs\Overseer\PublishStatus;
use Libs\Overseer\Reviewable;

/**
 * @property int $id
 * @property string $public_id
 * @property int $candidate_id
 * @property Candidate $candidate
 * @property PublishStatus $publish_status
 * @property Vendor $merge_into
 * @property Vendor $unapproved_change
 * @property CarbonInterface $available_from
 * @property CarbonInterface $available_to
 * @property EloquentCollection $interested_companies
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 */
class BenchSpecialist extends Model
{
    use AutoSlugTrait;
    use HasFactory;
    use Reviewable;
    use SoftDeletes;

    protected $fillable = [
        'available_from',
        'available_to',
    ];

    protected $attributes = [
        'publish_status' => PublishStatus::AwaitingApproval,
    ];

    protected $casts = [
        'publish_status' => PublishStatus::class,
        'available_from' => 'datetime',
        'available_to' => 'datetime',
    ];

    protected string $slugIdField = 'public_id';

    protected string $slugSourceField = 'candidate.name';

    public function candidate(): BelongsTo
    {
        return $this->belongsTo(Candidate::class);
    }

    public function interested_companies(): BelongsToMany
    {
        return $this->belongsToMany(Company::class)
            ->using(BenchSpecialistCompany::class)
            ->withPivot('status', 'rejection_reason', 'client_note');
    }

    protected function name(): Attribute
    {
        return Attribute::get(fn () => $this->candidate->name);
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->where('available_to', '>', now());
    }

    public function scopeActivePublished(Builder $query): Builder
    {
        return $query->active()->published();
    }
}
