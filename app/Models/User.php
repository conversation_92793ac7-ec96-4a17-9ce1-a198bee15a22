<?php

namespace App\Models;

use App\Models\Traits\AutoSlugTrait;
use App\Models\Traits\BelongsToCompanyTrait;
use App\Models\Traits\DisplayNameTrait;
use App\Models\Traits\HashedPasswordTrait;
use App\Models\Traits\ProtectedDeleteTrait;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Passport\HasApiTokens;
use Libs\Warehouse\Resource;
use Propaganistas\LaravelPhone\PhoneNumber;
use Spatie\Permission\Traits\HasRoles;

/**
 * @property int $id
 * @property string $name
 * @property string $surname
 * @property string $email
 * @property ?CarbonInterface $email_verified_at
 * @property ?string $position
 * @property ?string $division
 * @property ?string $department
 * @property ?string $phone
 * @property ?int $avatar_resource_id
 * @property ?Resource $avatar_resource
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class User extends Authenticatable
{
    use AutoSlugTrait;
    use BelongsToCompanyTrait;
    use DisplayNameTrait;
    use HasApiTokens;
    use HasFactory;
    use HashedPasswordTrait;
    use Notifiable;
    use ProtectedDeleteTrait;
    use SoftDeletes;
    use HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
    protected $fillable = [
        'name',
        'surname',
        'email',
        'phone',
        'position',
        'division',
        'department',
        'avatar_resource_id',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'password',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'immutable_datetime',
    ];

    /**
     * Relationships that prevent user from being removed
     *
     * @var string[]
     */
    protected array $dependentRelationships = [
        // TODO: include relationships, including nested ones if necessary
    ];

    public function guardName() { return ['web', 'admin']; }

    /**
     * Retrieves the human-readable part of slug after identifier
     */
    public function getSlugSourceValue(): string
    {
        return $this->display_name;
    }

    public function avatar_resource(): BelongsTo
    {
        return $this->belongsTo(Resource::class);
    }

    public function setPhoneAttribute(?string $phone): void
    {
        $this->attributes['phone'] = $phone ? (new PhoneNumber($phone))->formatInternational() : null;
    }

    public function workspaces(): BelongsToMany
    {
        return $this->belongsToMany(Workspace::class, 'user_workspace');
    }

    public function rfps(): HasMany
    {
        return $this->hasMany(Rfp::class, 'author_id');
    }

    public function company() {
        return $this->belongsTo(Company::class);
    }
}
