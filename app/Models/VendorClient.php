<?php

namespace App\Models;

use App\Models\Traits\BelongsToVendorTrait;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property ?int $client_id
 * @property ?Client $client
 * @property string $name
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 */
class VendorClient extends Model
{
    use BelongsToVendorTrait;
    use HasFactory;

    protected $fillable = [
        'name',
        'client_id',
    ];

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }
}
