<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class TenderCompanyInvitation extends Pivot
{
    const string TABLE = 'tender_company_invitations';

    protected $table = self::TABLE;
    protected $casts = [
        'sent_at' => 'datetime',
        'first_viewed_at' => 'datetime',
    ];

    public $timestamps = true;

    public function tender(): BelongsTo
    {
        return $this->belongsTo(Tender::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }
}
