<?php

namespace App\Models;

use App\Enums\Country;
use App\Enums\Education;
use App\Enums\Seniority;
use App\Models\Traits\AutoSlugTrait;
use App\Models\Traits\BelongsToVendorTrait;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\MassPrunable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Libs\Vision\Parser\Casts\ParsedCVDataCast;
use Libs\Vision\Parser\POPOs\ParsedResumeData;
use Libs\Warehouse\Resource;

/**
 * Do NOT hard delete these records!
 *
 * @property int $id
 * @property string $public_id
 * @property ?int $origin_id
 * @property ?Candidate $origin
 * @property ?int $cv_resource_id
 * @property ?Resource $cv_resource
 * @property ?ParsedResumeData $cv_parsed_raw_data
 * @property bool $clone
 * @property bool $finished
 * @property string $internal_name
 * @property string $name
 * @property ?string $profession
 * @property ?Seniority $seniority
 * @property ?int $rate
 * @property ?float $rateCommission
 * @property ?string $last_job_title
 * @property ?int $years_of_experience
 * @property ?Education $highest_education
 * @property ?string $field_of_study
 * @property ?Country $country
 * @property ?string $city
 * @property string $location
 * @property EloquentCollection $clones
 * @property EloquentCollection $skills
 * @property EloquentCollection $experiences
 * @property ?CandidateAssignment $assignment
 * @property ?CandidateAssignment $activeAssignment
 * @property EloquentCollection $positions
 * @property BenchSpecialist $benchSpecialist bench specialist directly associated with this candidate
 * @property BenchSpecialist $activeBenchSpecialist bench specialist associated with some of the clones of this candidate
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class Candidate extends Model
{
    use AutoSlugTrait;
    use BelongsToVendorTrait;
    use HasFactory;
    use MassPrunable;
    use SoftDeletes;

    protected $fillable = [
        'cv_resource_id',
        'internal_name',
        'name',
        'rate',
        'seniority',
        'profession',
        'last_job_title',
        'years_of_experience',
        'highest_education',
        'field_of_study',
        'country',
        'city',
    ];

    protected $casts = [
        'cv_parsed_raw_data' => ParsedCVDataCast::class,
        'clone' => 'boolean',
        'finished' => 'boolean',
        'seniority' => Seniority::class,
        'highest_education' => Education::class,
        'country' => Country::class,
    ];

    protected string $slugIdField = 'public_id';

    protected string $slugSourceField = 'internal_name';

    public function origin(): BelongsTo
    {
        return $this->belongsTo(self::class);
    }

    public function clones(): HasMany
    {
        return $this->hasMany(self::class, 'origin_id');
    }

    public function cv_resource(): BelongsTo
    {
        return $this->belongsTo(Resource::class);
    }

    public function skills(): HasMany
    {
        return $this->hasMany(CandidateSkill::class);
    }

    public function experiences(): HasMany
    {
        return $this->hasMany(CandidateExperience::class)->ordered();
    }

    public function assignment(): HasOne
    {
        return $this->hasOne(CandidateAssignment::class);
    }

    public function activeAssignment(): HasOneThrough
    {
        return $this->hasOneThrough(
            CandidateAssignment::class,
            self::class,
            'origin_id', // Foreign key on the candidates table
            'candidate_id', // Foreign key on the candidate_assignments table
            'id', // Local key on the candidates table
            'id', // Local key on the candidates table
        )->active()->latest();
    }

    public function positions(): BelongsToMany
    {
        return $this->belongsToMany(TenderPosition::class, CandidateAssignment::TABLE, 'candidate_id', 'tender_position_id')
            ->using(CandidateAssignment::class)
            ->withPivot(
                'status',
                'rejection_reason',
                'client_note',
                'matching_api_payload',
                'matching_api_response',
                'matching_api_response_status',
            );
    }

    public function benchSpecialist(): HasOne
    {
        return $this->hasOne(BenchSpecialist::class);
    }

    public function activeBenchSpecialist(): HasOneThrough
    {
        return $this->hasOneThrough(
            BenchSpecialist::class,
            self::class,
            'origin_id',
            'candidate_id',
            'id',
            'id',
        )->active()->latest();
    }

    protected function location(): Attribute
    {
        return Attribute::get(fn () => $this->country->transWithCity($this->city));
    }

    public function prunable(): Builder
    {
        // Prunes all unfinished candidates older than a day
        return $this
            ->withTrashed()
            ->where('finished', false)
            ->where('updated_at', '<', now()->subDay());
    }

    public function rateCommission(): Attribute
    {
        return Attribute::get(fn () => $this->rate * config('candidate.rate_commission'));
    }

    public function scopeMinRate(Builder $query, int $rate): Builder
    {
        return $query->where('rate', '>=', $rate);
    }

    public function scopeMaxRate(Builder $query, int $rate): Builder
    {
        return $query->where('rate', '<=', $rate);
    }

    public function scopeAvailableFrom(Builder $query, string $date): Builder
    {
        return $query->whereHas('activeBenchSpecialist', function (Builder $query) use ($date): void {
            $query->where('available_from', '<=', $date);
        });
    }

    public function scopeAvailableTo(Builder $query, string $date): Builder
    {
        return $query->whereHas('activeBenchSpecialist', function (Builder $query) use ($date) {
            $query->where('available_to', '>=', $date);
        });
    }

    public function scopeEngagedFrom(Builder $query, string $date): Builder
    {
        return $query->whereHas('activeAssignment', function (Builder $query) use ($date) {
            $query->where('start_date', '<=', $date);
        });
    }

    public function scopeEngagedTo(Builder $query, string $date): Builder
    {
        return $query->whereHas('activeAssignment', function (Builder $query) use ($date) {
            $query->where('end_date', '>=', $date);
        });
    }
}
