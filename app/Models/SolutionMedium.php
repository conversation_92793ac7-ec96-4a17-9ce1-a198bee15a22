<?php

namespace App\Models;

use App\Enums\SolutionMediumType;
use App\Models\Traits\OrderedTrait;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\Resource;

/**
 * @property int $id
 * @property string $public_id
 * @property int $solution_id
 * @property Solution $solution
 * @property int $order
 * @property ?string $name
 * @property SolutionMediumType $type
 * @property int $resource_id
 * @property resource $resource
 * @property string|JsonResource|null $source
 * @property string|JsonResource|null $cover
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class SolutionMedium extends Model
{
    use HasFactory;
    use OrderedTrait;
    use SoftDeletes;

    protected $fillable = [
        'public_id',
        'name',
        'order',
        'type',
        'resource_id',
        'source',
    ];

    protected $casts = [
        'type' => SolutionMediumType::class,
    ];

    public function solution(): BelongsTo
    {
        return $this->belongsTo(Solution::class);
    }

    public function resource(): BelongsTo
    {
        return $this->belongsTo(Resource::class);
    }

    protected function source(): Attribute
    {
        return Attribute::get(fn () => $this->type->driver()->get($this));
    }

    protected function cover(): Attribute
    {
        return Attribute::get(fn () => $this->type->driver()->getCover($this));
    }
}
