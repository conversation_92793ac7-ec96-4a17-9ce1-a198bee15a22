<?php

namespace App\Models;

use App\Enums\Enterprise\WorkLocation;
use App\Enums\LengthType;
use App\Models\Traits\AutoSlugTrait;
use App\Models\Traits\ProtectedDeleteTrait;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $public_id
 * @property int $tender_id
 * @property Tender $tender
 * @property int $employee_position_id
 * @property EmployeePosition $employee_position
 * @property string $name
 * @property string $description
 * @property string $escaped_description
 * @property string $requirements
 * @property string $escaped_requirements
 * @property string $must_have_requirements
 * @property string $escaped_must_have_requirements
 * @property ?int $price
 * @property ?int $price_to
 * @property ?CarbonInterface $start_date
 * @property LengthType $length_type
 * @property ?int $length
 * @property bool $possible_extension
 * @property int $workload
 * @property ?WorkLocation $work_location
 * @property int $count
 * @property ?string $interview
 * @property ?string $equipment
 * @property int $months_count
 * @property int $position_total_costs_from
 * @property int $position_total_costs_to
 * @property EloquentCollection $seniorities
 * @property EloquentCollection $technologies
 * @property EloquentCollection $candidates
 * @property EloquentCollection $own_candidates
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class TenderPosition extends Model
{
    use AutoSlugTrait;
    use HasFactory;
    use ProtectedDeleteTrait;
    use SoftDeletes;

    const int FULL_WORKLOAD = 160; // hours

    protected $fillable = [
        'name',
        'description',
        'requirements',
        'must_have_requirements',
        'price',
        'price_to',
        'start_date',
        'length_type',
        'length',
        'possible_extension',
        'workload',
        'work_location',
        'count',
        'interview',
        'equipment',
        'employee_position_id',
    ];

    protected $casts = [
        'start_date' => 'immutable_datetime',
        'length_type' => LengthType::class,
        'possible_extension' => 'boolean',
        'work_location' => WorkLocation::class,
    ];

    protected string $slugIdField = 'public_id';

    protected array $dependentRelationships = [
        'candidates',
    ];

    protected function escapedDescription(): Attribute
    {
        return Attribute::get(fn () => sanitize_html($this->description));
    }

    protected function escapedRequirements(): Attribute
    {
        return Attribute::get(fn () => sanitize_html($this->requirements));
    }

    protected function escapedMustHaveRequirements(): Attribute
    {
        return Attribute::get(fn () => sanitize_html($this->must_have_requirements));
    }

    protected function monthsCount(): Attribute
    {
        return Attribute::get(fn () => $this->length_type === LengthType::Months
            ? $this->length
            : $this->length * 12
        );
    }

    protected function positionTotalCostsFrom(): Attribute
    {
        return Attribute::get(function () {
            $monthlyCosts = $this->price * $this->workload;

            return $monthlyCosts * $this->months_count;
        });
    }

    protected function positionTotalCostsTo(): Attribute
    {
        return Attribute::get(function () {
            $monthlyCosts = ($this->price_to ?? $this->price) * $this->workload;

            return $monthlyCosts * $this->months_count;
        });
    }

    public function tender(): BelongsTo
    {
        return $this->belongsTo(Tender::class);
    }

    public function employee_position(): BelongsTo
    {
        return $this->belongsTo(EmployeePosition::class);
    }

    public function seniorities(): HasMany
    {
        return $this->hasMany(TenderPositionSeniority::class);
    }

    public function technologies(): BelongsToMany
    {
        return $this->belongsToMany(Technology::class, 'tender_position_technology')
            ->withPivot('is_mandatory');
    }

    public function candidates(): BelongsToMany
    {
        return $this->belongsToMany(Candidate::class, CandidateAssignment::TABLE, 'tender_position_id', 'candidate_id')
            ->using(CandidateAssignment::class)
            ->withPivot(
                'status',
                'rejection_reason',
                'client_note',
                'matching_api_payload',
                'matching_api_response',
                'matching_api_response_status',
            );
    }

    public function own_candidates(): BelongsToMany
    {
        return $this->candidates()->whereRelation('vendor.company', 'id', auth()->user()?->company_id);
    }
}
