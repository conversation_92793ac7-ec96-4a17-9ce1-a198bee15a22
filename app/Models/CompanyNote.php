<?php

namespace App\Models;

use App\Models\Traits\BelongsToCompanyTrait;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property ?int $user_id
 * @property ?User $user
 * @property string $note
 * @property string $escaped_note
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class CompanyNote extends Model
{
    use BelongsToCompanyTrait;
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'note',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    protected function escapedNote(): Attribute
    {
        return Attribute::get(fn () => strip_tags($this->note));
    }
}
