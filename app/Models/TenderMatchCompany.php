<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class TenderMatchCompany extends Pivot
{
    public $timestamps = false;

    protected $fillable = [
        'match_id',
        'company_id',
        'score',
    ];

    public function tenderMatch(): BelongsTo
    {
        return $this->belongsTo(TenderMatch::class, 'match_id');
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    public function matchingReason(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->tenderMatch->matching_api_response?->firstWhere('company_id', $this->company->uuid) ?? []
        );
    }
}
