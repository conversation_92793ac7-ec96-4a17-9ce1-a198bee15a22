<?php

namespace App\Models;

use App\Models\Traits\AutoSlugTrait;
use App\Models\Traits\BelongsToCompanyTrait;
use App\Models\TenderMatch;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Libs\Overseer\PublishStatus;
use Libs\Overseer\Reviewable;

/**
 * @property int $id
 * @property string $public_id
 * @property PublishStatus $publish_status
 * @property Vendor $merge_into
 * @property Vendor $unapproved_change
 * @property int $employees
 * @property int $developers
 * @property bool $offering_resources
 * @property bool $offering_solutions
 * @property bool $payment_time_and_material
 * @property bool $payment_fixed_price
 * @property bool $notify_irrelevant_offers
 * @property ?int $rate_junior
 * @property ?int $rate_medior
 * @property ?int $rate_senior
 * @property ?int $rate_lead
 * @property string $display_name
 * @property resource $display_logo_resource
 * @property resource $display_cover_resource
 * @property ?int $main_industry_id
 * @property Industry $main_industry
 * @property EloquentCollection $industries
 * @property EloquentCollection $technologies
 * @property EloquentCollection $clients
 * @property EloquentCollection $employee_positions
 * @property EloquentCollection $solutions
 * @property EloquentCollection $published_solutions
 * @property EloquentCollection $tenders
 * @property EloquentCollection $candidates
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class Vendor extends Model
{
    use AutoSlugTrait;
    use BelongsToCompanyTrait;
    use HasFactory;
    use Reviewable {
        scopePublished as scopePublishedStatus;
    }
    use SoftDeletes;

    protected $fillable = [
        'main_industry_id',
        'employees',
        'developers',
        'offering_resources',
        'offering_solutions',
        'payment_time_and_material',
        'payment_fixed_price',
        'notify_irrelevant_offers',
        'rate_junior',
        'rate_medior',
        'rate_senior',
        'rate_lead',
    ];

    protected $casts = [
        'publish_status' => PublishStatus::class,
        'offering_resources' => 'boolean',
        'offering_solutions' => 'boolean',
        'payment_time_and_material' => 'boolean',
        'payment_fixed_price' => 'boolean',
        'notify_irrelevant_offers' => 'boolean',
    ];

    protected array $reviewableRelationships = [
        'industries',
        'technologies',
        'clients',
        'employee_positions',
    ];

    protected string $slugIdField = 'public_id';

    protected string $slugSourceField = 'company.display_name';

    // TODO: determine whether is makes sense to load company always or not

    public function main_industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class);
    }

    public function industries(): BelongsToMany
    {
        return $this->belongsToMany(Industry::class);
    }

    public function technologies(): BelongsToMany
    {
        return $this->belongsToMany(Technology::class);
    }

    public function solutions(): HasMany
    {
        return $this->hasMany(Solution::class);
    }

    public function clients(): HasMany
    {
        return $this->hasMany(VendorClient::class);
    }

    public function employee_positions(): BelongsToMany
    {
        return $this->belongsToMany(EmployeePosition::class)->orderBy('name');
    }

    public function published_solutions(): HasMany
    {
        return $this->solutions()->published();
    }

    public function tenders(): BelongsToMany
    {
        return $this->belongsToMany(Tender::class)->withPivot('allowed', 'notified');
    }

    public function candidates(): HasMany
    {
        return $this->hasMany(Candidate::class);
    }

    public function benchSpecialists(): HasManyThrough
    {
        return $this->through('candidates')->has('benchSpecialist');
    }

    protected function displayName(): Attribute
    {
        return Attribute::get(fn () => $this->company->display_name);
    }

    protected function displayLogoResource(): Attribute
    {
        return Attribute::get(fn () => $this->company->display_logo_resource);
    }

    protected function displayCoverResource(): Attribute
    {
        return Attribute::get(fn () => $this->company->display_cover_resource);
    }

    public function scopePublished(Builder $query): Builder
    {
        return $query->publishedStatus()->whereRelation('company', 'is_vendor', true);
    }

    public function scopePublic(Builder $query): Builder
    {
        return $query->whereRelation('company', 'is_public', true);
    }
}
