<?php

namespace App\Models;

use App\Enums\Seniority;
use App\Models\Traits\AutoSlugTrait;
use App\Models\Traits\ProtectedDeleteTrait;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $name
 * @property string $name_plural
 * @property bool $hireable
 * @property ?string $seniorities
 * @property Collection $seniorities_collection
 * @property EloquentCollection $vendors
 * @property EloquentCollection $tender_positions
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class EmployeePosition extends Model
{
    use AutoSlugTrait;
    use HasFactory;
    use ProtectedDeleteTrait;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'name_plural',
        'seniorities',
        'hireable',
    ];

    protected $casts = [
        'hireable' => 'boolean',
    ];

    protected array $dependentRelationships = [
        'vendors',
        'tender_positions',
    ];

    public function vendors(): BelongsToMany
    {
        return $this->belongsToMany(Vendor::class);
    }

    public function tender_positions(): HasMany
    {
        return $this->hasMany(TenderPosition::class);
    }

    protected function senioritiesCollection(): Attribute
    {
        return Attribute::get(fn () => collect(
            $this->seniorities ? explode(',', $this->seniorities) : null
        )->map(fn ($value) => Seniority::from($value)));
    }
}
