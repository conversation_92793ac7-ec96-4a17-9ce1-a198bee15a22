<?php

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $solution_id
 * @property Solution $solution
 * @property bool $anonymous
 * @property ?int $client_id
 * @property Client $client
 * @property ?string $name
 * @property ?string $review
 * @property ?string $escaped_review
 * @property ?string $reviewer
 * @property ?string $reviewer_position
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class SolutionClient extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'anonymous',
        'client_id',
        'name',
        'review',
        'reviewer',
        'reviewer_position',
    ];

    protected $casts = [
        'anonymous' => 'boolean',
    ];

    protected function escapedReview(): Attribute
    {
        return Attribute::get(fn () => ! $this->review ? null : strip_tags($this->review));
    }

    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }
}
