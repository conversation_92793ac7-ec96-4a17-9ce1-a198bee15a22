<?php

namespace App\Models;

use App\Enums\Enterprise\Timezone;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Model;

use App\Enums\Enterprise\AssistantStep;

class Rfp extends Model
{
    use HasUuids;
    use SoftDeletes;

    protected $fillable = [
        'text',
        'name',
        'files',
        'author_id',
        'tender_id',
        'step',
        'rfp_api_called',
        'resources_extract_api_called',
        'resources_suggest_api_called',
        'resources_extract_api_response',
        'resources_suggest_api_response',
        'title',
        'start_date',
        'end_date',
        'timezone',
        'primary_industry_id',
        'description',
        'key_deliverables',
        'milestones',
        'team_composition'
    ];

    protected function casts(): array
    {
        return [
            'files' => 'array',
            'step' => AssistantStep::class,
            'info_api_called' => 'boolean',
            'resources_extract_api_called' => 'boolean',
            'resources_suggest_api_called' => 'boolean',
            'resources_extract_api_response' => 'array',
            'resources_suggest_api_response' => 'array',
            'start_date' => 'date',
            'end_date' => 'date',
            'timezone' => Timezone::class,
            'description' => 'string',
        ];
    }


    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    public function tender(): BelongsTo
    {
        return $this->belongsTo(Tender::class, 'tender_id');
    }

    public function primaryIndustry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, 'primary_industry_id');
    }

    public function secondaryIndustries(): BelongsToMany
    {
        return $this->belongsToMany(Industry::class, 'rfp_secondary_industry', 'rfp_id', 'industry_id');
    }

    public function locations(): BelongsToMany
    {
        return $this->belongsToMany(Location::class, 'rfp_location');
    }

    public function certifications(): BelongsToMany
    {
        return $this->belongsToMany(Certification::class, 'rfp_certification');
    }

    public function rfpPositions(): HasMany
    {
        return $this->hasMany(RfpPosition::class);
    }
}
