<?php

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * @property int $id
 * @property int $candidate_id
 * @property Candidate $candidate
 * @property int $technology_id
 * @property Technology $technology
 * @property ?int $years_of_experience
 * @property EloquentCollection $experiences
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 */
class CandidateSkill extends Model
{
    use HasFactory;

    protected $fillable = [
        'technology_id',
        'years_of_experience',
    ];

    public function candidate(): BelongsTo
    {
        return $this->belongsTo(Candidate::class);
    }

    public function technology(): BelongsTo
    {
        return $this->belongsTo(Technology::class);
    }

    public function experiences(): BelongsToMany
    {
        return $this->belongsToMany(CandidateExperience::class)->ordered();
    }
}
