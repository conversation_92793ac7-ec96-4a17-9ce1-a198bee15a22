<?php

namespace App\Models;

use App\Enums\LengthType;
use App\Models\Traits\AutoSlugTrait;
use App\Models\Traits\OrderedTrait;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $public_id
 * @property int $order
 * @property int $candidate_id
 * @property Candidate $candidate
 * @property string $name
 * @property string $description
 * @property LengthType $length_type
 * @property ?int $length
 * @property EloquentCollection $skills
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class CandidateExperience extends Model
{
    use AutoSlugTrait;
    use HasFactory;
    use OrderedTrait;
    use SoftDeletes;

    protected $fillable = [
        'order',
        'name',
        'description',
        'about',
        'length_type',
        'length',
    ];

    protected $casts = [
        'length_type' => LengthType::class,
    ];

    public function candidate(): BelongsTo
    {
        return $this->belongsTo(Candidate::class);
    }

    public function skills(): BelongsToMany
    {
        return $this->belongsToMany(CandidateSkill::class);
    }
}
