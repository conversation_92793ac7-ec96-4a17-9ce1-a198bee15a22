<?php

namespace App\Models;

use App\Enums\Enterprise\TechnologyType;
use App\Models\Traits\AutoSlugTrait;
use App\Models\Traits\ProtectedDeleteTrait;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Libs\Warehouse\Resource;

/**
 * @property int $id
 * @property ?string $emsi_id
 * @property int $parent_id
 * @property Technology $parent
 * @property string $name
 * @property ?int $logo_resource_id
 * @property resource $logo_resource
 * @property bool $featured
 * @property EloquentCollection $vendors
 * @property EloquentCollection $solutions
 * @property EloquentCollection $tender_projects
 * @property EloquentCollection $tender_positions
 * @property EloquentCollection $candidate_skills
 * @property EloquentCollection $children
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class Technology extends Model
{
    use AutoSlugTrait;
    use HasFactory;
    use ProtectedDeleteTrait;
    use SoftDeletes;

    protected $fillable = [
        'emsi_id',
        'parent_id',
        'name',
        'logo_resource_id',
        'featured',
        'category_id',
        'id_string',
        'type'
    ];

    protected $casts = [
        'featured' => 'boolean',
        'type' => TechnologyType::class
    ];

    protected array $dependentRelationships = [
        'vendors',
        'solutions',
        'tender_projects',
        'tender_positions',
        'children',
        'candidate_skills',
    ];

    public function vendors(): BelongsToMany
    {
        return $this->belongsToMany(Vendor::class);
    }

    public function solutions(): BelongsToMany
    {
        return $this->belongsToMany(Solution::class);
    }

    public function tender_projects(): BelongsToMany
    {
        return $this->belongsToMany(TenderProject::class);
    }

    public function tender_positions(): BelongsToMany
    {
        return $this->belongsToMany(TenderPosition::class);
    }

    public function candidate_skills(): HasMany
    {
        return $this->hasMany(CandidateSkill::class);
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    public function logo_resource(): BelongsTo
    {
        return $this->belongsTo(Resource::class);
    }
}
