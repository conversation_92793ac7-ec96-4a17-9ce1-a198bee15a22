<?php

namespace App\Models;

use App\Enums\Enterprise\IndustryType;
use App\Models\Traits\AutoSlugTrait;
use App\Models\Traits\ProtectedDeleteTrait;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $parent_id
 * @property Industry $parent
 * @property string $name
 * @property bool $featured
 * @property EloquentCollection $main_vendors
 * @property EloquentCollection $vendors
 * @property EloquentCollection $main_solutions
 * @property EloquentCollection $solutions
 * @property EloquentCollection $main_tender_projects
 * @property EloquentCollection $tender_projects
 * @property EloquentCollection $children
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class Industry extends Model
{
    use AutoSlugTrait;
    use HasFactory;
    use ProtectedDeleteTrait;
    use SoftDeletes;

    protected $fillable = [
        'parent_id',
        'name',
        'featured',
        'id_string',
        'type'
    ];

    protected $casts = [
        'featured' => 'boolean',
        'type' => IndustryType::class
    ];

    protected array $dependentRelationships = [
        'main_vendors',
        'vendors',
        'main_solutions',
        'solutions',
        'main_tender_projects',
        'tender_projects',
        'children',
    ];

    protected function name(): Attribute
    {
        return Attribute::make(
            set: fn (string $value): string => ucfirst($value),
        );
    }

    public function main_vendors(): HasMany
    {
        return $this->hasMany(Vendor::class, 'main_industry_id');
    }

    public function vendors(): BelongsToMany
    {
        return $this->belongsToMany(Vendor::class);
    }

    public function main_solutions(): HasMany
    {
        return $this->hasMany(Solution::class, 'main_industry_id');
    }

    public function solutions(): BelongsToMany
    {
        return $this->belongsToMany(Solution::class);
    }

    public function main_tender_projects(): HasMany
    {
        return $this->hasMany(TenderProject::class, 'main_industry_id');
    }

    public function tender_projects(): BelongsToMany
    {
        return $this->belongsToMany(TenderProject::class);
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }
}
