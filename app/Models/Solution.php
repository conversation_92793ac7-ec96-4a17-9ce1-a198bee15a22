<?php

namespace App\Models;

use App\Enums\Country;
use App\Enums\LengthType;
use App\Enums\SolutionOrigin;
use App\Models\Traits\AutoSlugTrait;
use App\Models\Traits\BelongsToVendorTrait;
use App\Repositories\SolutionMediaRepository;
use App\Scopes\OfExistingVendorScope;
use App\Scopes\OfPublicSolutionOriginScope;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Libs\Overseer\PublishStatus;
use Libs\Overseer\Reviewable;
use Libs\Warehouse\Resource;

/**
 * @property int $id
 * @property string $public_id
 * @property PublishStatus $publish_status
 * @property ?Solution $merge_into
 * @property ?Solution $unapproved_change
 * @property SolutionOrigin $origin
 * @property ?string $origin_id
 * @property string $name
 * @property string $description
 * @property string $about
 * @property string $escaped_about
 * @property ?int $cover_resource_id
 * @property ?Resource $cover_resource
 * @property ?Country $country
 * @property LengthType $length_type
 * @property ?int $length
 * @property ?int $ftes
 * @property ?int $value
 * @property ?bool $in_house
 * @property ?int $main_industry_id
 * @property ?Industry $main_industry
 * @property EloquentCollection $industries
 * @property EloquentCollection $technologies
 * @property EloquentCollection $media
 * @property SolutionClient $client
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class Solution extends Model
{
    use AutoSlugTrait;
    use BelongsToVendorTrait;
    use HasFactory;
    use Reviewable {
        scopePublished as scopePublishedStatus;
    }
    use SoftDeletes;

    protected $fillable = [
        'vendor_id',
        'name',
        'description',
        'about',
        'cover_resource_id',
        'country',
        'main_industry_id',
        'length_type',
        'length',
        'ftes',
        'value',
        'in_house',
    ];

    protected $attributes = [
        'publish_status' => PublishStatus::Draft,
        'origin' => SolutionOrigin::Platform,
    ];

    protected $casts = [
        'publish_status' => PublishStatus::class,
        'origin' => SolutionOrigin::class,
        'length_type' => LengthType::class,
        'country' => Country::class,
        'in_house' => 'boolean',
    ];

    protected array $reviewableRelationships = [
        'industries',
        'technologies',
        'client',
        'media',
    ];

    protected string $slugIdField = 'public_id';

    protected static function booted(): void
    {
        parent::booted();
        static::addGlobalScope(new OfExistingVendorScope);
        static::addGlobalScope(new OfPublicSolutionOriginScope);
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    public function main_industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class);
    }

    public function industries(): BelongsToMany
    {
        return $this->belongsToMany(Industry::class);
    }

    public function technologies(): BelongsToMany
    {
        return $this->belongsToMany(Technology::class);
    }

    public function media(): HasMany
    {
        return $this->hasMany(SolutionMedium::class)->ordered();
    }

    public function client(): HasOne
    {
        return $this->hasOne(SolutionClient::class);
    }

    public function cover_resource(): BelongsTo
    {
        return $this->belongsTo(Resource::class);
    }

    protected function escapedAbout(): Attribute
    {
        return Attribute::get(fn () => sanitize_html($this->about));
    }

    public function scopePublished(Builder $query): Builder
    {
        return $query->publishedStatus()->whereHas('vendor', fn ($query) => $query->published());
    }

    public function scopeWithHidden(Builder $query): Builder
    {
        return $query->withoutGlobalScope(OfPublicSolutionOriginScope::class);
    }

    public function scopeHidden(Builder $query): Builder
    {
        return $query->withHidden()->whereNotIn('origin', SolutionOrigin::publicOrigins());
    }

    // What is below here is a temporary solution until something more robust will be developed,
    // because the current solution is FUBAR
    public function storeNewMediaEntity(self $model, array $data, bool $publishMerge): void
    {
        // In case we are merging for publish, data is already created,
        // so we have to save it directly to the database. Otherwise,
        // any freshly created solution media needs to be saved through
        // the repository so that it can be properly handled.
        if ($publishMerge) {
            $this->media()->create($data);
        } else {
            /** @var SolutionMediaRepository $repository */
            $repository = app(SolutionMediaRepository::class);
            $repository->store($model, $data);
        }
    }
}
