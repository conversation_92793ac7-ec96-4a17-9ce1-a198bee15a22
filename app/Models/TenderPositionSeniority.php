<?php

namespace App\Models;

use App\Enums\Seniority;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $tender_position_id
 * @property Seniority $seniority
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 */
class TenderPositionSeniority extends Model
{
    use HasFactory;

    protected $fillable = [
        'seniority',
    ];

    protected $casts = [
        'seniority' => Seniority::class,
    ];
}
