<?php

namespace App\Models\Traits;

use App\Enums\UserRole;
use Illuminate\Database\Eloquent\Casts\Attribute;

/**
 * @property bool $is_public_to_user detonates whether given model is public to the currently authenticated user
 */
trait PublicToUserTrait
{
    private ?bool $isPublicToCurrentUser = null;

    protected function isPublicToUser(): Attribute
    {
        return Attribute::get(function () {
            if (! $this->isPublicToCurrentUser) {
                $this->isPublicToCurrentUser = $this->checkIfIsPublicToCurrentUser();
            }

            return $this->isPublicToCurrentUser;
        });
    }

    private function checkIfIsPublicToCurrentUser(): bool
    {
        // If there is explicitly true 'is_public' property,
        // ignore everything else and display it
        if ($this->is_public) {
            return true;
        }

        $user = auth()->user();

        // If user is not authenticated, do not show sensitive info
        if (! $user) {
            return false;
        }

        // Currently, NIO staff can see everything
        if (UserRole::SuperAdmin->has($user)) {
            return true;
        }

        // Clients can always see their own company
        // FIXME: only works on company model. Figure out the best way to determine relationship id!
        if ($user->company_id === $this->id) {
            return true;
        }

        return false;
    }
}
