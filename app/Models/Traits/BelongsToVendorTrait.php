<?php

namespace App\Models\Traits;

use App\Models\Company;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

/**
 * @property int $vendor_id
 * @property Company $company
 * @property Vendor $vendor
 *
 * @method Builder ofVendor(string|int|Vendor|null $slug) scopes query for records belonging to a given vendor
 * @method Builder notOfVendor(string|int|Vendor|null $slug) scopes query for records not belonging to a given vendor
 */
trait BelongsToVendorTrait
{
    public function company(): HasOneThrough
    {
        return $this->hasOneThrough(
            Company::class,
            Vendor::class,
            'id',
            'id',
            'vendor_id',
            'company_id',
        );
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    public function scopeOfVendor(Builder $query, string|int|Vendor|null $slug): Builder
    {
        $slug = $slug instanceof Vendor ? $slug->id : $slug;

        return $query->whereHas(
            'vendor',
            fn (Builder $subQuery) => is_int($slug) ? $subQuery->where('id', $slug) : $subQuery->slug($slug),
        );
    }

    public function scopeNotOfVendor(Builder $query, string|int|Vendor|null $slug): Builder
    {
        $slug = $slug instanceof Vendor ? $slug->id : $slug;

        return $query->whereDoesntHave(
            'vendor',
            fn (Builder $subQuery) => is_int($slug) ? $subQuery->where('id', $slug) : $subQuery->slug($slug),
        );
    }

    public function initializeBelongsToVendorTrait(): void
    {
        $this->mergeFillable([
            'vendor_id',
        ]);
    }
}
