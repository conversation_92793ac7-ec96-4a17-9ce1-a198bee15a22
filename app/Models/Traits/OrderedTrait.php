<?php

namespace App\Models\Traits;

use Illuminate\Database\Eloquent\Builder;

trait OrderedTrait
{
    /**
     * Field which is used to order records.
     */
    private string $__defaultOrderField = 'order';

    /**
     * Order in which 'order' field is sorted.
     */
    private string $__defaultOrderedDirection = 'ASC';

    /**
     * Orders results according to a dedicated 'order' column.
     *
     * @param  Builder  $query  Eloquent query builder
     */
    public function scopeOrdered(Builder $query): Builder
    {
        return $query->orderBy($this->__getOrderField(), $this->__getOrderedDirection());
    }

    private function __getOrderField(): string
    {
        return $this->orderField ?? $this->__defaultOrderField;
    }

    private function __getOrderedDirection(): string
    {
        return $this->orderedDirection ?? $this->__defaultOrderedDirection;
    }
}
