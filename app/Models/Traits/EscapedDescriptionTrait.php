<?php

namespace App\Models\Traits;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Str;

/**
 * @property string e_description             description escaped from dangerous HTML
 * @property string e_description_excerpt     excerpt of escaped description
 * @property string plain_description         description escaped from all HTML
 * @property string plain_description_excerpt excerpt from plain description
 */
trait EscapedDescriptionTrait
{
    /**
     * Field that stores path to description
     */
    private string $__defaultDescriptionField = 'description';

    /**
     * Returns escaped description with only allowed HTML tags.
     * Good for showing content from WYSIWYG editors.
     */
    protected function eDescription(): Attribute
    {
        return Attribute::get(fn () => $this->escapeText($this->attributes[$this->__getDescriptionField()]));
    }

    /**
     * Returns escaped text with only allowed HTML tags.
     * Good for showing content from WYSIWYG editors.
     *
     * @param  string  $text  text to be escaped
     */
    protected function escapeText(string $text): string
    {
        return sanitize_html(nl2br($text));
    }

    /**
     * Returns plain text excerpt from description.
     * Good for showing preview of description in lists.
     */
    protected function eDescriptionExcerpt(): Attribute
    {
        return Attribute::get(fn () => $this->escapeTextExcerpt($this->plain_description));
    }

    /**
     * Returns plain text excerpt from text.
     * Good for showing preview of text in lists.
     *
     * @param  string  $text  text to be escaped
     */
    protected function escapeTextExcerpt(string $text): string
    {
        return Str::limit($text, 350);
    }

    /**
     * Returns plain text excerpt from description.
     * Good for displaying in tables and lists.
     */
    protected function plainDescriptionExcerptExcerpt(): Attribute
    {
        return Attribute::get(fn () => $this->escapeTextShortExcerpt($this->plain_description));
    }

    /**
     * Returns plain text excerpt from text.
     * Good for displaying in tables and lists.
     *
     * @param  string  $text  text to be escaped
     */
    public function escapeTextShortExcerpt(string $text): string
    {
        return Str::limit($text, 100);
    }

    /**
     * Returns plain text description.
     * Good for showing unformatted content.
     */
    protected function plainDescription(): Attribute
    {
        return Attribute::get(fn () => $this->stripText($this->attributes[$this->__getDescriptionField()]));
    }

    /**
     * Returns plain text text.
     * Good for showing unformatted content.
     *
     * @param  string  $text  text to be stripped
     */
    public function stripText(string $text): string
    {
        return strip_tags($text);
    }

    private function __getDescriptionField(): string
    {
        return $this->descriptionField ?? $this->__defaultDescriptionField;
    }
}
