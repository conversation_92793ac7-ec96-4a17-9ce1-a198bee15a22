<?php

namespace App\Models\Traits;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Query\Builder;

/**
 * @property string $display_name All filled-in name parts merged in one property
 */
trait DisplayNameTrait
{
    /**
     * Merges name and surname into one string
     */
    protected function displayName(): Attribute
    {
        return Attribute::get(fn () => $this->surname
            ? "$this->name $this->surname"
            : $this->name
        );
    }

    /**
     * Scope for ordering by name and then surname
     *
     * @param  string  $direction  [default: 'ASC']
     * @param  bool  $surnameFirst  [default: false]
     */
    public function scopeOrderByFullName(Builder $query, string $direction = 'ASC', bool $surnameFirst = false): Builder
    {
        return $surnameFirst
            ? $query->orderBy('surname', $direction)->orderBy('name', $direction)
            : $query->orderBy('name', $direction)->orderBy('surname', $direction);
    }
}
