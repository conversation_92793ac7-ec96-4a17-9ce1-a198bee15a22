<?php

namespace App\Models\Traits;

use App\Models\Company;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;

/**
 * @property int $company_id
 * @property Company $company
 * @property ?Vendor $vendor
 *
 * @method Builder ofCompany(string|int|Company|null $slug) scopes query for records belonging to a given company
 * @method Builder notOfCompany(string|int|Company|null $slug) scopes query for records not belonging to a given company
 */
trait BelongsToCompanyTrait
{
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function vendor(): HasOneThrough
    {
        return $this->hasOneThrough(
            Vendor::class,
            Company::class,
            'id',
            'company_id',
            'company_id',
            'id',
        );
    }

    public function scopeOfCompany(Builder $query, string|int|Company|null $slug): Builder
    {
        $slug = $slug instanceof Company ? $slug->id : $slug;

        return $query->whereHas(
            'company',
            fn (Builder $subQuery) => is_int($slug) ? $subQuery->where('id', $slug) : $subQuery->slug($slug),
        );
    }

    public function scopeNotOfCompany(Builder $query, string|int|Company|null $slug): Builder
    {
        $slug = $slug instanceof Company ? $slug->id : $slug;

        return $query->whereDoesntHave(
            'company',
            fn (Builder $subQuery) => is_int($slug) ? $subQuery->where('id', $slug) : $subQuery->slug($slug),
        );
    }

    public function initializeBelongsToCompanyTrait(): void
    {
        $this->mergeFillable([
            'company_id',
        ]);
    }
}
