<?php

namespace App\Models\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Str;

/**
 * @property string $slug Defines a pseudo-attribute that generates dynamic slug.
 *                        Slug starts with id and other field (such as name)
 *                        is appended to it after being slugified.
 */
trait AutoSlugTrait
{
    /**
     * Field that servers as an id.
     */
    private string $__defaultSlugIdField = 'id';

    /**
     * Field that is used to created user-friendly
     * part of the slug.
     */
    private string $__defaultSlugSourceField = 'name';

    /**
     * Defines a pseudo-attribute that generates dynamic slug.
     * Slug starts with id and other field (such as name)
     * is appended to it after being slugified.
     */
    protected function slug(): Attribute
    {
        return Attribute::get(fn () => $this->getSlugIdValue().'-'.Str::slug($this->getSlugSourceValue()));
    }

    /**
     * Scopes seach query by id extracted from auto-slug.
     *
     * @param  Builder  $query  Eloquent query builder
     * @param  ?string  $slug  auto-slug containing id to be searched for
     */
    public function scopeSlug(Builder $query, ?string $slug): Builder
    {
        $id = unslugify($slug);

        return $query->where($this->getSlugIdField(), $id);
    }

    /**
     * Fetches entity by its slug
     *
     * @param  Builder  $query  Eloquent query builder
     * @param  ?string  $slug  auto-slug containing id to be searched for
     */
    public function scopeFindBySlug(Builder $query, ?string $slug): ?static
    {
        return $query->slug($slug)->first();
    }

    /**
     * Attempts to fetch entity by its slug. Throws if nothing is found.
     *
     * @param  Builder  $query  Eloquent query builder
     * @param  ?string  $slug  auto-slug containing id to be searched for
     */
    public function scopeFindBySlugOrFail(Builder $query, ?string $slug): static
    {
        return $query->slug($slug)->firstOrFail();
    }

    protected function getSlugIdValue(): string
    {
        return $this->{$this->getSlugIdField()};
    }

    protected function getSlugIdField(): string
    {
        return $this->slugIdField ?? $this->__defaultSlugIdField;
    }

    /**
     * Retrieves the human-readable part of slug after identifier
     */
    protected function getSlugSourceValue(): string
    {
        return collect(explode('.', $this->getSlugSourceField()))
            ->reduce(fn ($parent, $field) => $parent->{$field}, $this);
    }

    protected function getSlugSourceField(): string
    {
        return $this->slugSourceField ?? $this->__defaultSlugSourceField;
    }
}
