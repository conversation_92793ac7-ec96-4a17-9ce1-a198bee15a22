<?php

namespace App\Models\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

/**
 * @method Builder orderByEnum(string $field, Collection|array $enums) orders results in order of given enum values
 */
trait OrderByEnumTrait
{
    /**
     * Performs a FIELD ordering based on given field and values
     */
    public function scopeOrderByEnum(Builder $query, string $field, Collection|array $enums): Builder
    {
        $field = e($field);

        $preparedEnums = array_fill(0, count($enums), '?');
        $preparedEnumsString = implode(', ', $preparedEnums);

        return $query->orderByRaw("FIELD($field, $preparedEnumsString)", $enums);
    }
}
