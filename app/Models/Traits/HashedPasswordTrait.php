<?php

namespace App\Models\Traits;

use Illuminate\Support\Facades\Hash;

/**
 * This trait adds convenient methods to handle passwords
 * in secure and simple way. Primary usage is intended for
 * user entity models. This trait assumes that password is
 * stored in "password" database field.
 *
 * @property string $password hashed password of the user
 */
trait HashedPasswordTrait
{
    /**
     * Saves password to model using current Hash settings
     */
    public function setPasswordAttribute(?string $password): void
    {
        // By skipping empty passwords, we make it convenient to keep
        // password mass assignable from forms - and if password is
        // not filled in the form, then it should remain unchanged.
        if (! $password) {
            return;
        }

        $this->attributes['password'] = Hash::make($password);
    }

    /**
     * Clears any saved password
     */
    public function clearPassword(): void
    {
        $this->attributes['password'] = null;
    }

    /**
     * If current password's configuration is old, rehashes password
     *
     * @param  bool  $save  [default: true]
     */
    public function rehashPasswordIfNecessary(string $password, bool $save = true): bool
    {
        if (Hash::needsRehash($this->password)) {
            $this->password = $password;

            if ($save) {
                return $this->save();
            }
        }

        return true;
    }

    /**
     * Verifies password against stored hash
     */
    public function checkPassword(string $password): bool
    {
        return Hash::check($password, $this->password);
    }

    /**
     * Performs dummy password hashing. Can be used during auth to ensure
     * that even if user does not exist in database, the request will
     * take the same time as if it was found. This way, we can prevent
     * e-mail guessing.
     */
    public static function fakePasswordHashing(): void
    {
        Hash::make('dummystring');
    }
}
