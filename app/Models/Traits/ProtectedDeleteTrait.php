<?php

namespace App\Models\Traits;

use App\Exceptions\ModelCannotBeDeletedException;
use App\Exceptions\ModelStillInRelationshipsException;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Performs automatic checks to determine whether
 * it is safe to delete model instance or not.
 */
trait ProtectedDeleteTrait
{
    /**
     * If model should be protected when force deleted.
     * Works only with SoftDelete trait.
     */
    protected bool $protectOnForceDelete = true;

    protected static function bootProtectedDeleteTrait(): void
    {
        static::deleting(fn (self $model) => $model->checkAndProtect());
    }

    /**
     * List of relationships that should not exist
     * when attempting to remove a record.
     */
    protected function getDependentRelationships(): array
    {
        return $this->dependentRelationships ?? [];
    }

    /**
     * Custom verification for protecting deletion of model.
     * Specify your own complex condition here. If this method returns false,
     * \App\Exceptions\ModelCannotBeDeletedException will be thrown.
     * If you need to customize exception, feel free to thrown your own within
     * this method.
     */
    protected function canBeDeleted(): bool
    {
        return true;
    }

    /**
     * @throws ModelStillInRelationshipsException
     * @throws ModelCannotBeDeletedException
     */
    private function checkAndProtect(): void
    {
        // If for some reason model is not protected when force deleted,
        // then we will skip the check entirely
        if (! $this->protectOnForceDelete && $this->isModelForceDeleting()) {
            return;
        }

        // Verify if all protected relationship are empty.
        // If any of them exist, we will abort model deletion
        foreach ($this->getDependentRelationships() as $protectedRelationship) {
            if ($this->{$protectedRelationship}()->exists()) {
                throw new ModelStillInRelationshipsException($this);
            }
        }

        if (! $this->canBeDeleted()) {
            throw new ModelCannotBeDeletedException($this);
        }
    }

    private function isModelForceDeleting(): bool
    {
        $traits = class_uses_recursive($this);

        return in_array(SoftDeletes::class, $traits)
            && $this->isForceDeleting();
    }
}
