<?php

namespace App\Models;

use App\Enums\Country;
use App\Enums\LengthType;
use App\Enums\PaymentType;
use App\Enums\ServiceType;
use App\Enums\TenderProcessingType;
use App\Enums\TenderStatus;
use App\Models\Traits\AutoSlugTrait;
use App\Models\Traits\BelongsToCompanyTrait;
use App\Models\Traits\ProtectedDeleteTrait;
use App\Models\Traits\PublishStatesTrait;
use App\Scopes\AccessibleTenderScope;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Libs\Overseer\PublishStatus;
use Libs\Warehouse\Resource;
use Mattiverse\Userstamps\Traits\Userstamps;

/**
 * @property int $id
 * @property string $public_id
 * @property bool $anonymous_company
 * @property PublishStatus $publish_status
 * @property TenderStatus $status
 * @property TenderProcessingType $processing_type
 * @property ServiceType $service_type
 * @property PaymentType $payment_type
 * @property CarbonInterface $submissions_deadline
 * @property ?CarbonInterface $end_of_incubation
 * @property ?CarbonInterface $evaluation_date
 * @property ?int $price
 * @property ?int $price_to
 * @property LengthType $length_type
 * @property ?int $length
 * @property string $estimation
 * @property string $name
 * @property string $description
 * @property string $about
 * @property string $escaped_about
 * @property int $cover_resource_id
 * @property resource $cover_resource
 * @property Country $country
 * @property TenderProject $project
 * @property EloquentCollection $positions
 * @property EloquentCollection $vendors
 * @property EloquentCollection $allowed_vendors
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 *
 * @method static Builder incubated()
 * @method static Builder open()
 * @method static Builder notClosed()
 * @method static Builder completed()
 * @method static Builder withUnavailable()
 */
class Tender extends Model
{
    use AutoSlugTrait;
    use BelongsToCompanyTrait;
    use HasFactory;
    use ProtectedDeleteTrait;
    use PublishStatesTrait;
    use SoftDeletes;
    use Userstamps;

    protected $fillable = [
        'anonymous_company',
        'publish_status',
        'status',
        'submissions_deadline',
        'end_of_incubation',
        'processing_type',
        'service_type',
        'evaluation_date',
        'payment_type',
        'price',
        'price_to',
        'length_type',
        'length',
        'name',
        'description',
        'matching_description',
        'about',
        'cover_resource_id',
        'country',
        'simulation',
    ];

    protected $casts = [
        'anonymous_company' => 'boolean',
        'publish_status' => PublishStatus::class,
        'status' => TenderStatus::class,
        'submissions_deadline' => 'immutable_datetime',
        'end_of_incubation' => 'immutable_datetime',
        'processing_type' => TenderProcessingType::class,
        'service_type' => ServiceType::class,
        'evaluation_date' => 'immutable_datetime',
        'payment_type' => PaymentType::class,
        'length_type' => LengthType::class,
        'country' => Country::class,
        'description' => 'string',
        'matching_description' => 'string',
        'simulation' => 'boolean',
    ];

    protected string $slugIdField = 'public_id';

    protected static function booted(): void
    {
        parent::booted();
        static::addGlobalScope(new AccessibleTenderScope);
    }

    protected function canBeDeleted(): bool
    {
        return $this->publish_status !== PublishStatus::Published
            && ! $this->status->isCompleted();
    }

    public function scopeIncubated(Builder $query): Builder
    {
        return $query->where($this->qualifyColumn('status'), TenderStatus::Incubation);
    }

    public function scopeOpen(Builder $query): Builder
    {
        return $query->where($this->qualifyColumn('status'), TenderStatus::Open);
    }

    public function scopeNotClosed(Builder $query): Builder
    {
        return $query->whereIn($this->qualifyColumn('status'), TenderStatus::notCompletedArray());
    }

    public function scopeCompleted(Builder $query): Builder
    {
        return $query->whereIn($this->qualifyColumn('status'), TenderStatus::completedArray());
    }

    public function scopeWithUnavailable(Builder $query): Builder
    {
        return $query->withoutGlobalScope(AccessibleTenderScope::class);
    }

    public function project(): HasOne
    {
        return $this->hasOne(TenderProject::class);
    }

    public function positions(): HasMany
    {
        return $this->hasMany(TenderPosition::class);
    }

    public function vendors(): BelongsToMany
    {
        return $this->belongsToMany(Vendor::class)->withPivot('allowed', 'notified');
    }

    public function allowed_vendors(): BelongsToMany
    {
        return $this->vendors()->wherePivot('allowed', true);
    }

    public function cover_resource(): BelongsTo
    {
        return $this->belongsTo(Resource::class);
    }

    protected function estimation(): Attribute
    {
        return Attribute::get(function () {
            if ($this->service_type === ServiceType::Resources) {
                $lower = $this->positions->sum(fn ($position) => $position->position_total_costs_from * $position->count);
                $upper = $this->positions->sum(fn ($position) => $position->position_total_costs_to * $position->count);
            } else {
                $lower = $this->price;
                $upper = $this->price_to ?? $this->price;
            }

            $estimation = ($lower + $upper) / 2;

            return __('global.labels.estimation', ['value' => format_large_number($estimation)]);
        });
    }

    protected function escapedAbout(): Attribute
    {
        return Attribute::get(fn () => sanitize_html($this->about));
    }

    public function rfp(): HasOne
    {
        return $this->hasOne(Rfp::class, 'tender_id');
    }

    public function matches(): HasMany
    {
        return $this->hasMany(TenderMatch::class);
    }

    public function invitedCompanies(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, TenderCompanyInvitation::TABLE)
            ->using(TenderCompanyInvitation::class)
            ->withPivot('sent_at', 'first_viewed_at')
            ->as('invitation');
    }
}
