<?php

namespace App\Models;

use App\Enums\Enterprise\SeniorityLevel;
use App\Enums\Enterprise\TechnologyType;
use App\Enums\Enterprise\WorkLocation;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class RfpPosition extends Model
{
    use HasUuids;

    protected $fillable = [
        'rfp_id',
        'job_title',
        'number_of_resources',
        'seniority_level',
        'workload',
        'rate_min',
        'rate_max',
        'work_location',
        'notes',
        'from_suggestion',
    ];

    protected function casts(): array
    {
        return [
            'number_of_resources' => 'integer',
            'seniority_level' => SeniorityLevel::class,
            'workload' => 'integer',
            'rate_min' => 'integer',
            'rate_max' => 'integer',
            'work_location' => WorkLocation::class,
            'from_suggestion' => 'boolean',
        ];
    }

    public function languages(): BelongsToMany
    {
        return $this->belongsToMany(Language::class, 'rfp_position_language');
    }

    public function technologies(): BelongsToMany
    {
        // TODO(<PERSON>k): Consider using scopes.
        return $this->belongsToMany(Technology::class, 'rfp_position_technology')
            ->withPivot('is_mandatory')
            ->where('type', TechnologyType::Technology);
    }

    public function tools(): BelongsToMany
    {
        // TODO(Marian Rusnak): Consider using scopes.
        return $this->belongsToMany(Technology::class, 'rfp_position_technology')
            ->withPivot('is_mandatory')
            ->where('type', TechnologyType::Tool);
    }

    public function rfp(): BelongsTo
    {
        return $this->belongsTo(Rfp::class);
    }
}
