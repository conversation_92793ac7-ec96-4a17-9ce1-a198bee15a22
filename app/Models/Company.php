<?php

namespace App\Models;

use App\Enums\CompanyCategory;
use App\Enums\Country;
use App\Enums\Enterprise\CompanyDataSource;
use App\Models\Traits\AutoSlugTrait;
use App\Models\Traits\ProtectedDeleteTrait;
use App\Models\Traits\PublicToUserTrait;
use App\Observers\CompanyObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\MassPrunable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Libs\Overseer\PublishStatus;
use Libs\Overseer\Reviewable;
use Libs\Warehouse\Resource;

##[ObservedBy([CompanyObserver::class])]
class Company extends Model
{
    use AutoSlugTrait;

    use MassPrunable;
    use ProtectedDeleteTrait;
    use PublicToUserTrait;
    use Reviewable;
    use SoftDeletes;

    protected $fillable = [
        'is_public',
        'name',
        'category',
        'logo_resource_id',
        'cover_resource_id',
        'owner',
        'hq',
        'country',
        'eu_vat',
        'website',
        'linkedin',
        'founded',
        'about',
        'is_vendor',
        'presentation_url',
        'data_source_type',
        'data_source_key'
    ];

    protected $casts = [
        'publish_status' => PublishStatus::class,
        'category' => CompanyCategory::class,
        'country' => Country::class,
        'data_source_type' => CompanyDataSource::class,
        'is_public' => 'boolean',
        'is_vendor' => 'boolean',
    ];

    protected array $dependentRelationships = [
        // FIXME: include relationships, including nested vendor ones
        'completed_tenders',
    ];

    protected array $reviewableRelationships = [
        'document_resources',
    ];

    protected string $slugIdField = 'public_id';

    protected string $slugSourceField = 'display_name';

    public function uniqueIds(): array
    {
        return ['uuid'];
    }

    public function vendor(): HasOne
    {
        return $this->hasOne(Vendor::class);
    }

    public function tenders(): HasMany
    {
        return $this->hasMany(Tender::class);
    }

    public function completed_tenders(): HasMany
    {
        return $this->tenders()->completed();
    }

    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function notes(): HasMany
    {
        return $this->hasMany(CompanyNote::class);
    }

    public function interested_bench_specialists(): BelongsToMany
    {
        return $this->belongsToMany(BenchSpecialist::class)
            ->using(BenchSpecialistCompany::class)
            ->withPivot('status', 'rejection_reason', 'client_note');
    }

    public function logo_resource(): BelongsTo
    {
        return $this->belongsTo(Resource::class);
    }

    public function cover_resource(): BelongsTo
    {
        return $this->belongsTo(Resource::class);
    }

    public function public_logo_resource(): BelongsTo
    {
        return $this->belongsTo(Resource::class);
    }

    public function public_cover_resource(): BelongsTo
    {
        return $this->belongsTo(Resource::class);
    }

    public function presentation_resource(): BelongsTo
    {
        return $this->belongsTo(Resource::class);
    }

    public function document_resources(): BelongsToMany
    {
        return $this->belongsToMany(Resource::class, 'company_document_resources')
            ->withTimestamps();
    }

    public function assignedCandidates(): HasManyThrough
    {
        return $this->hasManyThrough(Candidate::class, CandidateAssignment::class, 'company_id', 'id', 'id');
    }

    protected function displayName(): Attribute
    {
        return Attribute::get(function () {
            if ($this->is_public_to_user) {
                return $this->name;
            }

            return $this->is_vendor
                ? __('global.labels.partner_vendor')
                : __('global.labels.partner_client');
        });
    }

    protected function displayLogoResource(): Attribute
    {
        return Attribute::get(fn () => $this->logo_resource);
    }

    protected function displayCoverResource(): Attribute
    {
        return Attribute::get(fn () => $this->cover_resource);
    }

    protected function escapedAbout(): Attribute
    {
        return Attribute::get(fn () => strip_tags($this->about));
    }

    protected function escapedNotes(): Attribute
    {
        return Attribute::get(fn () => sanitize_html($this->notes));
    }

    public function scopeVendors(Builder $query): Builder
    {
        // FIXME: scope by flag?
        //  should we scope only active vendors, or vendors with profile?
        return $query->whereHas('vendor');
    }

    public function scopePublic(Builder $query): Builder
    {
        return $query->where('is_public', true);
    }

    public function prunable(): Builder
    {
        // Prunes
        // - all rejected company registrations older than day
        // - other rejected or approved changes
        return $this
            ->onlyTrashed()
            ->withUnapprovedChanges()
            ->awaitingApproval()
            ->where('updated_at', '<', now()->subDay());
    }

    public function workspaces(): BelongsToMany
    {
        return $this->belongsToMany(Workspace::class, 'workspace_company');
    }
}
