<?php

namespace App\Models;

use App\Enums\TenderCandidateStatus;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\AsCollection;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Support\Collection;

/**
 * @property Candidate $candidate
 * @property ?Company $company
 * @property ?Tender $tender
 * @property ?TenderPosition $tenderPosition
 * @property ?User $manager
 * @property ?CarbonInterface $start_date
 * @property ?CarbonInterface $end_date
 * @property TenderCandidateStatus $status
 * @property ?string $rejection_reason
 * @property ?string $client_note
 * @property ?array $matching_api_payload
 * @property ?Collection $matching_api_response
 * @property ?string $matching_api_response_status
 */
class CandidateAssignment extends Pivot
{
    const string TABLE = 'candidate_assignments';

    public $timestamps = false;

    protected $table = self::TABLE;
    protected $primaryKey = 'candidate_id';

    protected $fillable = [
        'candidate_id',
        'company_id',
        'tender_id',
        'tender_position_id',
        'manager_id',
        'start_date',
        'end_date',
        'status',
        'matching_api_payload',
        'matching_api_response',
        'matching_api_response_status',
    ];

    protected function casts(): array
    {
        return [
            'start_date' => 'date',
            'end_date' => 'date',
            'status' => TenderCandidateStatus::class,
            'matching_api_payload' => 'array',
            'matching_api_response' => AsCollection::class,
        ];
    }

    public function candidate(): BelongsTo
    {
        return $this->belongsTo(Candidate::class);
    }

    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class);
    }

    public function tender(): BelongsTo
    {
        return $this->belongsTo(Tender::class);
    }

    public function tenderPosition(): BelongsTo
    {
        return $this->belongsTo(TenderPosition::class);
    }

    public function manager(): BelongsTo
    {
        return $this->belongsTo(User::class, 'manager_id');
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->where(
            static function (Builder $query): void {
                $query->where('end_date', '>', now());
                $query->orWhereNull('end_date');
            })
            ->where('status', TenderCandidateStatus::Hired);
    }
}
