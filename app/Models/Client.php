<?php

namespace App\Models;

use App\Models\Traits\AutoSlugTrait;
use App\Models\Traits\ProtectedDeleteTrait;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;
use Libs\Warehouse\Resource;

/**
 * @property int $id
 * @property string $name
 * @property ?string $description
 * @property ?int $logo_resource_id
 * @property resource $logo_resource
 * @property EloquentCollection $vendors
 * @property EloquentCollection $solutions
 * @property EloquentCollection $tender_projects
 * @property CarbonInterface $created_at
 * @property CarbonInterface $updated_at
 * @property ?CarbonInterface $deleted_at
 */
class Client extends Model
{
    use AutoSlugTrait;
    use HasFactory;
    use ProtectedDeleteTrait;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'logo_resource_id',
    ];

    protected array $dependentRelationships = [
        'vendors',
        'solutions',
        'tender_projects',
    ];

    public function vendors(): HasManyThrough
    {
        return $this->hasManyThrough(Vendor::class, VendorClient::class, 'client_id', 'id');
    }

    public function solutions(): HasManyThrough
    {
        return $this->hasManyThrough(Solution::class, SolutionClient::class, 'client_id', 'id');
    }

    public function tender_projects(): HasMany
    {
        return $this->hasMany(TenderProject::class);
    }

    public function logo_resource(): BelongsTo
    {
        return $this->belongsTo(Resource::class);
    }
}
