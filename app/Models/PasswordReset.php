<?php

namespace App\Models;

use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\MassPrunable;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $email
 * @property string $token
 * @property CarbonInterface $expires_at
 * @property CarbonInterface $created_at
 */
class PasswordReset extends Model
{
    use MassPrunable;

    const UPDATED_AT = null;

    const TOKEN_LIFETIME_MINUTES = 120;

    public $incrementing = false;

    protected $primaryKey = 'token';

    protected $keyType = 'string';

    public static function createFor(string $email): self
    {
        return self::forceCreate([
            'email' => $email,
            'token' => str_random(64),
        ]);
    }

    public static function findBy(string $token): ?self
    {
        return self::query()
            ->where('token', $token)
            ->where('created_at', '>=', now()->subMinutes(self::TOKEN_LIFETIME_MINUTES))
            ->first();
    }

    protected function expiresAt(): Attribute
    {
        return Attribute::get(fn () => $this->created_at->addMinutes(self::TOKEN_LIFETIME_MINUTES));
    }

    public function prunable(): Builder
    {
        return $this->where('created_at', '<', now()->subMinutes(self::TOKEN_LIFETIME_MINUTES));
    }
}
