<?php

namespace App\Models;

use App\Enums\RateType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Workspace extends Model
{
    use HasUuids;
    use SoftDeletes;

    protected $fillable = [
        'name',
        'rate_type',
        'marketplace_disabled',
    ];

    protected $casts = [
        'rate_type' => RateType::class,
        'marketplace_disabled' => 'boolean',
    ];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_workspace');
    }

    public function companies(): BelongsToMany
    {
        return $this->belongsToMany(Company::class, 'workspace_company');
    }
}
