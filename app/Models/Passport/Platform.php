<?php

namespace App\Models\Passport;

use App\Enums\Platform as PlatformEnum;
use Illuminate\Database\Eloquent\Model;

class Platform extends Model
{
    protected $table = 'oauth_client_platforms';
    public    $incrementing = false;

    public $timestamps = false;
    protected $primaryKey = 'oauth_client_id';

    protected $fillable = [
        'oauth_client_id',
        'platform',
        'url',
    ];

    protected function casts(): array
    {
        return [
            'oauth_client_id' => 'string',
            'platform' => PlatformEnum::class,
            'url' => 'string',
        ];
    }
}
