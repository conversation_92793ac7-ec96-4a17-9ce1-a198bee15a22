<?php

namespace App\Rules\Enterprise;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class TenderRequestTextCacheId implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!cache()->has("TenderRequest.{$value}.INPUT")) {
            $fail("tender request's cached input does not exists.");
        }
    }
}
