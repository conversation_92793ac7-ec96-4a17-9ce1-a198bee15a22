<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class YouTubeVideoId implements Rule
{
    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     */
    public function passes($attribute, $value): bool
    {
        $id = urlencode((string) $value);
        $headers = get_headers("https://www.youtube.com/oembed?format=json&url=http://www.youtube.com/watch?v=$id");

        return is_array($headers) && preg_match('/^HTTP\\/\\d+\\.\\d+\\s+2\\d\\d\\s+.*$/', $headers[0]);
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        return __('validation.youtube_video_id');
    }
}
