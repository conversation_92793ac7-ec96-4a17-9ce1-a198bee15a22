<?php

namespace App\View\Components;

use Illuminate\View\Component;
use Illuminate\View\View;

class AuthLayout extends Component
{


    public function __construct(public ?string $title,public ?bool $hideHeader,public ?bool $hideFooter)
    {
        $this->title = ($title ? $title .' | ' : '') . "Nordics Platform";
    }
    /**
     * Get the view / contents that represents the component.
     */
    public function render(): View
    {
        return view('layouts.auth');
    }
}