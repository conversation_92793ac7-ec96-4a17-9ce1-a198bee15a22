<?php

return [
    'rejected' => [
        'subject' => 'Changes to your :entity were rejected',
        'message' => 'We are sorry, but changes to your :entity have been rejected. We will get in touch with you shortly to figure out what we can do to satisfy your request.',
    ],

    'approved' => [
        'subject' => 'We have approved changes to your :entity',
        'message' => 'Your requested changes to the :entity have been reviewed and approved. The updated information is now reflected on the platform.',
    ],

    'requested_change' => [
        'subject' => "Company ':company' requested change to their :entity",
        'message' => "Company ':company' have just requested change to their :entity. You may review their request in the administration section of the platform by clicking on the button below.",
        'action' => 'Review changes',
    ],

    'entities' => [
        'company' => 'company profile',
        'vendor' => 'vendor profile',
        'solution' => "solution ':name'",
        'benchspecialist' => "bench specialist ':name'",
    ],

    'entities_admin_url' => [
        'company' => '/admin/companies/:id',
        'vendor' => '/admin/companies/:id/vendor',
        'solution' => '/admin/solutions/:id',
        'benchspecialist' => '/admin/bench-specialists/:id',
    ],
];
