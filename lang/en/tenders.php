<?php

return [
    'new_candidate_application' => [
        'subject' => "New candidate application in the platform for tender ':tender'",
        'message' => "Company ':company' has submitted an application for candidate ':candidate' for position ':position' in tender ':tender'. You can now approve or reject it by clicking on the button below.",
        'action' => 'Review candidate',
    ],
    'new_candidate_approved' => [
        'subject' => "New candidate application in the platform for tender :tender",
        'message' => "Company <strong>:company</strong> has submitted an application for candidate <strong>:candidate</strong> for position <strong>:position</strong> in tender <strong>:tender</strong>.",
        'action'  => 'View tender',
    ],

    'candidate_status_updated' => [
        'subject' => "Application status of candidate ':candidate' for tender ':tender' has been changed",
        'message' => "We wanted to let you know that status of your candidate ':candidate' for position ':position' in tender ':tender' has changed to: ':status'. Please view candidate's application for more details.",
        'action' => 'View applications',
    ],

    'new_tender_available' => [
        'subject' => 'New tender is available for you',
        'message' => "We have a new tender ':tender' available that fits your vendor profile details. The deadline to apply is set for ':deadline'. For more information and to apply please click on the button below.",
        'action' => 'View tender',
    ],

    'candidate_states' => [
        'awaiting_approval' => 'Awaiting approval',
        'rejected' => 'Rejected',
        'approved' => 'Approved',
        'approved_client' => 'Awaiting evaluation',
        'not_interested' => 'Not interested',
        'interested' => 'Interested',
        'invited_to_interview' => 'Invited to interview',
        'interviewed_not_interested' => 'Interviewed, not interested',
        'interviewed_not_hired' => 'Interviewed, not hired',
        'interviewed_interested' => 'Interviewed, interested',
        'hired' => 'Hired',
        'not_hired' => 'Not hired',
    ],
];
