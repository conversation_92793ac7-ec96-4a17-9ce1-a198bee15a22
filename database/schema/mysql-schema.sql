/*!999999\- enable the sandbox mode */ 
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
DROP TABLE IF EXISTS `bench_specialist_company`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `bench_specialist_company` (
  `bench_specialist_id` bigint(20) unsigned NOT NULL,
  `company_id` bigint(20) unsigned NOT NULL,
  `status` varchar(191) NOT NULL DEFAULT 'invited_to_interview',
  `rejection_reason` varchar(500) DEFAULT NULL,
  `client_note` varchar(500) DEFAULT NULL,
  KEY `bench_specialist_company_bench_specialist_id_foreign` (`bench_specialist_id`),
  KEY `bench_specialist_company_company_id_foreign` (`company_id`),
  CONSTRAINT `bench_specialist_company_bench_specialist_id_foreign` FOREIGN KEY (`bench_specialist_id`) REFERENCES `bench_specialists` (`id`) ON DELETE CASCADE,
  CONSTRAINT `bench_specialist_company_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `bench_specialists`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `bench_specialists` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `public_id` varchar(6) NOT NULL,
  `candidate_id` bigint(20) unsigned NOT NULL,
  `merge_into_id` bigint(20) unsigned DEFAULT NULL,
  `publish_status` varchar(191) NOT NULL,
  `available_from` timestamp NULL DEFAULT NULL,
  `available_to` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `bench_specialists_public_id_unique` (`public_id`),
  KEY `bench_specialists_candidate_id_foreign` (`candidate_id`),
  KEY `bench_specialists_merge_into_id_foreign` (`merge_into_id`),
  CONSTRAINT `bench_specialists_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates` (`id`) ON DELETE CASCADE,
  CONSTRAINT `bench_specialists_merge_into_id_foreign` FOREIGN KEY (`merge_into_id`) REFERENCES `bench_specialists` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cache` (
  `key` varchar(191) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `cache_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `cache_locks` (
  `key` varchar(191) NOT NULL,
  `owner` varchar(191) NOT NULL,
  `expiration` int(11) NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `candidate_experience_candidate_skill`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidate_experience_candidate_skill` (
  `candidate_experience_id` bigint(20) unsigned NOT NULL,
  `candidate_skill_id` bigint(20) unsigned NOT NULL,
  KEY `candidate_experience_skill_experience_id_foreign` (`candidate_experience_id`),
  KEY `candidate_experience_skill_skill_id_foreign` (`candidate_skill_id`),
  CONSTRAINT `candidate_experience_skill_experience_id_foreign` FOREIGN KEY (`candidate_experience_id`) REFERENCES `candidate_experiences` (`id`) ON DELETE CASCADE,
  CONSTRAINT `candidate_experience_skill_skill_id_foreign` FOREIGN KEY (`candidate_skill_id`) REFERENCES `candidate_skills` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `candidate_experiences`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidate_experiences` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `public_id` varchar(6) NOT NULL,
  `candidate_id` bigint(20) unsigned NOT NULL,
  `order` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` mediumtext NOT NULL,
  `length_type` varchar(191) NOT NULL,
  `length` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `candidate_experiences_candidate_id_foreign` (`candidate_id`),
  CONSTRAINT `candidate_experiences_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `candidate_skills`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidate_skills` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `candidate_id` bigint(20) unsigned NOT NULL,
  `technology_id` bigint(20) unsigned NOT NULL,
  `years_of_experience` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `candidate_skills_candidate_id_foreign` (`candidate_id`),
  KEY `candidate_skills_technology_id_foreign` (`technology_id`),
  CONSTRAINT `candidate_skills_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates` (`id`) ON DELETE CASCADE,
  CONSTRAINT `candidate_skills_technology_id_foreign` FOREIGN KEY (`technology_id`) REFERENCES `technologies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `candidate_tender_position`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidate_tender_position` (
  `candidate_id` bigint(20) unsigned NOT NULL,
  `tender_position_id` bigint(20) unsigned NOT NULL,
  `status` varchar(191) NOT NULL DEFAULT 'awaiting_approval',
  `rejection_reason` varchar(500) DEFAULT NULL,
  `client_note` varchar(500) DEFAULT NULL,
  KEY `candidate_tender_position_candidate_id_foreign` (`candidate_id`),
  KEY `candidate_tender_position_tender_position_id_foreign` (`tender_position_id`),
  CONSTRAINT `candidate_tender_position_candidate_id_foreign` FOREIGN KEY (`candidate_id`) REFERENCES `candidates` (`id`) ON DELETE CASCADE,
  CONSTRAINT `candidate_tender_position_tender_position_id_foreign` FOREIGN KEY (`tender_position_id`) REFERENCES `tender_positions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `candidates`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `candidates` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `public_id` varchar(6) NOT NULL,
  `vendor_id` bigint(20) unsigned NOT NULL,
  `origin_id` bigint(20) unsigned DEFAULT NULL,
  `cv_resource_id` bigint(20) unsigned DEFAULT NULL,
  `cv_parsed_raw_data` mediumtext DEFAULT NULL,
  `clone` tinyint(1) NOT NULL DEFAULT 0,
  `finished` tinyint(1) NOT NULL DEFAULT 1,
  `internal_name` varchar(50) NOT NULL,
  `name` varchar(50) NOT NULL,
  `profession` varchar(50) DEFAULT NULL,
  `seniority` varchar(191) DEFAULT NULL,
  `rate` int(10) unsigned DEFAULT NULL,
  `last_job_title` varchar(50) DEFAULT NULL,
  `years_of_experience` int(11) DEFAULT NULL,
  `highest_education` varchar(191) DEFAULT NULL,
  `field_of_study` varchar(50) DEFAULT NULL,
  `country` varchar(191) DEFAULT NULL,
  `city` varchar(50) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `candidates_vendor_id_foreign` (`vendor_id`),
  KEY `candidates_origin_id_foreign` (`origin_id`),
  KEY `candidates_cv_resource_id_foreign` (`cv_resource_id`),
  KEY `candidates_clone_index` (`clone`),
  KEY `candidates_finished_index` (`finished`),
  KEY `candidates_public_id_index` (`public_id`),
  CONSTRAINT `candidates_cv_resource_id_foreign` FOREIGN KEY (`cv_resource_id`) REFERENCES `resources` (`id`) ON DELETE SET NULL,
  CONSTRAINT `candidates_origin_id_foreign` FOREIGN KEY (`origin_id`) REFERENCES `candidates` (`id`) ON DELETE SET NULL,
  CONSTRAINT `candidates_vendor_id_foreign` FOREIGN KEY (`vendor_id`) REFERENCES `vendors` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `clients` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `description` text DEFAULT NULL,
  `logo_resource_id` bigint(20) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `clients_logo_resource_id_foreign` (`logo_resource_id`),
  CONSTRAINT `clients_logo_resource_id_foreign` FOREIGN KEY (`logo_resource_id`) REFERENCES `resources` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `companies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `companies` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `public_id` varchar(6) NOT NULL,
  `publish_status` varchar(191) NOT NULL DEFAULT 'published',
  `merge_into_id` bigint(20) unsigned DEFAULT NULL,
  `is_public` tinyint(1) NOT NULL DEFAULT 0,
  `is_vendor` tinyint(1) NOT NULL DEFAULT 0,
  `name` varchar(100) NOT NULL,
  `logo_resource_id` bigint(20) unsigned DEFAULT NULL,
  `cover_resource_id` bigint(20) unsigned DEFAULT NULL,
  `owner` varchar(191) NOT NULL,
  `hq` varchar(191) NOT NULL,
  `country` varchar(191) NOT NULL,
  `eu_vat` varchar(191) DEFAULT NULL,
  `website` varchar(255) NOT NULL,
  `linkedin` varchar(255) NOT NULL,
  `founded` int(10) unsigned NOT NULL,
  `about` mediumtext DEFAULT NULL,
  `notes` mediumtext DEFAULT NULL,
  `presentation_resource_id` bigint(20) unsigned DEFAULT NULL,
  `presentation_url` varchar(191) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `companies_public_id_unique` (`public_id`),
  KEY `companies_merge_into_id_foreign` (`merge_into_id`),
  KEY `companies_cover_resource_id_foreign` (`cover_resource_id`),
  KEY `companies_logo_resource_id_foreign` (`logo_resource_id`),
  KEY `companies_presentation_resource_id_foreign` (`presentation_resource_id`),
  CONSTRAINT `companies_cover_resource_id_foreign` FOREIGN KEY (`cover_resource_id`) REFERENCES `resources` (`id`),
  CONSTRAINT `companies_logo_resource_id_foreign` FOREIGN KEY (`logo_resource_id`) REFERENCES `resources` (`id`),
  CONSTRAINT `companies_merge_into_id_foreign` FOREIGN KEY (`merge_into_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `companies_presentation_resource_id_foreign` FOREIGN KEY (`presentation_resource_id`) REFERENCES `resources` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `company_notes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `company_notes` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint(20) unsigned NOT NULL,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `note` mediumtext DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `company_notes_company_id_foreign` (`company_id`),
  KEY `company_notes_user_id_foreign` (`user_id`),
  CONSTRAINT `company_notes_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_notes_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `employee_position_vendor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employee_position_vendor` (
  `employee_position_id` bigint(20) unsigned NOT NULL,
  `vendor_id` bigint(20) unsigned NOT NULL,
  KEY `employee_position_vendor_employee_position_id_foreign` (`employee_position_id`),
  KEY `employee_position_vendor_vendor_id_foreign` (`vendor_id`),
  CONSTRAINT `employee_position_vendor_employee_position_id_foreign` FOREIGN KEY (`employee_position_id`) REFERENCES `employee_positions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `employee_position_vendor_vendor_id_foreign` FOREIGN KEY (`vendor_id`) REFERENCES `vendors` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `employee_positions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `employee_positions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(191) NOT NULL,
  `name_plural` varchar(191) NOT NULL,
  `hireable` tinyint(1) NOT NULL DEFAULT 0,
  `seniorities` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `failed_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(191) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `industries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `industries` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `featured` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `industries_parent_id_foreign` (`parent_id`),
  CONSTRAINT `industries_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `industries` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `industry_solution`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `industry_solution` (
  `industry_id` bigint(20) unsigned NOT NULL,
  `solution_id` bigint(20) unsigned NOT NULL,
  KEY `industry_solution_industry_id_foreign` (`industry_id`),
  KEY `industry_solution_solution_id_foreign` (`solution_id`),
  CONSTRAINT `industry_solution_industry_id_foreign` FOREIGN KEY (`industry_id`) REFERENCES `industries` (`id`) ON DELETE CASCADE,
  CONSTRAINT `industry_solution_solution_id_foreign` FOREIGN KEY (`solution_id`) REFERENCES `solutions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `industry_tender_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `industry_tender_project` (
  `industry_id` bigint(20) unsigned NOT NULL,
  `tender_project_id` bigint(20) unsigned NOT NULL,
  KEY `industry_tender_project_industry_id_foreign` (`industry_id`),
  KEY `industry_tender_project_tender_project_id_foreign` (`tender_project_id`),
  CONSTRAINT `industry_tender_project_industry_id_foreign` FOREIGN KEY (`industry_id`) REFERENCES `industries` (`id`) ON DELETE CASCADE,
  CONSTRAINT `industry_tender_project_tender_project_id_foreign` FOREIGN KEY (`tender_project_id`) REFERENCES `tender_projects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `industry_vendor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `industry_vendor` (
  `industry_id` bigint(20) unsigned NOT NULL,
  `vendor_id` bigint(20) unsigned NOT NULL,
  KEY `industry_vendor_industry_id_foreign` (`industry_id`),
  KEY `industry_vendor_vendor_id_foreign` (`vendor_id`),
  CONSTRAINT `industry_vendor_industry_id_foreign` FOREIGN KEY (`industry_id`) REFERENCES `industries` (`id`) ON DELETE CASCADE,
  CONSTRAINT `industry_vendor_vendor_id_foreign` FOREIGN KEY (`vendor_id`) REFERENCES `vendors` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(191) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) unsigned NOT NULL,
  `reserved_at` int(10) unsigned DEFAULT NULL,
  `available_at` int(10) unsigned NOT NULL,
  `created_at` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(191) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications` (
  `id` char(36) NOT NULL,
  `type` varchar(191) NOT NULL,
  `notifiable_type` varchar(191) NOT NULL,
  `notifiable_id` bigint(20) unsigned NOT NULL,
  `data` text NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `password_resets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `password_resets` (
  `email` varchar(191) NOT NULL,
  `token` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  KEY `password_resets_email_index` (`email`),
  KEY `password_resets_token_index` (`token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `public_id` varchar(6) NOT NULL,
  `tokenable_type` varchar(191) NOT NULL,
  `tokenable_id` bigint(20) unsigned NOT NULL,
  `name` varchar(191) NOT NULL,
  `token` varchar(64) NOT NULL,
  `remember` tinyint(1) NOT NULL DEFAULT 0,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`),
  KEY `personal_access_tokens_public_id_index` (`public_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `resources`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `resources` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `type` varchar(191) NOT NULL,
  `public_id` varchar(6) NOT NULL,
  `hash` varchar(191) NOT NULL,
  `name` varchar(191) NOT NULL,
  `disk` varchar(191) NOT NULL,
  `directory` varchar(191) NOT NULL,
  `filename` varchar(191) NOT NULL,
  `path` varchar(191) NOT NULL,
  `extra` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`extra`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `resources_public_id_unique` (`public_id`),
  KEY `resources_user_id_foreign` (`user_id`),
  KEY `resources_hash_index` (`hash`),
  CONSTRAINT `resources_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `sessions` (
  `id` varchar(191) NOT NULL,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` text NOT NULL,
  `last_activity` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `solution_clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `solution_clients` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `solution_id` bigint(20) unsigned NOT NULL,
  `anonymous` tinyint(1) NOT NULL,
  `client_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(150) DEFAULT NULL,
  `review` text DEFAULT NULL,
  `reviewer` varchar(150) DEFAULT NULL,
  `reviewer_position` varchar(150) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `solution_clients_solution_id_foreign` (`solution_id`),
  KEY `solution_clients_client_id_foreign` (`client_id`),
  CONSTRAINT `solution_clients_client_id_foreign` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE SET NULL,
  CONSTRAINT `solution_clients_solution_id_foreign` FOREIGN KEY (`solution_id`) REFERENCES `solutions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `solution_media`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `solution_media` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `public_id` varchar(6) NOT NULL,
  `solution_id` bigint(20) unsigned NOT NULL,
  `order` int(11) NOT NULL DEFAULT 0,
  `name` varchar(150) DEFAULT NULL,
  `type` varchar(191) NOT NULL,
  `resource_id` bigint(20) unsigned NOT NULL,
  `source` varchar(191) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `solution_media_solution_id_foreign` (`solution_id`),
  KEY `solution_media_resource_id_foreign` (`resource_id`),
  KEY `solution_media_public_id_index` (`public_id`),
  CONSTRAINT `solution_media_resource_id_foreign` FOREIGN KEY (`resource_id`) REFERENCES `resources` (`id`) ON DELETE CASCADE,
  CONSTRAINT `solution_media_solution_id_foreign` FOREIGN KEY (`solution_id`) REFERENCES `solutions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `solution_technology`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `solution_technology` (
  `solution_id` bigint(20) unsigned NOT NULL,
  `technology_id` bigint(20) unsigned NOT NULL,
  KEY `solution_technology_solution_id_foreign` (`solution_id`),
  KEY `solution_technology_technology_id_foreign` (`technology_id`),
  CONSTRAINT `solution_technology_solution_id_foreign` FOREIGN KEY (`solution_id`) REFERENCES `solutions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `solution_technology_technology_id_foreign` FOREIGN KEY (`technology_id`) REFERENCES `technologies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `solutions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `solutions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `public_id` varchar(6) NOT NULL,
  `vendor_id` bigint(20) unsigned NOT NULL,
  `publish_status` varchar(191) NOT NULL,
  `merge_into_id` bigint(20) unsigned DEFAULT NULL,
  `origin` varchar(191) NOT NULL DEFAULT 'platform',
  `origin_id` varchar(191) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `description` varchar(150) NOT NULL,
  `about` mediumtext NOT NULL,
  `cover_resource_id` bigint(20) unsigned DEFAULT NULL,
  `country` varchar(191) DEFAULT NULL,
  `main_industry_id` bigint(20) unsigned DEFAULT NULL,
  `length_type` varchar(191) NOT NULL,
  `length` int(11) DEFAULT NULL,
  `ftes` int(11) DEFAULT NULL,
  `value` int(11) DEFAULT NULL,
  `in_house` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `solutions_public_id_unique` (`public_id`),
  KEY `solutions_vendor_id_foreign` (`vendor_id`),
  KEY `solutions_main_industry_id_index` (`main_industry_id`),
  KEY `solutions_cover_resource_id_foreign` (`cover_resource_id`),
  KEY `solutions_merge_into_id_foreign` (`merge_into_id`),
  KEY `solutions_origin_index` (`origin`),
  FULLTEXT KEY `solutions_name_fulltext` (`name`),
  CONSTRAINT `solutions_cover_resource_id_foreign` FOREIGN KEY (`cover_resource_id`) REFERENCES `resources` (`id`),
  CONSTRAINT `solutions_merge_into_id_foreign` FOREIGN KEY (`merge_into_id`) REFERENCES `solutions` (`id`) ON DELETE CASCADE,
  CONSTRAINT `solutions_vendor_id_foreign` FOREIGN KEY (`vendor_id`) REFERENCES `vendors` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `technologies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `technologies` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `emsi_id` varchar(191) DEFAULT NULL,
  `parent_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `logo_resource_id` bigint(20) unsigned DEFAULT NULL,
  `featured` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `technologies_parent_id_foreign` (`parent_id`),
  KEY `technologies_logo_resource_id_foreign` (`logo_resource_id`),
  CONSTRAINT `technologies_logo_resource_id_foreign` FOREIGN KEY (`logo_resource_id`) REFERENCES `resources` (`id`),
  CONSTRAINT `technologies_parent_id_foreign` FOREIGN KEY (`parent_id`) REFERENCES `technologies` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `technology_tender_position`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `technology_tender_position` (
  `technology_id` bigint(20) unsigned NOT NULL,
  `tender_position_id` bigint(20) unsigned NOT NULL,
  KEY `technology_tender_position_technology_id_foreign` (`technology_id`),
  KEY `technology_tender_position_tender_position_id_foreign` (`tender_position_id`),
  CONSTRAINT `technology_tender_position_technology_id_foreign` FOREIGN KEY (`technology_id`) REFERENCES `technologies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `technology_tender_position_tender_position_id_foreign` FOREIGN KEY (`tender_position_id`) REFERENCES `tender_positions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `technology_tender_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `technology_tender_project` (
  `technology_id` bigint(20) unsigned NOT NULL,
  `tender_project_id` bigint(20) unsigned NOT NULL,
  KEY `technology_tender_project_technology_id_foreign` (`technology_id`),
  KEY `technology_tender_project_tender_project_id_foreign` (`tender_project_id`),
  CONSTRAINT `technology_tender_project_technology_id_foreign` FOREIGN KEY (`technology_id`) REFERENCES `technologies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `technology_tender_project_tender_project_id_foreign` FOREIGN KEY (`tender_project_id`) REFERENCES `tender_projects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `technology_vendor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `technology_vendor` (
  `technology_id` bigint(20) unsigned NOT NULL,
  `vendor_id` bigint(20) unsigned NOT NULL,
  KEY `technology_vendor_technology_id_foreign` (`technology_id`),
  KEY `technology_vendor_vendor_id_foreign` (`vendor_id`),
  CONSTRAINT `technology_vendor_technology_id_foreign` FOREIGN KEY (`technology_id`) REFERENCES `technologies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `technology_vendor_vendor_id_foreign` FOREIGN KEY (`vendor_id`) REFERENCES `vendors` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `tender_position_seniorities`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tender_position_seniorities` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tender_position_id` bigint(20) unsigned NOT NULL,
  `seniority` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `tender_position_seniorities_tender_position_id_foreign` (`tender_position_id`),
  CONSTRAINT `tender_position_seniorities_tender_position_id_foreign` FOREIGN KEY (`tender_position_id`) REFERENCES `tender_positions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `tender_positions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tender_positions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `public_id` varchar(6) NOT NULL,
  `tender_id` bigint(20) unsigned NOT NULL,
  `employee_position_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `description` text NOT NULL,
  `requirements` text NOT NULL,
  `price` int(11) DEFAULT NULL,
  `price_to` int(11) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `length_type` varchar(191) NOT NULL,
  `length` int(11) DEFAULT NULL,
  `possible_extension` tinyint(1) NOT NULL,
  `workload` int(11) NOT NULL,
  `count` tinyint(1) NOT NULL DEFAULT 1,
  `interview` varchar(150) DEFAULT NULL,
  `equipment` varchar(150) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `tender_positions_tender_id_foreign` (`tender_id`),
  KEY `tender_positions_employee_position_id_foreign` (`employee_position_id`),
  KEY `tender_positions_public_id_index` (`public_id`),
  CONSTRAINT `tender_positions_employee_position_type_id_foreign` FOREIGN KEY (`employee_position_id`) REFERENCES `employee_positions` (`id`) ON DELETE SET NULL,
  CONSTRAINT `tender_positions_tender_id_foreign` FOREIGN KEY (`tender_id`) REFERENCES `tenders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `tender_projects`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tender_projects` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `tender_id` bigint(20) unsigned NOT NULL,
  `main_industry_id` bigint(20) unsigned DEFAULT NULL,
  `in_house` tinyint(1) NOT NULL,
  `anonymous` tinyint(1) NOT NULL,
  `name` varchar(100) DEFAULT NULL,
  `about` mediumtext DEFAULT NULL,
  `client_id` bigint(20) unsigned DEFAULT NULL,
  `client_name` varchar(150) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `tender_projects_tender_id_foreign` (`tender_id`),
  KEY `tender_projects_main_industry_id_foreign` (`main_industry_id`),
  KEY `tender_projects_client_id_foreign` (`client_id`),
  CONSTRAINT `tender_projects_client_id_foreign` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE SET NULL,
  CONSTRAINT `tender_projects_main_industry_id_foreign` FOREIGN KEY (`main_industry_id`) REFERENCES `industries` (`id`) ON DELETE SET NULL,
  CONSTRAINT `tender_projects_tender_id_foreign` FOREIGN KEY (`tender_id`) REFERENCES `tenders` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `tender_vendor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tender_vendor` (
  `tender_id` bigint(20) unsigned NOT NULL,
  `vendor_id` bigint(20) unsigned NOT NULL,
  `allowed` tinyint(1) NOT NULL DEFAULT 1,
  `notified` tinyint(1) NOT NULL DEFAULT 0,
  KEY `tender_vendor_tender_id_foreign` (`tender_id`),
  KEY `tender_vendor_vendor_id_foreign` (`vendor_id`),
  CONSTRAINT `tender_vendor_tender_id_foreign` FOREIGN KEY (`tender_id`) REFERENCES `tenders` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tender_vendor_vendor_id_foreign` FOREIGN KEY (`vendor_id`) REFERENCES `vendors` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `tenders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tenders` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `public_id` varchar(6) NOT NULL,
  `company_id` bigint(20) unsigned NOT NULL,
  `anonymous_company` tinyint(1) NOT NULL,
  `publish_status` varchar(191) NOT NULL,
  `status` varchar(191) NOT NULL,
  `submissions_deadline` timestamp NOT NULL DEFAULT current_timestamp(),
  `end_of_incubation` timestamp NULL DEFAULT NULL,
  `processing_type` varchar(191) NOT NULL,
  `service_type` varchar(191) NOT NULL,
  `evaluation_date` timestamp NULL DEFAULT NULL,
  `payment_type` varchar(191) DEFAULT NULL,
  `price` int(11) DEFAULT NULL,
  `price_to` int(11) DEFAULT NULL,
  `length_type` varchar(191) NOT NULL,
  `length` int(11) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `description` varchar(150) NOT NULL,
  `about` mediumtext NOT NULL,
  `cover_resource_id` bigint(20) unsigned NOT NULL,
  `country` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `tenders_public_id_unique` (`public_id`),
  KEY `tenders_company_id_foreign` (`company_id`),
  KEY `tenders_cover_resource_id_foreign` (`cover_resource_id`),
  CONSTRAINT `tenders_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tenders_cover_resource_id_foreign` FOREIGN KEY (`cover_resource_id`) REFERENCES `resources` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(191) NOT NULL,
  `surname` varchar(191) NOT NULL,
  `email` varchar(191) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `type` varchar(191) NOT NULL,
  `role` varchar(191) NOT NULL,
  `position` varchar(191) DEFAULT NULL,
  `phone` varchar(191) DEFAULT NULL,
  `avatar_resource_id` bigint(20) unsigned DEFAULT NULL,
  `password` varchar(191) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`),
  KEY `users_company_id_foreign` (`company_id`),
  KEY `users_avatar_resource_id_foreign` (`avatar_resource_id`),
  CONSTRAINT `users_avatar_resource_id_foreign` FOREIGN KEY (`avatar_resource_id`) REFERENCES `resources` (`id`),
  CONSTRAINT `users_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vendor_clients`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vendor_clients` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `vendor_id` bigint(20) unsigned NOT NULL,
  `client_id` bigint(20) unsigned DEFAULT NULL,
  `name` varchar(191) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `vendor_clients_vendor_id_foreign` (`vendor_id`),
  KEY `vendor_clients_client_id_foreign` (`client_id`),
  CONSTRAINT `vendor_clients_client_id_foreign` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`) ON DELETE CASCADE,
  CONSTRAINT `vendor_clients_vendor_id_foreign` FOREIGN KEY (`vendor_id`) REFERENCES `vendors` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
DROP TABLE IF EXISTS `vendors`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vendors` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `public_id` varchar(6) NOT NULL,
  `company_id` bigint(20) unsigned NOT NULL,
  `publish_status` varchar(191) NOT NULL DEFAULT 'published',
  `merge_into_id` bigint(20) unsigned DEFAULT NULL,
  `main_industry_id` bigint(20) unsigned DEFAULT NULL,
  `employees` int(10) unsigned NOT NULL DEFAULT 0,
  `developers` int(10) unsigned NOT NULL DEFAULT 0,
  `offering_resources` tinyint(1) NOT NULL,
  `offering_solutions` tinyint(1) NOT NULL,
  `payment_time_and_material` tinyint(1) NOT NULL,
  `payment_fixed_price` tinyint(1) NOT NULL,
  `notify_irrelevant_offers` tinyint(1) NOT NULL DEFAULT 0,
  `rate_junior` int(10) unsigned DEFAULT NULL,
  `rate_medior` int(10) unsigned DEFAULT NULL,
  `rate_senior` int(10) unsigned DEFAULT NULL,
  `rate_lead` int(10) unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `vendors_public_id_unique` (`public_id`),
  KEY `vendors_company_id_foreign` (`company_id`),
  KEY `vendors_main_industry_id_foreign` (`main_industry_id`),
  KEY `vendors_merge_into_id_foreign` (`merge_into_id`),
  CONSTRAINT `vendors_company_id_foreign` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `vendors_main_industry_id_foreign` FOREIGN KEY (`main_industry_id`) REFERENCES `industries` (`id`) ON DELETE SET NULL,
  CONSTRAINT `vendors_merge_into_id_foreign` FOREIGN KEY (`merge_into_id`) REFERENCES `vendors` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

/*!999999\- enable the sandbox mode */ 
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (1,'2014_10_12_000000_create_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (2,'2014_10_12_100000_create_password_resets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (3,'2019_08_19_000000_create_failed_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (4,'2019_12_14_000001_create_personal_access_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (5,'2021_11_08_123018_create_cache_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (6,'2021_11_08_123034_create_sessions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (7,'2021_11_08_123221_create_jobs_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (8,'2021_11_08_123359_create_notifications_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (9,'2021_11_11_091702_create_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (10,'2021_11_11_091857_create_industries_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (11,'2021_11_11_091907_create_vendors_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (12,'2021_11_11_093549_create_industry_vendor_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (13,'2021_11_11_093719_create_clients_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (14,'2021_11_11_093845_create_vendor_clients_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (15,'2021_11_11_094338_create_employee_position_types_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (16,'2021_11_11_094348_create_employee_positions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (17,'2021_11_11_115623_create_technologies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (18,'2021_11_11_115653_create_technology_vendor_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (19,'2021_12_03_102418_create_solutions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (20,'2021_12_03_103322_create_solution_media_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (21,'2021_12_03_105948_create_solution_technology_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (22,'2021_12_06_003004_create_solution_clients_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (23,'2021_12_10_144704_add_publish_status_to_vendors_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (24,'2022_01_04_091513_make_eu_vat_nullable_in_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (25,'2022_01_26_135427_create_tenders_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (26,'2022_01_26_142345_create_tender_projects_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (27,'2022_01_26_144640_create_industry_tender_project_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (28,'2022_01_27_080251_create_technology_tender_project_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (29,'2022_01_31_125255_create_tender_positions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (30,'2022_01_31_132819_create_tender_position_seniorities_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (31,'2022_02_01_162920_create_technology_tender_position_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (32,'2022_02_23_131121_change_offerings_in_vendors_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (33,'2022_03_10_134301_add_public_id_remember_to_personal_access_tokens_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (34,'2022_03_10_134619_extend_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (35,'2022_03_16_171217_add_avatar_to_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (36,'2022_03_18_020806_add_company_id_to_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (37,'2022_03_19_164713_add_soft_deletes_to_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (38,'2022_03_28_153538_change_industry_id_to_main_industry_id_in_solutions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (39,'2022_03_28_153611_create_industry_solution_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (40,'2022_03_29_085709_add_index_to_main_industry_id_in_solutions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (41,'2022_04_08_114412_add_index_to_token_in_password_resets_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (42,'2022_04_27_003819_add_in_house_to_solutions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (43,'2022_04_29_161128_add_is_vendor_to_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (44,'2022_05_01_005106_remove_clients_from_industry_vendor_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (45,'2022_05_03_145035_drop_employee_positions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (46,'2022_05_03_145116_rename_employee_position_types_to_employee_positions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (47,'2022_05_03_145800_rename_employee_position_type_id_in_tender_positions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (48,'2022_05_03_155629_create_employee_position_vendor_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (49,'2022_05_04_005739_add_rates_to_vendors_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (50,'2022_05_05_163525_add_publish_status_and_merge_to_columns_to_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (51,'2022_05_16_172312_add_merge_into_id_to_vendors_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (52,'2022_05_31_154901_fix_deletion_in_vendor_clients_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (53,'2022_06_04_132305_add_phone_to_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (54,'2022_06_06_131517_add_position_to_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (55,'2022_06_09_114427_add_name_fulltext_index_to_solutions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (56,'2022_06_20_101912_add_notes_to_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (57,'2022_06_20_120358_create_company_notes_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (58,'2022_06_22_085109_create_tender_vendor_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (59,'2022_07_04_131808_create_resources_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (60,'2022_07_07_170634_migrate_images_to_resources_in_solution_media_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (61,'2022_07_12_130148_migrate_images_to_resources_in_solutions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (62,'2022_07_19_103803_add_order_to_solution_media_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (63,'2022_07_20_131226_add_merge_into_id_to_solutions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (64,'2022_08_01_155521_add_public_id_to_solution_media_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (65,'2022_08_04_115851_make_name_optional_in_solution_media_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (66,'2022_08_04_124933_migrate_images_to_resources_in_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (67,'2022_08_09_024241_add_presentations_to_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (68,'2022_08_11_172227_merge_presentations_in_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (69,'2022_08_17_162134_migrate_images_to_resources_in_users_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (70,'2022_08_18_122117_fix_submissions_deadline_in_tenders_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (71,'2022_08_18_125946_migrate_images_to_resources_in_tenders_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (72,'2022_08_18_141733_migrate_images_to_resources_in_technologies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (73,'2022_08_18_145735_migrate_images_to_resources_in_clients_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (74,'2022_08_22_125205_create_candidates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (75,'2022_08_22_131903_create_candidate_skills_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (76,'2022_08_22_134529_create_candidate_tender_position_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (77,'2022_08_31_220349_add_emsi_id_to_technologies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (78,'2022_09_06_150333_add_presentation_url_to_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (79,'2022_09_09_114908_add_public_id_to_tender_positions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (80,'2022_09_13_105519_add_seniority_and_rate_to_candidates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (81,'2022_09_26_003358_add_public_id_index_to_candidates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (82,'2022_10_01_192643_fix_note_column_in_company_notes_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (83,'2023_01_31_104535_enlarge_cv_parsed_raw_data_in_candidates_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (84,'2023_01_31_131604_remove_public_fields_from_companies_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (85,'2023_02_17_132510_enlarge_abouts_to_medium_texts_in_database',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (86,'2023_03_11_164516_change_closed_status_to_reviewing_in_tenders_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (87,'2023_03_16_002159_add_rejection_reason_to_candidate_tender_position_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (88,'2023_03_28_193557_add_client_note_to_candidate_tender_position',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (89,'2023_04_12_103234_create_candidate_experiences_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (90,'2023_04_12_103721_create_candidate_experience_candidate_skill_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (91,'2023_04_27_145328_add_origin_fields_to_solutions_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (92,'2023_05_05_114156_create_bench_specialists_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (93,'2023_05_05_114758_create_bench_specialist_company_table',1);
INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES (94,'2023_07_11_232432_make_bench_specialists_table_reviewable',1);
