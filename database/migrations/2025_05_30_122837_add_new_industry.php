<?php

use App\Enums\Enterprise\IndustryType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('industries')->insert([
            'id_string' => 'information_technology_and_digital_services',
            'name' => 'Information Technology & Digital Services',
            'type' => IndustryType::Primary,
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('industries')
            ->where('id_string', 'information_technology_and_digital_services')
            ->delete();
    }
};
