<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $companiesWithPresentationFile = DB::table('companies')
            ->whereNotNull('presentation_resource_id')
            ->get();
        foreach ($companiesWithPresentationFile as $company) {
            DB::table('company_document_resources')->insert([
                'company_id' => $company->id,
                'resource_id' => $company->presentation_resource_id,
            ]);
        }

        Schema::table('companies', function (Blueprint $table) {
            $table->dropForeign(['presentation_resource_id']);
            $table->dropColumn('presentation_resource_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->unsignedBigInteger('presentation_resource_id')->nullable()->after('notes');
            $table->foreign('presentation_resource_id')->references('id')->on('resources')->onDelete('set null');
        });

        $companyPresentationFiles = DB::table('company_document_resources')
            ->join('resources', 'company_document_resources.resource_id', '=', 'resources.id')
            ->where('resources.type', 'company_presentation_file')
            ->get();
        foreach ($companyPresentationFiles as $companyPresentationFile) {
            DB::table('companies')
                ->where('id', $companyPresentationFile->company_id)
                ->update([
                    'presentation_resource_id' => $companyPresentationFile->resource_id,
                ]);
        }
    }
};
