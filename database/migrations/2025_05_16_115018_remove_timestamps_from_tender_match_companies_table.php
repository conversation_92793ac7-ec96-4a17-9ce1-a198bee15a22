<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tender_match_companies', function (Blueprint $table) {
            $table->dropColumn(['invited_at', 'viewed_at']);
            $table->dropTimestamps();
            $table->dropSoftDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tender_match_companies', function (Blueprint $table) {
            $table->dateTime('invited_at')->nullable();
            $table->dateTime('viewed_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }
};
