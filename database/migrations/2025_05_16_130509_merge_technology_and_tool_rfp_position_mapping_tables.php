<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $toolMappingsToInsert = DB::table('tool_rfp_position')
            ->get()
            ->map(fn ($mapping) => [
                'rfp_position_id' => $mapping->rfp_position_id,
                'technology_id' => $mapping->technology_id,
            ])
            ->toArray();
        DB::table('technology_rfp_position')->insert($toolMappingsToInsert);

        Schema::dropIfExists('tool_rfp_position');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::create('tool_rfp_position', function (Blueprint $table) {
            $table->uuid('rfp_position_id');
            $table->unsignedBigInteger('technology_id');
            $table->foreign('rfp_position_id')->references('id')->on('rfp_positions')->onDelete('cascade');
            $table->foreign('technology_id')->references('id')->on('technologies')->onDelete('cascade');
            $table->primary(['rfp_position_id', 'technology_id']);
        });

        $toolMappingsToInsert = DB::table('technology_rfp_position')
            ->join('technologies', 'technology_rfp_position.technology_id', '=', 'technologies.id')
            ->where('technologies.type', 'tool')
            ->get()
            ->map(fn ($mapping) => [
                'rfp_position_id' => $mapping->rfp_position_id,
                'technology_id' => $mapping->technology_id,
            ])
            ->toArray();
        DB::table('tool_rfp_position')->insert($toolMappingsToInsert);

        $allToolIdsQuery = DB::table('technologies')->select('id')->where('type', 'tool');
        DB::table('technology_rfp_position')
            ->whereIn('technology_id', $allToolIdsQuery)
            ->delete();
    }
};
