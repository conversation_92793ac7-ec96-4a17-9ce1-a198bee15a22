<?php

use App\Enums\UserRole;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $this->seedRoles();
        DB::table('model_has_roles')->truncate();

        $staffUserIds = DB::table('users')
            ->whereNull('company_id')
            ->pluck('id');
        $this->attachRole($staffUserIds, UserRole::SuperAdmin);

        $vendorUserIds = DB::table('users')
            ->join('companies', 'users.company_id', '=', 'companies.id')
            ->where('is_vendor', true)
            ->pluck('users.id');
        $this->attachRole($vendorUserIds, UserRole::Vendor);

        $clientUserIds = DB::table('users')
            ->join('companies', 'users.company_id', '=', 'companies.id')
            ->where('is_vendor', false)
            ->pluck('users.id');
        $this->attachRole($clientUserIds, UserRole::Client);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Nothing to reverse.
    }

    private function seedRoles(): void
    {
        Artisan::call('db:seed', [
            '--class' => 'RoleSeeder',
            '--force' => true, // Use --force to run in production.
        ]);
    }

    private function attachRole(array|Collection $userIds, UserRole $role): void
    {
        $apiRole = $this->getRole($role, 'api');
        $webRole = $this->getRole($role, 'web');

        foreach ($userIds as $userId) {
            DB::table('model_has_roles')
                ->insert([
                    [
                        'role_id' => $apiRole->id,
                        'model_type' => User::class,
                        'model_id' => $userId,
                    ], [
                        'role_id' => $webRole->id,
                        'model_type' => User::class,
                        'model_id' => $userId,
                    ]
                ]);
        }
    }

    private function getRole(UserRole $role, string $guard): \stdClass
    {
        return DB::table('roles')
            ->where('name', $role->value)
            ->where('guard_name', $guard)
            ->firstOrFail();
    }
};
