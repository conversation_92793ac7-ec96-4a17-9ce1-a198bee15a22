<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tender_matches', function (Blueprint $table) {
            $table->string('companies_filter')->default('specific_companies')->index()->after('tender_id');
            $table->json('matching_api_payload')->nullable()->after('companies_filter');
            $table->renameColumn('source_matching', 'matching_api_response');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tender_matches', function (Blueprint $table) {
            $table->dropIndex(['companies_filter']);
            $table->dropColumn('companies_filter');
            $table->dropColumn('matching_api_payload');
            $table->renameColumn('matching_api_response', 'source_matching');
        });
    }
};
