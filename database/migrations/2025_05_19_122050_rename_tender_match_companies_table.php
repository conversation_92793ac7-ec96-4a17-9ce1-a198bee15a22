<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Rename to match Lara<PERSON>'s convention for pivot tables.
        Schema::rename('tender_match_companies', 'tender_match_company');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::rename('tender_match_company', 'tender_match_companies');
    }
};
