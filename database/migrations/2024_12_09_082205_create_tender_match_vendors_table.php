<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tender_match_vendors', function (Blueprint $table) {
            $table->uuid('match_id');
            $table->unsignedBigInteger('vendor_id');
            $table->foreign('match_id')->references('id')->on('tender_matches')->onDelete('cascade');
            $table->foreign('vendor_id')->references('id')->on('vendors')->onDelete('cascade');
            $table->float('score');
            $table->dateTime('invited_at')->nullable();
            $table->dateTime('viewed_at')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->primary(['match_id', 'vendor_id']);

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tender_match_vendors');  
    }
};
