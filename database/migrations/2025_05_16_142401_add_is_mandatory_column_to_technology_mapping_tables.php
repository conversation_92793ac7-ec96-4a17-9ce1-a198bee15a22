<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rfp_position_technology', function (Blueprint $table) {
            $table->boolean('is_mandatory')->default(false);
        });
        Schema::table('tender_position_technology', function (Blueprint $table) {
            $table->boolean('is_mandatory')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rfp_position_technology', function (Blueprint $table) {
            $table->dropColumn('is_mandatory');
        });
        Schema::table('tender_position_technology', function (Blueprint $table) {
            $table->dropColumn('is_mandatory');
        });
    }
};
