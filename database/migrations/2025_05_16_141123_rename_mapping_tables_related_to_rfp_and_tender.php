<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::rename('location_rfp', 'rfp_location');
        Schema::rename('certification_rfp', 'rfp_certification');
        Schema::rename('secondary_industry_rfp', 'rfp_secondary_industry');
        Schema::rename('language_rfp_position', 'rfp_position_language');
        Schema::rename('technology_rfp_position', 'rfp_position_technology');

        Schema::rename('technology_tender_position', 'tender_position_technology');
        Schema::rename('technology_tender_project', 'tender_project_technology');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::rename('rfp_location', 'location_rfp');
        Schema::rename('rfp_certification', 'certification_rfp');
        Schema::rename('rfp_secondary_industry', 'secondary_industry_rfp');
        Schema::rename('rfp_position_language', 'language_rfp_position');
        Schema::rename('rfp_position_technology', 'technology_rfp_position');

        Schema::rename('tender_position_technology', 'technology_tender_position');
        Schema::rename('tender_project_technology', 'technology_tender_project');
    }
};
