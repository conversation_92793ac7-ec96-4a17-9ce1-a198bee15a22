<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            $table->boolean('offering_resources')->default(true)->change();
            $table->boolean('offering_solutions')->default(false)->change();
            $table->boolean('payment_time_and_material')->default(true)->change();
            $table->boolean('payment_fixed_price')->default(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vendors', function (Blueprint $table) {
            $table->boolean('offering_resources')->default(null)->change();
            $table->boolean('offering_solutions')->default(null)->change();
            $table->boolean('payment_time_and_material')->default(null)->change();
            $table->boolean('payment_fixed_price')->default(null)->change();
        });
    }
};
