<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rfp_positions', function (Blueprint $table) {
            $table->string('work_location')->nullable()->after('rate_max');
            $table->text('notes')->nullable()->after('work_location');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rfp_positions', function (Blueprint $table) {
            $table->dropColumn(['work_location', 'notes']);
        });
    }
};
