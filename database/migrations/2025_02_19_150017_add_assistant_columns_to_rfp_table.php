<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('industries', function (Blueprint $table) {
            $table->string('id_string', 100)->nullable()->unique()->after('id');
            $table->string('type', 32)->nullable()->after('id_string');
        });


        Schema::table('rfps', function (Blueprint $table) {
            $table->string('step')->nullable();
            $table->boolean('api_rfp')->default(false);
            $table->boolean('api_resources')->default(false);
            $table->string('title')->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->string('timezone')->nullable();
            $table->foreignId('primary_industry')->nullable()->constrained('industries')->onDelete('cascade');
            $table->longText('description')->nullable();
            $table->longText('key_deliverables')->nullable();
            $table->longText('milestones')->nullable();
            $table->longText('team_composition')->nullable();
        });


        Schema::create('locations', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('id_string');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('location_rfp', function (Blueprint $table) {
            $table->uuid('rfp_id');
            $table->unsignedBigInteger('location_id');
            $table->foreign('rfp_id')->references('id')->on('rfps')->onDelete('cascade');
            $table->foreign('location_id')->references('id')->on('locations')->onDelete('cascade');
            $table->primary(['rfp_id', 'location_id']);
        });


        Schema::create('secondary_industry_rfp', function (Blueprint $table) {
            $table->uuid('rfp_id');
            $table->unsignedBigInteger('industry_id');
            $table->foreign('rfp_id')->references('id')->on('rfps')->onDelete('cascade');
            $table->foreign('industry_id')->references('id')->on('industries')->onDelete('cascade');
            $table->primary(['rfp_id', 'industry_id']);
        });


        Schema::create('certifications', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('id_string');
            $table->timestamps();
            $table->softDeletes();
        });


        Schema::create('certification_rfp', function (Blueprint $table) {
            $table->uuid('rfp_id');
            $table->unsignedBigInteger('certification_id');
            $table->foreign('rfp_id')->references('id')->on('rfps')->onDelete('cascade');
            $table->foreign('certification_id')->references('id')->on('certifications')->onDelete('cascade');
            $table->primary(['rfp_id', 'certification_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rfps', function (Blueprint $table) {
            $table->dropForeign(['primary_industry']);
        });
    
        Schema::table('location_rfp', function (Blueprint $table) {
            $table->dropForeign(['rfp_id']);
            $table->dropForeign(['location_id']);
        });
    
        Schema::table('secondary_industry_rfp', function (Blueprint $table) {
            $table->dropForeign(['rfp_id']);
            $table->dropForeign(['industry_id']);
        });
    
        Schema::table('certification_rfp', function (Blueprint $table) {
            $table->dropForeign(['rfp_id']);
            $table->dropForeign(['certification_id']);
        });
    
        Schema::dropIfExists('location_rfp');
        Schema::dropIfExists('secondary_industry_rfp');
        Schema::dropIfExists('certification_rfp');
    
        Schema::dropIfExists('locations');
        Schema::dropIfExists('certifications');
    
        Schema::table('rfps', function (Blueprint $table) {
            $table->dropColumn([
                'step', 
                'api_rfp', 
                'api_resources', 
                'title',
                'start_date', 
                'end_date', 
                'timezone', 
                'primary_industry', 
                'description', 
                'key_deliverables', 
                'milestones', 
                'team_composition'
            ]);
        });
    
        Schema::table('industries', function (Blueprint $table) {
            $table->dropColumn(['id_string', 'type']);
        });
    }
};
