<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rfps', function (Blueprint $table) {
            // Fix one-to-one relationship between RFP and Tender.
            $table->unique('tender_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rfps', function (Blueprint $table) {
            // Drop unique constraint. Foreign key has to be dropped before unique constraint.
            $table->dropForeign(['tender_id']);
            $table->dropUnique(['tender_id']);
            $table->foreign('tender_id')->references('id')->on('tenders')->cascadeOnDelete()->cascadeOnUpdate();
        });
    }
};
