<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::statement("
            UPDATE candidate_assignments ca
            JOIN tender_positions tp ON tp.id = ca.tender_position_id
            JOIN tenders t ON t.id = tp.tender_id
            SET ca.company_id = t.company_id,
                ca.tender_id = t.id
            WHERE ca.company_id IS NULL AND ca.tender_id IS NULL;
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Non-reversible migration.
    }
};
