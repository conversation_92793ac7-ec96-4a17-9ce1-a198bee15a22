<?php

use App\Enums\Enterprise\AssistantStep;
use Database\Seeders\LanguagesSeeder;
use Database\Seeders\TechnologiesAssistantSeeder;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rfp_positions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('rfp_id');
            $table->foreign('rfp_id')->references('id')->on('rfps')->onDelete('cascade');
            $table->string('job_title')->nullable();
            $table->integer('number_of_resources')->nullable();
            $table->string('seniority_level')->nullable();
            $table->integer('workload')->nullable();
            $table->integer('rate')->nullable();
            $table->integer('rate_to')->nullable();
            $table->boolean('estimated')->default(true);
            $table->timestamps();
        });

        Schema::create('languages', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('id_string');
            $table->timestamps();
            $table->softDeletes();
        });

        $this->seedLanguages();

        Schema::create('language_rfp_position', function (Blueprint $table) {
            $table->uuid('rfp_position_id');
            $table->unsignedBigInteger('language_id');
            $table->foreign('rfp_position_id')->references('id')->on('rfp_positions')->onDelete('cascade');
            $table->foreign('language_id')->references('id')->on('languages')->onDelete('cascade');
            $table->primary(['rfp_position_id', 'language_id']);
        });

        Schema::table('technologies', function (Blueprint $table) {
            $table->string('id_string', 100)->nullable()->unique()->after('id');
            $table->string('type', 32)->nullable()->after('id_string');
        });

        Schema::create('technology_rfp_position', function (Blueprint $table) {
            $table->uuid('rfp_position_id');
            $table->unsignedBigInteger('technology_id');
            $table->foreign('rfp_position_id')->references('id')->on('rfp_positions')->onDelete('cascade');
            $table->foreign('technology_id')->references('id')->on('technologies')->onDelete('cascade');
            $table->primary(['rfp_position_id', 'technology_id']);
        });

        Schema::create('tool_rfp_position', function (Blueprint $table) {
            $table->uuid('rfp_position_id');
            $table->unsignedBigInteger('technology_id');
            $table->foreign('rfp_position_id')->references('id')->on('rfp_positions')->onDelete('cascade');
            $table->foreign('technology_id')->references('id')->on('technologies')->onDelete('cascade');
            $table->primary(['rfp_position_id', 'technology_id']);
        });

        Schema::table('rfps', function (Blueprint $table) {
            $table->string('step')->nullable()->default(AssistantStep::Info)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tool_rfp_position');
        Schema::dropIfExists('technology_rfp_position');
        Schema::dropIfExists('language_rfp_position');

        Schema::table('technologies', function (Blueprint $table) {
            $table->dropColumn(['id_string', 'type']);
        });

        Schema::dropIfExists('languages');

        Schema::table('rfp_positions', function (Blueprint $table) {
            $table->dropForeign(['rfp_id']);
        });

        Schema::dropIfExists('rfp_positions');

        Schema::table('rfps', function (Blueprint $table) {
            $table->string('step')->nullable()->default(null)->change();
        });
    }

    private function seedLanguages(): void
    {
        Artisan::call('db:seed', ['--class' => LanguagesSeeder::class]);
    }

    private function seedTechnologies(): void
    {
        Artisan::call('db:seed', ['--class' => TechnologiesAssistantSeeder::class]);
    }
};
