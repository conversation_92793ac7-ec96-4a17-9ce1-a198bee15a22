<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::rename('candidate_tender_position', 'candidate_assignments');

        Schema::table('candidate_assignments', function (Blueprint $table) {
            $table->foreignId('company_id')->nullable()->after('candidate_id')->constrained()->onDelete('cascade');
            $table->foreignId('tender_id')->nullable()->after('company_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('tender_position_id')->nullable()->after('tender_id')->change();
            $table->date('start_date')->nullable()->after('tender_position_id');
            $table->date('end_date')->nullable()->after('start_date');
            $table->primary(['candidate_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('candidate_assignments', function (Blueprint $table) {
            $table->dropPrimary(['candidate_id']);
            $table->dropForeign(['company_id']);
            $table->dropForeign(['tender_id']);
            $table->dropColumn(['company_id', 'tender_id', 'start_date', 'end_date']);
            // Revert tender_position_id to non-nullable is not possible without data loss.
        });

        Schema::rename('candidate_assignments', 'candidate_tender_position');
    }
};
