<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('workspace_company', function (Blueprint $table) {
            $table->uuid('workspace_id');
            $table->unsignedBigInteger('company_id');
            $table->foreign('workspace_id')->references('id')->on('workspaces')->onDelete('cascade');
            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
            $table->primary(['workspace_id', 'company_id']);
        });

        Schema::table('workspaces', function (Blueprint $table) {
            $table->string('name')->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('workspace_company');

        Schema::table('workspaces', function (Blueprint $table) {
            $table->string('name')->nullable(false)->change();
        });
    }
};
