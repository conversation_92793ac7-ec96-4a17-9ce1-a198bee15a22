<?php

use App\Enums\Enterprise\IndustryType;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('industries')
            ->where('type', IndustryType::Primary)
            ->update([
                'featured' => true,
            ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('industries')
            ->where('type', IndustryType::Primary)
            ->update([
                'featured' => false,
            ]);
    }
};
