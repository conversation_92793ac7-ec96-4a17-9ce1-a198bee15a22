<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('candidate_tender_position', function (Blueprint $table) {
            $table->json('matching_api_payload')->nullable();
            $table->json('matching_api_response')->nullable();
            $table->unsignedSmallInteger('matching_api_response_status')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('candidate_tender_position', function (Blueprint $table) {
            $table->dropColumn('matching_api_payload');
            $table->dropColumn('matching_api_response');
            $table->dropColumn('matching_api_response_status');
        });
    }
};
