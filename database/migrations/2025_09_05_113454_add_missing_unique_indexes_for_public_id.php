<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Tables with existing unique index on public_id:
        // bench_specialists
        // companies
        // resources
        // solutions
        // tenders
        // vendor

        // Tables without public_id unique index:
        // candidate_experiences
        // candidates
        // personal_access_tokens
        // tender_positions

        // Tables without public_id unique index, but duplicates:
        // solution_media

        Schema::table('candidate_experiences', function (Blueprint $table) {
            $table->unique('public_id');
        });

        Schema::table('candidates', function (Blueprint $table) {
            $table->dropIndex(['public_id']);
            $table->unique('public_id');
        });

        Schema::table('personal_access_tokens', function (Blueprint $table) {
            $table->dropIndex(['public_id']);
            $table->unique('public_id');
        });

        Schema::table('tender_positions', function (Blueprint $table) {
            $table->dropIndex(['public_id']);
            $table->unique('public_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('candidate_experiences', function (Blueprint $table) {
            $table->dropUnique(['public_id']);
        });

        Schema::table('candidates', function (Blueprint $table) {
            $table->dropUnique(['public_id']);
            $table->index('public_id');
        });

        Schema::table('personal_access_tokens', function (Blueprint $table) {
            $table->dropUnique(['public_id']);
            $table->index('public_id');
        });

        Schema::table('tender_positions', function (Blueprint $table) {
            $table->dropUnique(['public_id']);
            $table->index('public_id');
        });
    }
};
