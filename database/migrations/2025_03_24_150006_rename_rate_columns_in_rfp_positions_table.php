<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rfp_positions', function (Blueprint $table) {
            $table->renameColumn('rate', 'rate_min');
            $table->renameColumn('rate_to', 'rate_max');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rfp_positions', function (Blueprint $table) {
            $table->renameColumn('rate_min', 'rate');
            $table->renameColumn('rate_max', 'rate_to');
        });
    }
};
