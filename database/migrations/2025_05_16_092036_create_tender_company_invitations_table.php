<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tender_company_invitations', function (Blueprint $table) {
            $table->unsignedBigInteger('tender_id');
            $table->unsignedBigInteger('company_id');
            $table->foreign('tender_id')->references('id')->on('tenders')->onDelete('restrict');
            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
            $table->timestamp('sent_at')->nullable();
            $table->timestamps();

            $table->primary(['tender_id', 'company_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tender_company_invitations');
    }
};
