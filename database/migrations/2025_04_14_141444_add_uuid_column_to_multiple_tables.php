<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->uuid('uuid')->unique()->nullable()->after('id');
        });
        Schema::table('solutions', function (Blueprint $table) {
            $table->uuid('uuid')->unique()->nullable()->after('id');
        });
        Schema::table('technologies', function (Blueprint $table) {
            $table->uuid('uuid')->unique()->nullable()->after('id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropColumn('uuid');
        });
        Schema::table('solutions', function (Blueprint $table) {
            $table->dropColumn('uuid');
        });
        Schema::table('technologies', function (Blueprint $table) {
            $table->dropColumn('uuid');
        });
    }
};
