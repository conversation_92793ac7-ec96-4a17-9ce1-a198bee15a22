<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tender_match_vendors', function (Blueprint $table) {
            $table->dropForeign(['match_id']);
            $table->dropForeign(['vendor_id']);
        });
        foreach (DB::table('tender_match_vendors')->get() as $tenderMatchVendor) {
            $vendorId = $tenderMatchVendor->vendor_id;
            $companyId = DB::table('vendors')
                ->where('id', $vendorId)
                ->value('company_id');
            DB::table('tender_match_vendors')
                ->where([
                    'match_id' => $tenderMatchVendor->match_id,
                    'vendor_id' => $vendorId,
                ])
                ->update(['vendor_id' => $companyId]);
        }
        Schema::table('tender_match_vendors', function (Blueprint $table) {
            $table->renameColumn('vendor_id', 'company_id');
            $table->rename('tender_match_companies');
        });
        Schema::table('tender_match_companies', function (Blueprint $table) {
            $table->foreign('match_id')->references('id')->on('tender_matches')->onDelete('cascade');
            $table->foreign('company_id')->references('id')->on('companies')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tender_match_companies', function (Blueprint $table) {
            $table->dropForeign(['match_id']);
            $table->dropForeign(['company_id']);
        });
        foreach (DB::table('tender_match_companies')->get() as $tenderMatchCompany) {
            $companyId = $tenderMatchCompany->company_id;
            $vendorId = DB::table('vendors')
                ->where('company_id', $companyId)
                ->value('id');
            DB::table('tender_match_companies')
                ->where([
                    'match_id' => $tenderMatchCompany->match_id,
                    'company_id' => $companyId,
                ])
                ->update(['company_id' => $vendorId]);
        }
        Schema::table('tender_match_companies', function (Blueprint $table) {
            $table->renameColumn('company_id', 'vendor_id');
            $table->rename('tender_match_vendors');
        });
        Schema::table('tender_match_vendors', function (Blueprint $table) {
            $table->foreign('match_id')->references('id')->on('tender_matches')->onDelete('cascade');
            $table->foreign('vendor_id')->references('id')->on('vendors')->onDelete('cascade');
        });
    }
};
