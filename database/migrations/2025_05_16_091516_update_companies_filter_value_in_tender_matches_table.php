<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('tender_matches')
            ->where('companies_filter', 'specific_companies')
            ->update(['companies_filter' => 'selection']);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('tender_matches')
            ->where('companies_filter', 'selection')
            ->update(['companies_filter' => 'specific_companies']);
    }
};
