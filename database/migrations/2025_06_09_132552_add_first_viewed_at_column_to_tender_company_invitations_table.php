<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tender_company_invitations', function (Blueprint $table) {
            $table->timestamp('first_viewed_at')->nullable()->after('sent_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tender_company_invitations', function (Blueprint $table) {
            $table->dropColumn('first_viewed_at');
        });
    }
};
