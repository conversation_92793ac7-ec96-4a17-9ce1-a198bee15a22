<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tender_matches', function (Blueprint $table) {
            $table->unsignedSmallInteger('matching_api_response_status')->nullable()->after('matching_api_response');
        });

        DB::table('tender_matches')
            ->whereNotNull('matching_api_response')
            ->update(['matching_api_response_status' => 200]);
        DB::table('tender_matches')
            ->whereNull('matching_api_response')
            ->update(['matching_api_response_status' => 500]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tender_matches', function (Blueprint $table) {
            $table->dropColumn('matching_api_response_status');
        });
    }
};
