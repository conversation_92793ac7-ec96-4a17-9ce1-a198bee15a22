<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('rfps', function (Blueprint $table) {
            $table->renameColumn('api_rfp', 'rfp_api_called');
            $table->renameColumn('api_resources', 'resources_extract_api_called');
            $table->boolean('resources_suggest_api_called')->after('resources_extract_api_called')->default(false);
            $table->json('resources_extract_api_response')->after('resources_suggest_api_called')->nullable();
            $table->json('resources_suggest_api_response')->after('resources_extract_api_response')->nullable();
        });

        Schema::table('rfp_positions', function (Blueprint $table) {
            $table->renameColumn('estimated', 'from_suggestion');
            $table->boolean('from_suggestion')->default(false)->change();
        });
    }

    public function down(): void
    {
        Schema::table('rfps', function (Blueprint $table) {
            $table->renameColumn('rfp_api_called', 'api_rfp');
            $table->renameColumn('resources_extract_api_called', 'api_resources');
            $table->dropColumn('resources_suggest_api_called');
            $table->dropColumn('resources_extract_api_response');
            $table->dropColumn('resources_suggest_api_response');
        });

        Schema::table('rfp_positions', function (Blueprint $table) {
            $table->renameColumn('from_suggestion', 'estimated');
        });
    }
};
