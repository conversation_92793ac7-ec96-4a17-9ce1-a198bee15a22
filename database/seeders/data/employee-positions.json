[{"name": "Backend Developer", "name_plural": "Backend Developers", "hireable": true, "seniorities": "junior,medior,senior,lead"}, {"name": "Frontend Developer", "name_plural": "Frontend Developers", "hireable": true, "seniorities": "junior,medior,senior,lead"}, {"name": "Mobile Deveoper", "name_plural": "Mobile Deveopers", "hireable": true, "seniorities": "junior,medior,senior,lead"}, {"name": "Full Stack Developer", "name_plural": "Full Stack Developers", "hireable": true, "seniorities": "junior,medior,senior,lead"}, {"name": "QA Engineer", "name_plural": "QA Engineers", "hireable": true, "seniorities": "junior,medior,senior,lead"}, {"name": "DevOps Engineer", "name_plural": "DevOps Engineers", "hireable": true, "seniorities": "junior,medior,senior,lead"}, {"name": "Integration Engineer", "name_plural": "Integration Engineers", "hireable": true, "seniorities": "junior,medior,senior,lead"}, {"name": "Software Architect", "name_plural": "Software Architects", "hireable": true, "seniorities": null}, {"name": "System Architect", "name_plural": "System Architects", "hireable": true, "seniorities": null}, {"name": "UI/UX designer", "name_plural": "UI/UX designers", "hireable": true, "seniorities": "junior,medior,senior,lead"}, {"name": "Product/Project Manager", "name_plural": "Product/Project Managers", "hireable": false, "seniorities": null}]