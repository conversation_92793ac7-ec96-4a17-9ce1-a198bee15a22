{"score": 0.4371413079791044, "errors": null, "technologies": {"scoring": {"score": 0, "weight": 1, "weight_normalized": 0.3333333333333333, "contribution": 0}, "evaluated_technologies": [{"score": 0, "id": "baaa43fd-349b-4230-b6a9-ac81fa6992a1", "found_in": "none", "found_by_ids": []}, {"score": 0, "id": "c7d30980-8fa3-4dc0-babf-14279d8ef77d", "found_in": "none", "found_by_ids": []}, {"score": 0, "id": "dcbbe029-cc74-4b58-afd2-eb1ff8519987", "found_in": "none", "found_by_ids": []}]}, "experience": {"scoring": {"score": 0.31142392393731316, "weight": 1, "weight_normalized": 0.3333333333333333, "contribution": 0.10380797464577106}, "evaluated_experience": [{"score": 0.31142392393731316, "name": "Senior Android Developer at Large Automotive Company"}]}, "seniority": {"scoring": {"score": 1, "weight": 1, "weight_normalized": 0.3333333333333333, "contribution": 0.3333333333333333}, "reason": "overqualified", "requested_seniority": "medior", "candidate_seniority": "senior"}, "language": null, "location": null, "rates": {"scoring": {"score": 1, "weight": 0, "weight_normalized": 0, "contribution": 0}, "difference": 0, "difference_percentage": 0}}