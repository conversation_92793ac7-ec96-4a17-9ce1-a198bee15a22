<?php

use App\Enums\Country;
use App\Enums\Education;
use App\Enums\Seniority;

return [
    [
        'candidate' => [
            'id' => 4,
            'public_id' => 'UntBrK',
            'vendor_id' => 1,
            'origin_id' => null,
            'cv_resource_id' => null,
            'cv_parsed_raw_data' => null,
            'clone' => false,
            'finished' => true,
            'internal_name' => 'Pata',
            'name' => 'Patrycja',
            'profession' => 'Software Engineer',
            'seniority' => Seniority::Senior,
            'rate' => 20,
            'last_job_title' => 'Senior Java Developer',
            'years_of_experience' => 8,
            'highest_education' => Education::Master,
            'field_of_study' => 'Computer Science',
            'country' => Country::Poland,
            'city' => 'Kraków',
        ],
        'skills' => [
            [ 'id' => 7, 'technology_id' => 100, 'years_of_experience' => 10 ],
            [ 'id' => 8, 'technology_id' => 10,  'years_of_experience' => 8 ],
        ],
        'experiences' => [
            [
                'experience' => [
                    'id' => 3,
                    'public_id' => 's9fg82',
                    'order' => 1,
                    'name' => 'Senior Java Developer at Large Automotive Company',
                    'description' => 'Started as an intern on this position and slowly build up my way to the top. Key team player.',
                    'length_type' => 'years',
                    'length' => 5,
                ],
                'skills' => [7, 8],
            ],
        ],
    ],
    [
        'candidate' => [
            'id' => 5,
            'public_id' => 'UMoPth',
            'vendor_id' => 1,
            'origin_id' => null,
            'cv_resource_id' => null,
            'cv_parsed_raw_data' => null,
            'clone' => false,
            'finished' => true,
            'internal_name' => 'Gregor of Rivia',
            'name' => 'Gregor',
            'profession' => 'Software Engineer',
            'seniority' => Seniority::Senior,
            'rate' => 57,
            'last_job_title' => 'Developer',
            'years_of_experience' => 17,
            'highest_education' => Education::Master,
            'field_of_study' => 'Computer Science',
            'country' => Country::Poland,
            'city' => 'Kraków',
        ],
        'skills' => [
            [ 'id' => 9,  'technology_id' => 100, 'years_of_experience' => 10 ],
            [ 'id' => 10, 'technology_id' => 10,  'years_of_experience' => 8 ],
        ],
        'experiences' => [
            [
                'experience' => [
                    'id' => 4,
                    'public_id' => 's9fg24',
                    'order' => 1,
                    'name' => 'Developer at Large Company',
                    'description' => 'Started as an intern on this position and slowly build up my way to the top. Key team player.',
                    'length_type' => 'years',
                    'length' => 5,
                ],
                'skills' => [9, 10],
            ],
        ],
    ],
    [
        'candidate' => [
            'id' => 6,
            'public_id' => 'pQ1L0a',
            'vendor_id' => 1,
            'origin_id' => null,
            'cv_resource_id' => null,
            'cv_parsed_raw_data' => null,
            'clone' => false,
            'finished' => true,
            'internal_name' => 'Aga',
            'name' => 'Agata',
            'profession' => 'Software Engineer',
            'seniority' => Seniority::Senior,
            'rate' => 35,
            'last_job_title' => 'Java Developer',
            'years_of_experience' => 5,
            'highest_education' => Education::Bachelor,
            'field_of_study' => 'Software Engineering',
            'country' => Country::Poland,
            'city' => 'Warszawa',
        ],
        'skills' => [
            [ 'id' => 11, 'technology_id' => 100, 'years_of_experience' => 5 ],
            [ 'id' => 12, 'technology_id' => 10,  'years_of_experience' => 3 ],
        ],
        'experiences' => [
            [
                'experience' => [
                    'id' => 5,
                    'public_id' => 'xp92ka',
                    'order' => 1,
                    'name' => 'Java Developer at FinTech Startup',
                    'description' => 'Built payment integrations and internal tooling. Worked across backend services and Android client.',
                    'length_type' => 'years',
                    'length' => 3,
                ],
                'skills' => [11, 12],
            ],
        ],
    ],
    [
        'candidate' => [
            'id' => 7,
            'public_id' => 'mN7yZ4',
            'vendor_id' => 1,
            'origin_id' => null,
            'cv_resource_id' => null,
            'cv_parsed_raw_data' => null,
            'clone' => false,
            'finished' => true,
            'internal_name' => 'Marek K',
            'name' => 'Marek',
            'profession' => 'Software Engineer',
            'seniority' => Seniority::Junior,
            'rate' => 18,
            'last_job_title' => 'Junior Android Developer',
            'years_of_experience' => 2,
            'highest_education' => Education::Bachelor,
            'field_of_study' => 'Computer Science',
            'country' => Country::Poland,
            'city' => 'Gdańsk',
        ],
        'skills' => [
            [ 'id' => 13, 'technology_id' => 10,  'years_of_experience' => 2 ],
            [ 'id' => 14, 'technology_id' => 100, 'years_of_experience' => 1 ],
        ],
        'experiences' => [
            [
                'experience' => [
                    'id' => 6,
                    'public_id' => 'qd12bv',
                    'order' => 1,
                    'name' => 'Junior Android Developer at Retail App',
                    'description' => 'Implemented features, wrote unit tests, and assisted with Java-to-Kotlin migration.',
                    'length_type' => 'years',
                    'length' => 1,
                ],
                'skills' => [13, 14],
            ],
        ],
    ],
    [
        'candidate' => [
            'id' => 8,
            'public_id' => 'Zk8Ew1',
            'vendor_id' => 1,
            'origin_id' => null,
            'cv_resource_id' => null,
            'cv_parsed_raw_data' => null,
            'clone' => false,
            'finished' => true,
            'internal_name' => 'Ola',
            'name' => 'Aleksandra',
            'profession' => 'Software Engineer',
            'seniority' => Seniority::Senior,
            'rate' => 62,
            'last_job_title' => 'Senior Android/Java Engineer',
            'years_of_experience' => 11,
            'highest_education' => Education::Master,
            'field_of_study' => 'Computer Science',
            'country' => Country::Poland,
            'city' => 'Wrocław',
        ],
        'skills' => [
            [ 'id' => 15, 'technology_id' => 100, 'years_of_experience' => 11 ],
            [ 'id' => 16, 'technology_id' => 10,  'years_of_experience' => 9 ],
        ],
        'experiences' => [
            [
                'experience' => [
                    'id' => 7,
                    'public_id' => 'rv77cx',
                    'order' => 1,
                    'name' => 'Senior Engineer at Mobility Scale-up',
                    'description' => 'Led Android client modernization and backend Java services for real-time trip tracking.',
                    'length_type' => 'years',
                    'length' => 4,
                ],
                'skills' => [15, 16],
            ],
        ],
    ],
];
