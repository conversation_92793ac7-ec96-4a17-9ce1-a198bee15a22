<?php

namespace Database\Seeders;

use App\Models\Workspace;
use App\Models\User;
use App\Models\Company;
use Illuminate\Database\Seeder;

class WorkspaceSeeder extends Seeder
{

    public function run(): void
    {
        if (Workspace::count() > 0) {
            $this->command->warn('Workspace seeder already run, skipping...');
            return;
        }

        $user = User::find(1);
        $companyIds = Company::where('is_vendor', true)->take(6)->pluck('id');
        $this->createWorkspaceForUser($user, $companyIds);

        $company = Company::where('is_vendor', false)->first();
        $user = $company->users()->first();
        $this->createWorkspaceForUser($user, $companyIds);
    }

    private static function createWorkspaceForUser($user, $companyIds): void
    {
        $workspace = Workspace::create();
        $workspace->users()->attach($user->id);
        $workspace->companies()->sync($companyIds);
    }
}
