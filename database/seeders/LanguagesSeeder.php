<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Language;

class LanguagesSeeder extends Seeder
{
    public function run(): void
    {
        $languages = [
            ['id_string' => 'en', 'name' => 'English'],
            ['id_string' => 'de', 'name' => 'German'],
            ['id_string' => 'fr', 'name' => 'French'],
            ['id_string' => 'es', 'name' => 'Spanish'],
            ['id_string' => 'it', 'name' => 'Italian'],
            ['id_string' => 'ru', 'name' => 'Russian'],
            ['id_string' => 'pt', 'name' => 'Portuguese'],
            ['id_string' => 'nl', 'name' => 'Dutch'],
            ['id_string' => 'pl', 'name' => 'Polish'],
            ['id_string' => 'tr', 'name' => 'Turkish'],
            ['id_string' => 'sv', 'name' => 'Swedish'],
            ['id_string' => 'da', 'name' => 'Danish'],
            ['id_string' => 'no', 'name' => 'Norwegian'],
            ['id_string' => 'fi', 'name' => 'Finnish'],
            ['id_string' => 'cs', 'name' => 'Czech'],
            ['id_string' => 'ro', 'name' => 'Romanian'],
            ['id_string' => 'el', 'name' => 'Greek'],
            ['id_string' => 'hu', 'name' => 'Hungarian'],
            ['id_string' => 'uk', 'name' => 'Ukrainian'],
            ['id_string' => 'sr', 'name' => 'Serbian'],
            ['id_string' => 'hr', 'name' => 'Croatian'],
            ['id_string' => 'sk', 'name' => 'Slovak'],
            ['id_string' => 'bg', 'name' => 'Bulgarian'],
            ['id_string' => 'sl', 'name' => 'Slovenian'],
            ['id_string' => 'lt', 'name' => 'Lithuanian'],
            ['id_string' => 'lv', 'name' => 'Latvian'],
            ['id_string' => 'et', 'name' => 'Estonian'],
        ];

        foreach ($languages as $language) {
            Language::firstOrCreate(
                ['id_string' => $language['id_string']],
                ['name' => $language['name']]
            );
        }
    }
}
