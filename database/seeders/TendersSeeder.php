<?php

namespace Database\Seeders;

use App\Enums\Country;
use App\Enums\LengthType;
use App\Enums\PaymentType;
use App\Enums\ServiceType;
use App\Enums\TenderProcessingType;
use App\Enums\TenderStatus;
use App\Models\Company;
use App\Models\Tender;
use App\Models\TenderPosition;
use App\Models\TenderProject;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Libs\Overseer\PublishStatus;

class TendersSeeder extends Seeder
{
    // Required, otherwise tenders would regenerate their own public ids
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (Tender::count() > 0) {
            $this->command->warn('Tenders seeder already run, skipping...');

            return;
        }

        $this->seedTender();
    }

    private function seedTender(): void
    {
        $matchingApiPayload = json_decode(file_get_contents(database_path('seeders/data/tenders/net_dev_matching_api_payload.json')), true);
        $matchingApiResponse = json_decode(file_get_contents(database_path('seeders/data/tenders/net_dev_matching_api_response.json')), true);

        $tenders = [
            [
                'tender' => [
                    'id' => 1,
                    'public_id' => 'Yd0CGJ',
                    'company_id' => 2,
                    'anonymous_company' => true,
                    'publish_status' => PublishStatus::Published,
                    'status' => TenderStatus::Open,
                    'submissions_deadline' => fake()->dateTimeBetween('+3 weeks', '+2 months'),
                    'end_of_incubation' => null,
                    'processing_type' => TenderProcessingType::Immediate,
                    'service_type' => ServiceType::Resources,
                    'evaluation_date' => null,
                    'payment_type' => null,
                    'price' => null,
                    'price_to' => null,
                    'length_type' => LengthType::Unspecified,
                    'length' => null,
                    'name' => '.NET Developer for Logistics Project',
                    'description' => "Project Overview\nThis capacity will be responsible for software development activities under a workstream managed by DHL...",
                    'matching_description' => "The project involves a .NET Developer for a logistics initiative with DHL...",
                    'about' => "Project Overview\nThis capacity will be responsible for software development activities under a workstream managed by DHL...",
                    'cover_resource_id' => 4,
                    'country' => Country::UnitedStates,
                    'created_by' => 4,
                    'updated_by' => 4,
                    'deleted_by' => null,
                    'deleted_at' => null,
                ],

                'project' => [
                    'project' => [
                        'main_industry_id' => 51,
                        'anonymous' => false,
                        'name' => '.NET Developer for Logistics Project',
                        'about' => 'Project Overview\nThis capacity will be responsible for software development activities under a workstream managed by DHL...',
                        'in_house' => false,
                        'client_name' => 'Gordon Ramsay',
                    ],
                    'industries' => [],
                    'technologies' => [118, 7784],
                ],

                'invitations' => [
                    1 => [
                        'sent_at' => fake()->dateTimeBetween('+3 months', '+4 months')
                    ],
                    5 => [
                        'sent_at' => fake()->dateTimeBetween('+3 months', '+4 months')
                    ],
                    8 => [
                        'sent_at' => null
                    ],
                ],
                'matches' => [
                    'id' => '9fac0241-bbc0-40c7-8312-0961fcc3f735',
			        'companies_filter'=> 'selection',
                    'matching_api_payload' => $matchingApiPayload,
                    'matching_api_response' => $matchingApiResponse,
                    'matching_api_response_status' => 200,
                ],
                'positions' => [
                    [
                        'position' => [
                            'id' => 1,
                            'public_id' => 'nq1sSP',
                            'employee_position_id' => 4,
                            'name' => 'Boss Coder',
                            'description' => '<p>Will do anything haha</p><script>alert("xss")</script>',
                            'requirements' => '<p onclick="alert(\'xss\')">Know everything and be cheap</p>',
                            'must_have_requirements' => '<p onclick="alert(\'xss\')">AI, GenAI, Quantum Computing, Big Data, IoT, Blockchain, Crypto, Virtual Realtiy</p>',
                            'price' => 15,
                            'price_to' => 30,
                            'start_date' => null,
                            'length_type' => LengthType::Years,
                            'length' => 2,
                            'possible_extension' => true,
                            'workload' => 160,
                            'count' => 3,
                            'interview' => null,
                            'equipment' => null,
                        ],

                        'technologies' => [118, 7784],
                        'seniorities' => [
                            [
                                'seniority' => 'senior',
                            ],
                            [
                                'seniority' => 'lead',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $this->seedTenders($tenders);
    }
    private function seedTenders(array $tenders): void
    {
        collect($tenders)->each(function ($data) {
            /** @var Tender $tender */
            $tender = Tender::forceCreate($data['tender']);

            /** @var TenderProject $project */
            $project = $tender->project()->forceCreate($data['project']['project']);
            $project->industries()->sync($data['project']['industries'] ?? []);
            $project->technologies()->sync($data['project']['technologies'] ?? []);

            if ($data['positions'] ?? false) {
                collect($data['positions'])->each(function ($positionData) use ($tender) {
                    /** @var TenderPosition $position */
                    $position = $tender->positions()->forceCreate($positionData['position']);
                    $position->seniorities()->createMany($positionData['seniorities']);
                    $position->technologies()->sync($positionData['technologies']);
                });
            }

            // Tender matches.
            $tenderMatch = $tender->matches()->create($data['matches'] ?? []);
            collect($data['matches']['matching_api_response'])->each(function ($match) use ($tenderMatch) {
                $companyId = Company::query()->where('uuid', $match['company_id'])->value('id');
                $tenderMatch->matchedCompanies()
                    ->attach($companyId, ['score' => $match['score']]);
            });

            // Tender invitations.
            $tender->invitedCompanies()->sync($data['invitations'] ?? []);
            $tender->vendors()->syncWithPivotValues(
                collect($data['invitations'])
                    ->filter(fn ($invitation) => !is_null($invitation['sent_at']))
                    ->keys(),
                [
                    'allowed'  => true,
                    'notified' => true,
                ]
            );

        });
    }
}
