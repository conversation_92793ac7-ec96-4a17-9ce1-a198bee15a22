<?php

namespace Database\Seeders;

use App\Enums\Country;
use App\Enums\LengthType;
use App\Enums\SolutionOrigin;
use App\Models\Company;
use App\Models\Solution;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Libs\Overseer\PublishStatus;

class SolutionsSeeder extends Seeder
{
    // Required, otherwise solutions would regenerate their own public ids
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (Solution::count() > 0) {
            $this->command->warn('Solutions seeder already run, skipping...');

            return;
        }

        $this->seedSolutionsForVendorCompany();
        $this->seedSolutionsForSecondVendorCompany();
    }

    private function seedSolutionsForVendorCompany(): void
    {
        /** @var Company $company */
        $company = Company::with('vendor')->where('is_vendor', true)->first();
        if (! $company) {
            $this->command->error('Cannot seed solutions as vendor company does not exist, skipping...');

            return;
        }

        $solutions = [
            [
                'solution' => [
                    'id' => 1,
                    'public_id' => '7gbWD1',
                    'vendor_id' => $company->vendor->id,
                    'publish_status' => PublishStatus::Published,
                    'name' => 'We are proud of this solution',
                    'description' => 'We are not gonna tell what it is but our claim still stands. We are proud.',
                    'about' => 'Lorem ipsum x 4 paragraphs. Should be larger than description so here is this disclaimer to achieve that',
                    'cover_resource_id' => 2,
                    'country' => Country::Germany,
                    'main_industry_id' => 19, // Cloud
                    'length_type' => LengthType::Ongoing,
                    'in_house' => false,
                ],
                'client' => [
                    'anonymous' => true,
                ],
                'technologies' => [
                    100, // Java
                    218, // Vue
                    148, // PHP
                    132, // NodeJS
                ],
            ],
            [
                'solution' => [
                    'id' => 2,
                    'public_id' => 'xd0KCG',
                    'vendor_id' => $company->vendor->id,
                    'publish_status' => PublishStatus::Draft,
                    'name' => 'Drafted solution',
                    'description' => 'Not sure what this is going to end up being but oh boi here we go',
                    'about' => 'Placeholder',
                    'cover_resource_id' => 2,
                    'country' => Country::Croatia,
                    'main_industry_id' => 34, // Design (UI/UX)
                    'length_type' => LengthType::Years,
                    'length' => 3,
                    'in_house' => true,
                ],
            ],
            [
                'solution' => [
                    'id' => 3,
                    'public_id' => '1iLiNM',
                    'vendor_id' => $company->vendor->id,
                    'publish_status' => PublishStatus::AwaitingApproval,
                    'name' => 'Another great success',
                    'description' => 'We believe this one should be published mate',
                    'about' => 'Pretty please UwU',
                    'cover_resource_id' => 2,
                    'country' => Country::Malaysia,
                    'main_industry_id' => 20, // Computer Vision
                    'length_type' => LengthType::Months,
                    'length' => 9,
                    'in_house' => false,
                ],
                'client' => [
                    'anonymous' => false,
                    'review' => 'Really good',
                    'reviewer' => 'Feri',
                    'reviewer_position' => 'Šefik',
                ],
            ],
            [
                'solution' => [
                    'id' => 4,
                    'public_id' => '7sfd36',
                    'vendor_id' => $company->vendor->id,
                    'publish_status' => PublishStatus::Published,
                    'origin' => SolutionOrigin::CompanyWebsite,
                    'origin_id' => 'our-great-solution',
                    'name' => 'Hidden solution',
                    'description' => 'I am hidden',
                    'about' => 'Really you cannot see me but I am there',
                    'cover_resource_id' => null,
                    'country' => null,
                    'main_industry_id' => null,
                    'length_type' => LengthType::Unspecified,
                    'length' => null,
                    'in_house' => null,
                ],
                'client' => [
                    'anonymous' => false,
                    'review' => 'Really good',
                    'reviewer' => 'Feri',
                    'reviewer_position' => 'Šefik',
                ],
            ],
        ];

        $this->seedSolutions($solutions);
    }

    private function seedSolutionsForSecondVendorCompany(): void
    {
        /** @var Company $company */
        $company = Company::with('vendor')->where('is_vendor', true)->skip(1)->first();
        if (! $company) {
            $this->command->error('Cannot seed solutions as second vendor company does not exist, skipping...');

            return;
        }

        $solutions = [
            [
                'solution' => [
                    'id' => 10,
                    'public_id' => '4kyQ2A',
                    'vendor_id' => $company->vendor->id,
                    'publish_status' => PublishStatus::Published,
                    'name' => 'YAPA',
                    'description' => 'Yet another parking app',
                    'about' => 'Exactly. Another parking app. Because there are simply not enough, just like JavaScript frameworks!',
                    'cover_resource_id' => 2,
                    'country' => Country::UnitedStates,
                    'main_industry_id' => 9, // Automotive
                    'length_type' => LengthType::Years,
                    'length' => 1,
                    'in_house' => true,
                ],
                'technologies' => [
                    1, // .NET
                    10, // Android
                    97, // iOS
                ],
            ],
            [
                'solution' => [
                    'id' => 11,
                    'public_id' => '26pWvG',
                    'vendor_id' => $company->vendor->id,
                    'publish_status' => PublishStatus::Draft,
                    'name' => 'We not sure yet',
                    'description' => 'We had a good idea abut that guy left the company',
                    'about' => 'But it will be lit we promise',
                    'cover_resource_id' => 2,
                    'country' => Country::UnitedKingdom,
                    'main_industry_id' => 58, // Human Resources
                    'length_type' => LengthType::Unspecified,
                    'in_house' => true,
                ],
            ],
        ];

        $this->seedSolutions($solutions);
    }

    private function seedSolutions(array $solutions): void
    {
        collect($solutions)->each(function ($data) {
            /** @var Solution $solution */
            $solution = Solution::forceCreate($data['solution']);

            $solution->industries()->sync($data['industries'] ?? []);
            $solution->technologies()->sync($data['technologies'] ?? []);

            if ($data['client'] ?? false) {
                $solution->client()->forceCreate($data['client']);
            }
        });
    }
}
