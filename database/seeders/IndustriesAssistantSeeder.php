<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Industry;
use App\Enums\Enterprise\IndustryType;

class IndustriesAssistantSeeder extends Seeder
{
    public function run(): void
    {
        $primaryIndustries = [
            ['id_string' => 'banking_financial_services', 'name' => 'Banking & Financial Services'],
            ['id_string' => 'insurance_insurtech', 'name' => 'Insurance & InsurTech'],
            ['id_string' => 'healthcare_life_sciences', 'name' => 'Healthcare & Life Sciences'],
            ['id_string' => 'manufacturing_industry_4_0', 'name' => 'Manufacturing & Industry 4.0'],
            ['id_string' => 'energy_utilities', 'name' => 'Energy & Utilities'],
            ['id_string' => 'telecommunications', 'name' => 'Telecommunications'],
            ['id_string' => 'government_public_sector', 'name' => 'Government & Public Sector'],
            ['id_string' => 'logistics_supply_chain', 'name' => 'Logistics & Supply Chain'],
            ['id_string' => 'education_edtech', 'name' => 'Education & EdTech'],
            ['id_string' => 'construction_real_estate', 'name' => 'Construction & Real Estate'],
            ['id_string' => 'automotive_mobility', 'name' => 'Automotive & Mobility'],
            ['id_string' => 'aerospace_defense', 'name' => 'Aerospace & Defense'],
            ['id_string' => 'agriculture_agtech', 'name' => 'Agriculture & AgTech'],
            ['id_string' => 'media_entertainment', 'name' => 'Media & Entertainment'],
            ['id_string' => 'information_technology_and_digital_services', 'name' => 'Information Technology & Digital Services'],
        ];

        foreach ($primaryIndustries as $industry) {
            Industry::updateOrCreate(
                ['id_string' => $industry['id_string']],
                [
                    'name' => $industry['name'],
                    'type' => IndustryType::Primary,
                ]
            );
        }

        $secondaryIndustries = [
            ['id_string' => 'retail_ecommerce', 'name' => 'Retail & E-Commerce'],
            ['id_string' => 'cybersecurity', 'name' => 'Cybersecurity'],
            ['id_string' => 'artificial_intelligence_machine_learning', 'name' => 'Artificial Intelligence & Machine Learning'],
            ['id_string' => 'cloud_it_infrastructure', 'name' => 'Cloud & IT Infrastructure'],
            ['id_string' => 'iot_smart_devices', 'name' => 'IoT & Smart Devices'],
            ['id_string' => 'saas_enterprise_software', 'name' => 'SaaS & Enterprise Software'],
            ['id_string' => 'blockchain_digital_identity', 'name' => 'Blockchain & Digital Identity'],
            ['id_string' => 'sustainability_esg', 'name' => 'Sustainability & ESG'],
            ['id_string' => 'customer_experience_ux', 'name' => 'Customer Experience & UX'],
            ['id_string' => 'data_analytics_business_intelligence', 'name' => 'Data Analytics & Business Intelligence'],
            ['id_string' => 'legal_tech_compliance', 'name' => 'Legal Tech & Compliance'],
        ];

        foreach ($secondaryIndustries as $industry) {
            Industry::updateOrCreate(
                ['id_string' => $industry['id_string']],
                [
                    'name' => $industry['name'],
                    'type' => IndustryType::Secondary,
                ]
            );
        }
    }

    public static function getRandomPrimaryIndustryId(): int
    {
        return Industry::where(['type' => IndustryType::Primary])->inRandomOrder()->first()->value('id');
    }
}
