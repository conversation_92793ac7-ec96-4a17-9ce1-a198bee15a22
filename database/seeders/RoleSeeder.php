<?php

namespace Database\Seeders;

use App\Enums\UserRole;
use Spatie\Permission\Models\Role;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{

    public function run(): void
    {
        foreach (UserRole::cases() as $role) {
            Role::createOrFirst(['name' => $role->value, 'guard_name' => 'api']);
            Role::createOrFirst(['name' => $role->value, 'guard_name' => 'web']);
        }
    }
}
