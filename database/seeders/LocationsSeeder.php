<?php

namespace Database\Seeders;

use App\Enums\Country;
use App\Models\Location;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class LocationsSeeder extends Seeder
{
    public function run(): void
    {
        // TODO(<PERSON>): Temporarily seed locations from enum.
        $countries = Country::cases();

        foreach ($countries as $country) {
            Location::firstOrCreate(
                ['id_string' => $country->value],
                ['name' => Str::headline($country->name)]
            );
        }
    }
}
