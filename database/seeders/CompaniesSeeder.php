<?php

namespace Database\Seeders;

use App\Enums\Country;
use App\Models\Company;
use App\Models\Technology;
use App\Models\Vendor;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Libs\Overseer\PublishStatus;

class CompaniesSeeder extends Seeder
{
    // Required, otherwise candidates would regenerate their own public ids
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (Company::count() > 0) {
            $this->command->warn(self::class . ' already run, skipping...');
            return;
        }

        $this->seedVendorCompany();
        $this->seedClientCompany();
        $this->seedUnapprovedCompany();
        $this->seedSpecificVendors();
        
    }

    private function seedVendorCompany(): void
    {
        /** @var Company $company */
        $company = Company::forceCreate([
            'id' => 1,
            'public_id' => str_random(6),
            'publish_status' => PublishStatus::Published,
            'is_public' => true,
            'is_vendor' => true,
            'name' => 'Vendor company',
            // FIXME: logo/cover fake resources
            'owner' => 'Holding International Inc.',
            'hq' => 'Toronto',
            'country' => Country::Canada,
            'website' => 'https://google.com/',
            'linkedin' => 'https://linkedin.com/holding-international',
            'founded' => 1997,
            'about' => 'Very good company can recommend',
            'presentation_url' => 'https://nordics.io/pdf/Presentation.pdf',
        ]);

        /** @var Vendor $vendor */
        $vendor = $company->vendor()->forceCreate([
            'id' => 1,
            'public_id' => 'ghBg6v',
            'publish_status' => PublishStatus::Published,
            'main_industry_id' => 12, // Banking & Fintech
            'employees' => 45,
            'developers' => 29,
            'offering_resources' => true,
            'offering_solutions' => true,
            'payment_time_and_material' => true,
            'payment_fixed_price' => true,
            'notify_irrelevant_offers' => true,
            'rate_medior' => 30,
            'rate_senior' => 50,
        ]);

        $vendor->technologies()->sync([
            100, // Java
            218, // Vue
            148, // PHP
            132, // NodeJS
        ]);
    }

    private function seedClientCompany(): void
    {
        Company::forceCreate([
            'id' => 2,
            'public_id' => '90S1Rn',
            'publish_status' => PublishStatus::Published,
            'is_public' => true,
            'is_vendor' => false,
            'name' => 'Client company',
            // FIXME: logo/cover fake resources
            'owner' => 'Microsoft Corporation',
            'hq' => 'Palo Alto',
            'country' => Country::UnitedStates,
            'website' => 'https://microsoft.com/',
            'linkedin' => 'https://linkedin.com/microsoft',
            'founded' => 1987,
            'about' => 'Secret branch of MS in a search for good developers',
            'presentation_url' => 'https://microsoft.com/sikrit',
        ]);
    }

    private function seedUnapprovedCompany(): void
    {
        Company::forceCreate([
            'id' => 3,
            'public_id' => 'dy8hxn',
            'publish_status' => PublishStatus::AwaitingApproval,
            'is_public' => false,
            'is_vendor' => false,
            'name' => 'The Hockey Company',
            // FIXME: logo/cover fake resources
            'owner' => 'Paul Stastny',
            'hq' => 'Bratislava',
            'country' => Country::Slovakia,
            'website' => 'https://hockey.example.com/',
            'linkedin' => 'https://linkedin.com/the-hockey-company',
            'founded' => 2022,
            'about' => 'We do hockey, nothing else really',
            'presentation_url' => 'https://hockey.example.com/nemame.pdf',
        ]);
    }

    private function seedRandomVendor(string $companyName): void
    {
        /** @var Company $company */
        $company = Company::create([
            'public_id' => str_random(6),
            'publish_status' => PublishStatus::Published,
            'is_public' => true,
            'is_vendor' => true,
            'name' => $companyName,
            // FIXME: logo/cover fake resources
            'owner' => fake()->company() . ' Holding, Inc.',
            'hq' => fake()->city(),
            'country' => strtolower(fake()->countryCode()),
            'website' => 'https://nordics.io/',
            'linkedin' => 'https://www.linkedin.com/company/nordicsio',
            'founded' => fake()->numberBetween(1900, 2025),
            'about' => fake()->catchPhrase(),
            'presentation_url' => 'https://nordics.io/pdf/Presentation.pdf',
        ]);

        $numberOfEmployees = fake()->numberBetween(1, 10000);
        $numberOfDevelopers = fake()->numberBetween(1, $numberOfEmployees);

        /** @var Vendor $vendor */
        $vendor = $company->vendor()->create([
            'public_id' => str_random(6),
            'publish_status' => PublishStatus::Published,
            'main_industry_id' => IndustriesAssistantSeeder::getRandomPrimaryIndustryId(),
            'employees' => $numberOfEmployees,
            'developers' => $numberOfDevelopers,
            'offering_resources' => false,
            'offering_solutions' => true,
            'payment_time_and_material' => true,
            'payment_fixed_price' => true,
            'notify_irrelevant_offers' => false,
        ]);

        $vendor->technologies()->sync(TechnologiesAssistantSeeder::getRandomTechnologyIds(10));
    }

    private function seedSpecificVendors(): void
    {
        $vendors = [
            [
                'company' => [
                    'id' => 4,
                    'uuid' => '554337ab-c656-46d8-b0e5-436af173fd21',
                    'public_id' => 'wMacRK',
                    'name' => 'I.S.D.D. plus',
                    'country' => 'sk',
                    'website' => 'https://isdd.sk/',
                    'linkedin' => 'https://www.linkedin.com/company/isdd-plus-sro/',
                    'founded' => 1999,
                    'is_public' => false,
                    'owner' => 'Vladimir Šošovička',
                    'hq' => 'Bratislava',
                    'about' => 'We\'re a software company that provides complex services in the field of information technologies. We offer credible, powerful and secure solutions, which help to make work processes more effective and business communication easier.',
                    'presentation_url' => null,
                ],
                'vendor' => [
                    'id' => 4,
                    'public_id' => 'JCDtJj',
                    'main_industry_id' => 56,
                    'employees' => 100,
                    'developers' => 95,
                    'offering_resources' => true,
                    'offering_solutions' => true,
                    'payment_time_and_material' => true,
                    'payment_fixed_price' => true,
                    'notify_irrelevant_offers' => true,
                    'rate_junior' => 10,
                    'rate_medior' => null,
                    'rate_senior' => null,
                    'rate_lead' => null,
                    ],
                    'technologies' => ['Angular (Web Framework)','C# (Programming Language)','Java (Programming Language)','JavaScript (Programming Language)','.NET Framework','Kotlin','Python (Programming Language)','PL/SQL','TensorFlow','Spring Framework','Apache Kafka','Spring Boot','PHP (Scripting Language)','Laravel Lumen','JQuery','React.js (Javascript Library)','React Redux','Vue.js (Javascript Library)','Polymer library','Node.js (Javascript Library)','MySQL','MariaDB','PostgreSQL','Apache Hadoop'],
            ],
            [
                'company' => [
                    'id' => 5,
                    'uuid' => '91b91437-4aae-493e-8b18-75b7dd541134',
                    'public_id' => 'EBtLLL',
                    'name' => 'Objectify, s. r. o.',
                    'country' => 'sk',
                    'website' => 'https://koderia.sk',
                    'linkedin' => 'https://www.linkedin.com/company/koderiask/mycompany/',
                    'founded' => 2014,
                    'is_public' => false,
                    'owner' => 'Ing. Igor Dysko',
                    'hq' => 'Bratislava',
                    'about' => 'Objectify is a slovak softwarehouse specializing in the development of robust, highly customized solutions for large clients. For over 10 years, we’ve been delivering projects that tackle key business and technical challenges — from enterprise systems and biometric solutions to advanced infrastructure data management.What sets us apart?Expertise in complex data systems:We integrate and manage large volumes of data from diverse sources — including daily synchronizations, data model normalization, analytical layers, and graph databases.Domain knowledge:Our teams understand both technology and the industries we work in — telecommunications, biometrics, and manufacturing. We collaborate directly with our clients’ technicians, managers, and analysts.Long-term partnerships:We’ve worked with clients like Orange Group and Innovatrics for many years. The systems we’ve developed are used by hundreds of users across multiple countries.Practical solutions with high added value:We don’t just record data — we help analyze it. Outages, impact assessments, backup paths, operational dependencies — we turn raw data into actionable insights.In addition to software development, we also provide IT specialist outsourcing services to our clients. Thanks to our ongoing delivery of IT experts for clients in sectors such as banking, telecommunications, healthcare, and transportation, we maintain a valuable database of professionals who can meet your staffing needs at every stage of the software development lifecycle.',
                    'presentation_url' => 'https://koderia.sk',
                ],
                'vendor' => [
                    'id' => 5,
                    'public_id' => '92pRpi',
                    'main_industry_id' => 56,
                    'employees' => 60,
                    'developers' => 50,
                    'offering_resources' => true,
                    'offering_solutions' => true,
                    'payment_time_and_material' => true,
                    'payment_fixed_price' => true,
                    'notify_irrelevant_offers' => true,
                    'rate_junior' => 15,
                    'rate_medior' => 25,
                    'rate_senior' => 40,
                    'rate_lead' => 60,
                    ],
                    'technologies' => ['.NET Framework','.NET Core','Android (Operating System)','Angular (Web Framework)','Amazon Web Services','BI/DWH','C (Programming Language)','C# (Programming Language)','C++ (Programming Language)','Java (Programming Language)','JavaScript (Programming Language)','Kotlin','Linux','Microsoft Azure','Microsoft Dynamics 365','MongoDB','MySQL','Next.js (Javascript Library)','Node.js (Javascript Library)','PHP (Scripting Language)','PL/SQL','PostgreSQL','Python (Programming Language)','Project Management','React Native','React.js (Javascript Library)','RESTful API','Ruby (Programming Language)','Ruby On Rails','SAP Applications','Spring Boot','Spring Framework','SQL (Programming Language)','TypeScript','Vue.js (Javascript Library)','AngularJS','Azure DevOps Server','Cloud Platform System','DevOps','Docker (Software)','Git (Version Control System)','Gitlab','GitLab CI/CD','Kubernetes','Microsoft Power BI Report Server','Terraform','Apple IOS'],
            ],
            [
                'company' => [
                    'id' => 6,
                    'uuid' => '6b252ed1-c46f-44c1-afe6-5d63a2e17930',
                    'public_id' => 'IYZTy5',
                    'name' => 'TITANS freelancers, s.r.o.',
                    'country' => 'sk',
                    'website' => 'http://www.titans.sk',
                    'linkedin' => 'https://www.linkedin.com/company/titans-freelancers/posts/?feedView=all',
                    'founded' => 2013,
                    'is_public' => false,
                    'owner' => 'Sandberg Holdco II SARL / TITANS holding, s.r.o.',
                    'hq' => 'Bratislava',
                    'about' => null,
                    'presentation_url' => null,
                ],
                'vendor' => [
                    'id' => 6,
                    'public_id' => 'pn8K4S',
                    'main_industry_id' => 114,
                    'employees' => 35,
                    'developers' => 30000,
                    'offering_resources' => true,
                    'offering_solutions' => false,
                    'payment_time_and_material' => true,
                    'payment_fixed_price' => false,
                    'notify_irrelevant_offers' => false,
                    'rate_junior' => 15,
                    'rate_medior' => 25,
                    'rate_senior' => 40,
                    'rate_lead' => 60,
                    ],
                    'technologies' => ['.NET Framework']
            ],
            [
                'company' => [
                    'id' => 7,
                    'uuid' => 'e13a19ff-fed3-4b3d-bfa4-81f883da57a0',
                    'public_id' => 'kOg9vG',
                    'name' => 'CoolPeople GmbH',
                    'country' => 'at',
                    'website' => 'https://www.coolpeople.at/',
                    'linkedin' => 'https://www.linkedin.com/company/coolpeople/',
                    'founded' => 2005,
                    'is_public' => true,
                    'owner' => 'CoolPeople Technology a.s.',
                    'hq' => 'Vienna',
                    'about' => 'Our core service is IT outsourcing (bodyshop/permanent placement) – the flexible rental of IT specialists for our clients\' projects. The key advantage of IT bodyshop is that clients receive exactly the temporary capacity of IT specialists they need for their projects. They don\'t have to worry about sourcing, mobilizing, and demobilizing them. CoolPeople provides all these services, including all guarantees and warranties.Our complementary service is IT recruitment – ​​the placement of IT specialists who work directly with the client. If our client needs an IT expert who will become a long-term part of their team, we find and place them as a permanent recruitment service with all the usual guarantees. Alternatively, we can place a so-called Transfer2Core IT expert who has proven so successful that the client is interested in a long-term collaboration.Our other service is Managed Services (MSP) – managed services for clients that consolidate IT capabilities from vendors. These services are particularly suitable for clients who want to consolidate their IT capacity providers under a single provider with guaranteed Service Level Agreements (SLAs).',
                    'presentation_url' => null,
                ],
                'vendor' => [
                    'id' => 7,
                    'public_id' => 'r0ms6h',
                    'main_industry_id' => 6,
                    'employees' => 194,
                    'developers' => 1300,
                    'offering_resources' => true,
                    'offering_solutions' => true,
                    'payment_time_and_material' => true,
                    'payment_fixed_price' => true,
                    'notify_irrelevant_offers' => true,
                    'rate_junior' => 43,
                    'rate_medior' => 50,
                    'rate_senior' => 65,
                    'rate_lead' => 82,
                    ],
                    'technologies' => ['.NET Framework','.NET Core','Access Controls','Adobe Experience Cloud','Adobe XD','Advanced Business Application Programming (ABAP)','Agile Coaching','Akka (Toolkit)','Alation','ALM - Application Lifecycle Management','Android (Operating System)','Angular (Web Framework)','Apache Airflow','Apache Camel','Apache Hive','Apache Maven','Apache NiFi','Apache Parquet','Appian (Software)','Appium','Argo CD','Asana','Artificial Intelligence','Atlassian Confluence','Amazon Web Services','AWS Glue','Azure Cloud Services','Azure Databricks','Azure Pipelines','Azure Machine Learning','BI/DWH','Blazor','Business Intelligence','Bloomberg Terminal','C (Programming Language)','C# (Programming Language)','C++ (Programming Language)','Change Management','ChangeMan (Software)','CI/CD','Cisco Networking','Clojure','Cloud Security','Cloudflare','Cortex XDR','Cucumber (Software)','Customer Information Control System (CICS)','CyberArk','Dart (Programming Language)','Data Analysis','Data Engineering','Data Integration','Data Science','Data Visualization','Dataflow','Design Thinking','DevSecOps','Documentum','Dynatrace','Eclipse (Software)','Electronic Data Capture (EDC)','Electronic Document Management','Espresso','Espresso (Android Testing Framework)','Extract Transform Load (ETL)','Feature Engineering','Figma (Design Software)','Firebase','Firewall','Fraud Detection','Github','Go (Programming Language)','Google Ads','Google Cloud Dataproc','Google Pay','GraphQL','gRPC','Identity And Access Management','IdentityIQ','Imperva','Information Technology Infrastructure Library','Infrastructure as Code (IaC)','Integration Testing','Intelex','IntelliJ IDEA','International Software Testing Qualifications Board (ISTQB) Certified','Investment Accounting','Ionic Framework','Apple IOS','IT Infrastructure','Java (Programming Language)','JavaScript (Programming Language)','JIRA','Job Entry Subsystem 2/3','JSON','Junit','Jupyter','Kotlin','Laravel','Large Language Modeling','Lean Startup Methodology','LinkedIn Sales Navigator','Linode','Linux','Load Balancing','Lua (Scripting Language)','Machine Learning','MariaDB','Medidata Rave','Mendix Low-Code Platform','Microfrontend (MFE)','Microservices','Microsoft 365','Microsoft Azure','Microsoft Dynamics 365','Microsoft Office','Microsoft Outlook','MicroStrategy','MongoDB','Mortgage Loans','MuleSoft','Multiprotocol Label Switching','MySQL','NativeScript','Natural Language Processing (NLP)','NetBeans','Network Architecture','Network Security','Next.js (Javascript Library)','Node.js (Javascript Library)','Nuxt.js (Javascript Library)','OAuth','Objective-C (Programming Language)','ONESOURCE Indirect Tax (Software)','OpenID','Palantir Foundry','Palo Alto Firewalls','PHP (Scripting Language)','PL/SQL','Play Framework','Playwright (Software Testing)','PostGIS','PostgreSQL','Predictive Analytics','PRINCE2 (PRojects IN Controlled Environments 2)','Prisma Access','Prisma SASE','Private Cloud','Project Management','Public Cloud','Public Key Infrastructure','PySpark','Python (Programming Language)','React Native','Qualtrics','Quality Assurance','React.js (Javascript Library)','Redis','Relational Databases','Release Management','RESTful API','REST Assured','Risk Management','Ruby (Programming Language)','Ruby On Rails','Rust (Programming Language)','S3','SAP Applications','SAP CO','SAP CRM','SAP DMS','SAP ERP','SAP EWM','SAP FI','SAP Fiori','SAP Governance Risk And Compliance','SAP Human Resource Management Software','SAP Logistics','SAP Logistics Execution System','SAP Material Management','SAP Plant Maintenance','SAP Production Planning','SAP Project System','SAP Quality Management','SAP Sales And Distribution','SAP Security','SAP SRM','SAP Supply Chain Management (SCM)','SAP Treasury And Risk Management','Sarbanes-Oxley Act (SOX) Compliance','SAP Warehouse Management','Saviynt','Scala (Programming Language)','Scrum (Software Development)','Security Assertion Markup Language (SAML)','Selenium (Software)','Simple Object Access Protocol (SOAP)','Software Architecture','Spring Framework','Spring Boot','Sprinklr','SQL (Programming Language)','Statistical Modeling','Swagger UI','Symfony','Systems Integration','TailwindCSS','The Open Group Architecture Framework (TOGAF)','Time Series Analysis And Forecasting','Tokenization','TypeScript','Unity Engine','Unreal Engine','Vue.js (Javascript Library)','Virtual Private Networks (VPN)','Workday (Software)','XPEDITER','YAML','Zapier','z/OS','Autodesk 3DS Max (3D Graphics Software)','Adalo','Adobe Experience Manager','Aerospike Database','Algolia','Analyst','AngularJS','Ansible','Apache CXF','Apache JMeter','Apache Kafka','Apache Solr','Apache Spark','Apache Tomcat','Application Programming Interface (API)','Apollo GraphQL','ARCore','ARKit','ASP.NET','ASP.NET Core','Assembly Language','Aurora DB','AWS EC2','AWS Elastic Beanstalk','AWS OpenSearch','AWS IoT Greengrass','AWS Lambda','Azure DevOps Server','Bash/Shell','Bitbucket','Bootstrap (Front-End Framework)','BluePrism (RPA Software)','Bubble.io','Business Analytics','Cacti (Software)','Apache Cassandra','Chef (Configuration Management Tool)','Cloud Platform System','Hadoop Cloudera','COBOL (Programming Language)','CodeIgniter','Collibra','Compass','Continues Integration/Delivery - CI/CD','Apache Cordova','Couchbase Servers','CouchDB','Cascading Style Sheets (CSS)','CypressIO','Database Systems','Delphi (Programming Language)','Deno','Designs & 3D','DevOps','Dialogflow (Google Service)','DigitalOcean','Django (Web Framework)','Docker (Software)','Drupal','Amazon DynamoDB','EDW','EggPlant AI','Elastic Stack','Elasticsearch','Electron','Elixir (Programming Language)','Entity Framework','Ember.js (Javascript Library)','Erlang','Express.js (Javascript Library)','F# (Programming Language)','FastAPI','Flask (Web Framework)','Flow','Flutter (Software)','FlutterFlow','Galera Cluster','Gatsby.js (Javascript Library)','GCP','Git (Version Control System)','Gitlab','GitLab CI/CD','Google App Engines','Google Cloud Platform (GCP)','Gradle','Grafana','Grafana Loki','Groovy (Programming Language)','GSM Networks','Grunt.js (Javascript Library)','Gulp.js (Javascript Library)','Apache Hadoop','Handlebars.js (Javascript Library)','Hazelcast','Heroku','Hibernate (Java)','HTC Vive','HTML/CSS','IBM Blueworks Live','IBM Business Automation Workflow','IBM Business Process Manager (BPM)','IBM Integration Bus','IBM MQ','IBM Operational Decision Manager (ODM)','IBM Websphere Application Server','IBM Tools','IBM DB2','IBM Cloud or Watson','Ionic Framework','IOS','Jamstack','Jakarta EE','Jedox','Jenkins','Jetpack Compose','Joomla CMS','JQuery','Julia (Programming Language)','K8s orchestration','Keras (Neural Network Library)','Kubernetes','Laravel Lumen','Leaner Style Sheets (LESS)','Make (Software)','MATLAB','Microsoft Hololens','Microsoft Power BI Report Server','Microsoft SharePoint','Microsoft SQL Servers','Mobile Application Development','Mustache','Neo4j','NestJS','Netlify','Nette','Nexus','Nginx','NoSQL','NumPy (Python Package)','NW.js (Javascript Library)','Objective-C (Programming Language)','October CMS','Oculus Quest','Oculus Rift','OpenCart','OpenStack','OpenCV','Oracle','Oracle Application Express - APEX','Oracle Databases','Other','Pandas (Python Package)','Pega PRPC','Perl (Programming Language)','Phalcon (PHP Framework)','PhoneGap','Pimcore','Polymer library','Postman API Platform','Windows PowerShell','PrestaShop','Prismic CMS','Progressive Web Applications - PWA','Prometheus (Software)','Python (Programming Language)','Qlik Sense (Data Analytics Software)','Qore','Qt (Software)','RabbitMQ','Rackspace Cloud','Ranorex','Raspbery Pi','RavenDB','React Redux','Render.com','RethinkDB','Robotic Process Automation - RPA','Robotics','Ruby (Programming Language)','Ruby On Rails','Rust (Programming Language)','Sails.js (Javascript Library)','Salesforce','SAP OpenUI5','Sass / SCSS','SAP Applications','Sentry','Serverless Computing','ServiceMax','ServiceNow','Shopify','Shopsys Framework','Shopware','Sketch (Design Software)','SmartBear','Simple Object Access Protocol (SOAP)','Solana','Solidity (Programming Language)','Spring Framework','Spring Boot','SQL (Programming Language)','SQLite','Storyblok CMS','Swift (Programming Language)','SwiftUI','Symfony','TensorFlow','Terraform','Transact-SQL','Tricentis Tosca','TYPO3','UiPath (RPA Software)','Umbraco CMD','Unity Engine','Unreal Engine','Vaadin','Vagrant','Valve Index','Visual Basic (Programming Language)','Vuetify','Vuforia','Webpack','WinAutomation RPA','Windows Communication Foundation - WCF','Winter CMS','WooCommerce','WordPress CMS','Xamarin','Yii Framework','Zend Framwork',],
            ],
            [
                'company' => [
                    'id' => 8,
                    'uuid' => 'c99b85b9-a7b0-4fe1-8d03-716167458e7b',
                    'public_id' => 'Ug3qmW',
                    'name' => 'Blocshop s.r.o.',
                    'country' => 'cz',
                    'website' => 'https://www.blocshop.io',
                    'linkedin' => 'https://www.linkedin.com/company/blocshop',
                    'founded' => 2012,
                    'is_public' => false,
                    'owner' => 'Greg Crawley',
                    'hq' => 'Prague',
                    'about' => 'Based in Prague with offices in Brno and Tbilisi, Blocshop is a community {family/group/collection} of top software engineers building best in class solutions. We are proud of our mantra "keeping our commitments since 2012"​ as it emphasizes our company-wide focus on meeting our objectives. We are a boutique business who care deeply about staying on top of technology trends and leveraging that expertise to maintain lasting relationships with our colleagues and clients.',
                    'presentation_url' => 'https://blocshop.io/',
                ],
                'vendor' => [
                    'id' => 8,
                    'public_id' => 'pdS0s3',
                    'main_industry_id' => 12,
                    'employees' => 25,
                    'developers' => 20,
                    'offering_resources' => true,
                    'offering_solutions' => true,
                    'payment_time_and_material' => true,
                    'payment_fixed_price' => true,
                    'notify_irrelevant_offers' => true,
                    'rate_junior' => 55,
                    'rate_medior' => 60,
                    'rate_senior' => 66,
                    'rate_lead' => 73,
                ],
                'technologies' =>  ['.NET Framework','.NET Core','Agile Coaching','Android (Operating System)','Angular (Web Framework)','Azure Cloud Services','C# (Programming Language)','Data Integration','Design Thinking','Dataflow','Figma (Design Software)','Apple IOS','Ionic Framework','Java (Programming Language)','JIRA','JSON','Kotlin','Lean Startup Methodology','Microservices','Microsoft Azure','Node.js (Javascript Library)','PRINCE2 (PRojects IN Controlled Environments 2)','Project Management','Quality Assurance','Python (Programming Language)','React Native','React.js (Javascript Library)','Relational Databases','RESTful API','Spring Framework','AngularJS'],
            ]
        ];

        foreach ($vendors as $data) {
        
            /** @var Company $company */
            $company = Company::forceCreate([
                ...$data['company'],
                'country' => strtolower($data['company']['country']),
                'publish_status' => PublishStatus::Published,
                'is_vendor' => true,
            ]);

            /** @var Vendor $vendor */
            $vendor = $company->vendor()->forceCreate([
                ...$data['vendor'],
                'publish_status' => PublishStatus::Published,
            ]);

            $techIds = Technology::whereIn('name', $data['technologies'])->pluck('id')->all();
            $vendor->technologies()->sync($techIds);
        }
    }
}