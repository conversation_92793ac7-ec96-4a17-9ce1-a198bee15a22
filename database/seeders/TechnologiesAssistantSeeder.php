<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Technology;
use App\Enums\Enterprise\TechnologyType;
use Illuminate\Support\Facades\DB;

class TechnologiesAssistantSeeder extends Seeder
{
    public function run(): void
    {
        // Disable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        $sql = file_get_contents(database_path('seeders/data/technologies.sql'));
        DB::unprepared($sql);

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }

    public static function getRandomTechnologyIds(int $count)
    {
        return self::getRandomItemsIds($count, TechnologyType::Technology);
    }

    public static function getRandomToolIds(int $count)
    {
        return self::getRandomItemsIds($count, TechnologyType::Tool);
    }

    private static function getRandomItemsIds(int $count, TechnologyType $type)
    {
        return Technology::where('type', $type)->inRandomOrder()->take($count)->pluck('id');
    }
}
