<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Legacy reference tables.
        $this->call(Legacy\IndustriesSeeder::class);
        $this->call(Legacy\EmployeePositionsSeeder::class);

        // Reference tables.
        $this->call(CertificationsSeeder::class);
        $this->call(IndustriesAssistantSeeder::class);
        $this->call(LanguagesSeeder::class);
        $this->call(LocationsSeeder::class);
        $this->call(TechnologiesAssistantSeeder::class);

        // Legacy tables.
        $this->call(Legacy\ResourcesSeeder::class);
        $this->call(Legacy\ClientsSeeder::class);

        // User-related tables.
        $this->call(CompaniesSeeder::class);
        $this->call(RoleSeeder::class);
        $this->call(UsersSeeder::class);
        $this->call(WorkspaceSeeder::class);

        // Tender-related tables.
        $this->call(SolutionsSeeder::class);
        $this->call(TendersSeeder::class);
        $this->call(CandidatesSeeder::class);
        $this->call(Legacy\BenchSpecialistsSeeder::class);
        
        // RFP related tables.
        $this->call(RfpSeeder::class);
    }
}
