<?php

namespace Database\Seeders\Legacy;

use App\Models\EmployeePosition;
use Illuminate\Database\Seeder;

class EmployeePositionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (EmployeePosition::count() > 0) {
            $this->command->warn('Seeder already run, skipping...');

            return;
        }

        $path = database_path('seeders/data/employee-positions.json');
        $employeePositions = json_decode(file_get_contents($path), true);
        collect($employeePositions)->each(
            fn ($employeePosition) => EmployeePosition::create($employeePosition)
        );
    }
}
