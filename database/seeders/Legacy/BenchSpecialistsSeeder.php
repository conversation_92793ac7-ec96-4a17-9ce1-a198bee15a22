<?php

namespace Database\Seeders\Legacy;

use App\Models\BenchSpecialist;
use App\Models\Company;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BenchSpecialistsSeeder extends Seeder
{
    // Required, otherwise bench specialists would regenerate their own public ids
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (BenchSpecialist::count() > 0) {
            $this->command->warn('Bench specialists seeder already run, skipping...');

            return;
        }

        $this->seedBenchSpecialistsForVendorCompany();
        $this->seedBenchSpecialistsForSecondVendorCompany();
    }

    private function seedBenchSpecialistsForVendorCompany(): void
    {
        /** @var Company $company */
        $company = Company::with('vendor')->where('is_vendor', true)->first();
        if (! $company) {
            $this->command->error('Cannot seed solutions as vendor company does not exist, skipping...');

            return;
        }

        $specialists = [
            [
                'id' => 1,
                'public_id' => 's32g1h',
                'candidate_id' => 1,
                'publish_status' => 'published',
                'available_from' => now(),
                'available_to' => now()->addMonth(),
            ],
        ];

        $this->seedBenchSpecialists($company, $specialists);
    }

    private function seedBenchSpecialistsForSecondVendorCompany(): void
    {
        /** @var Company $company */
        $company = Company::with('vendor')->where('is_vendor', true)->skip(1)->first();
        if (! $company) {
            $this->command->error('Cannot seed solutions as second vendor company does not exist, skipping...');

            return;
        }

        $specialists = [
            [
                'id' => 10,
                'public_id' => '5PqCj8',
                'candidate_id' => 1,
                'publish_status' => 'published',
                'available_from' => now()->subMonth()->addDay(),
                'available_to' => now()->addDay(),
            ],
        ];

        $this->seedBenchSpecialists($company, $specialists);
    }

    private function seedBenchSpecialists(Company $company, array $specialists): void
    {
        collect($specialists)->each(fn ($data) => $company->vendor->benchSpecialists()->forceCreate($data));
    }
}
