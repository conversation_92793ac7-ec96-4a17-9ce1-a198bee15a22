<?php

namespace Database\Seeders\Legacy;

use App\Models\Client;
use Illuminate\Database\Seeder;

class ClientsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        if (Client::count() > 0) {
            $this->command->warn('Seeder already run, skipping...');

            return;
        }

        $path = database_path('seeders/data/clients.json');
        $clients = json_decode(file_get_contents($path), true);
        collect($clients)->each(fn ($client) => Client::create($client));
    }
}
