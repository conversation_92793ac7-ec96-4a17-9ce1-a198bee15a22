<?php

namespace Database\Seeders\Legacy;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Libs\Warehouse\Resource;

class ResourcesSeeder extends Seeder
{
    // Required, otherwise resources would regenerate their own public ids
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (Resource::count() > 0) {
            $this->command->warn('Resources seeder already run, skipping...');

            return;
        }

        $this->seedDummyResources();
    }

    private function seedDummyResources(): void
    {
        $this->createDummyForType(1, 'lj6nav', 'user_avatar_image');
        $this->createDummyForType(2, '4Hq5t2', 'solution_cover_image');
        $this->createDummyForType(3, '3z4luE', 'solution_medium_image');
        $this->createDummyForType(4, '28udZc', 'tender_cover_image');
        $this->createDummyForType(5, '456GHJ', 'candidate_cv_file', [
            'format' => 'pdf',
            'mime' => 'application/pdf',
            'size' => 0,
            'name' => 'dummy.pdf',
        ]);
    }

    private function createDummyForType(int $id, string $publicId, string $type, array $extra = []): void
    {
        $dummyThumbnail = [
            'name' => 'dummy',
            'disk' => 'public',
            'directory' => 'dummy',
            'filename' => 'dummy.png',
            'path' => 'dummy/dummy.png',
        ];

        Resource::forceCreate([
            'id' => $id,
            'user_id' => null,
            'type' => $type,
            'public_id' => $publicId,
            'hash' => 'fake_hash',
            'name' => 'dummy',
            'disk' => 'public',
            'directory' => 'dummy',
            'filename' => 'dummy.png',
            'path' => 'dummy/dummy.png',
            'extra' => [
                'thumbnails' => [
                    'preview' => $dummyThumbnail,
                    'carousel_thumbnail' => $dummyThumbnail,
                    '750' => $dummyThumbnail,
                    '500' => $dummyThumbnail,
                    '250' => $dummyThumbnail,
                ],
            ] + $extra,
        ]);
    }
}
