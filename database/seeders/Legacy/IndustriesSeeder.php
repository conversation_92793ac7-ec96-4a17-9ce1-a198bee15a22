<?php

namespace Database\Seeders\Legacy;

use App\Models\Industry;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class IndustriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $industryCount = Industry::count();
        if ($industryCount === 1) {
            // Delete one industry created by migration.
            DB::table('industries')->delete();
        } elseif ($industryCount > 1) {
            $this->command->warn('Seeder already run, skipping...');
            return;
        }

        $path = database_path('seeders/data/industries.json');
        $industries = json_decode(file_get_contents($path), true);
        collect($industries)->each(fn ($industry) => Industry::create($industry));
    }
}
