<?php

namespace Database\Seeders;

use App\Enums\Enterprise\IndustryType;
use App\Enums\Enterprise\SeniorityLevel;
use App\Enums\Enterprise\AssistantStep;
use App\Models\Company;
use App\Models\Industry;
use App\Models\Location;
use App\Models\Rfp;
use App\Models\User;
use App\Repositories\Enterprise\Assistant\RfpTenderRepository;
use Illuminate\Database\Seeder;

class RfpSeeder extends Seeder
{
    protected $rfpTenderRepository;

    public function __construct(RfpTenderRepository $rfpTenderRepository)
    {
        $this->rfpTenderRepository = $rfpTenderRepository;
    }


    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $clientCompany = Company::where('is_vendor', false)->first();
        $user = $clientCompany->users()->first();

        foreach (AssistantStep::cases() as $step) {
            $rfp = $this->createRfp($user, $step);
            for ($i = 0; $i < 3; $i++) {
                $this->creatPositionForRfp($rfp);
            }
        }

        $rfpWithTender = $this->createRfp($user);
        $rfpWithTender->title = "TaskFlow: Advanced Task Management Software (tender created)";
        $rfpWithTender->save();
        for ($i = 0; $i < 3; $i++) {
            $this->creatPositionForRfp($rfpWithTender);
        }
        $this->rfpTenderRepository->createTenderFromRfp($rfpWithTender);

        $this->createTenderRfp();
    }

    private function createRfp(User $user, AssistantStep $step = AssistantStep::Final): Rfp
    {
        static $rfpText = file_get_contents(database_path('seeders/data/project.txt'));

        $rfp = Rfp::create([
            'author_id' => $user->id,
            'title' => "TaskFlow: Advanced Task Management Software (currently at step {$step->value})",
            'text' => $rfpText,
            'start_date' => fake()->dateTimeBetween('+1 months', '+3 months'),
            'end_date' => fake()->dateTimeBetween('+4 months', '+6 months'),
            'timezone' => 'UTC',
            'primary_industry_id' => IndustriesAssistantSeeder::getRandomPrimaryIndustryId(),
            'description' => 'TaskFlow is a next-generation task management platform designed to streamline productivity for teams and individuals, offering features like project management, task customization, collaboration tools, analytics, and integrations.',
            'rfp_api_called' => true,
            'resources_extract_api_called' => true,
            'step' => $step
        ]);
        $rfp->locations()->attach(Location::whereIn('id_string', ['sk', 'cz'])->pluck('id')->toArray());
        // Certifications and secondary industries are not being used or displayed anywhere at the moment.
        // Seeding could be removed if it continues to be the case.
        $rfp->certifications()->attach(1);
        $rfp->secondaryIndustries()->attach(Industry::where('type', IndustryType::Secondary)->take(2)->pluck('id'));
        return $rfp;
    }

    private function createTenderRfp(): Rfp
    {
        $rfp = Rfp::create([
            'id' => '9fac018c-8774-4fa4-adff-1d48f627f096',
            'author_id' => 4,
            'tender_id' => 1,
            'text' => "Our client from the Logistics industry we are currently looking for a well-versed .NET Developer (m/f/x). Tasks & Qualification Development within the eMerge Project Overview: This capacity will be responsible for software development activities under a workstream managed by DHL Development manager for the eMerge project. Their services include codding, unit testing and technical design related activities Key Deliverables: • Software Development based on .NET framework (Blazor) and supported by MS Azure resources • Unit Testing coverage in line with standard policies • Cloud Infrastructure – Configuration, and consumption of cloud resources managed by client • Virtual Machine (VDE) - Development Environment provided by client to leverage activities in scope according to policies, tools and processes Project Details • Location: 100% remote from Europe • Start: ASAP • End: 30th of September 2025 • Volume: ca. 100 MD • Number of Consultants: 1 • Language: English Do you have free capacities to assist our client? Then, we'd be happy to receive a feedback including latest CV and hourly rate indication.",
            'files' => "[\"rfps\\/1755611091_rfp.pdf\"]",
            'step' => AssistantStep::Final,
            'rfp_api_called' => 1,
            'resources_extract_api_called' => 1,
            'resources_suggest_api_called' => 1,
            'resources_extract_api_response' => "{\"resources\":[{\"job_id\":\"da64f3d0-7c55-4c12-a3cf-e28014a33a96\",\"job_title\":\".NET Developer\",\"seniority_level\":\"medior\",\"languages\":[\"en\"],\"hourly_rate_expectations\":{\"min\":null,\"max\":null},\"number_of_resources\":1,\"workload\":100,\"technologies\":[\"f4d64955-0bce-4a0e-8234-88ffa98d9d23\",\"baaa43fd-349b-4230-b6a9-ac81fa6992a1\",\"9db2f2ef-9105-49b9-a6f6-8d5485335228\"],\"mandatory_technologies\":[\"baaa43fd-349b-4230-b6a9-ac81fa6992a1\",\"9db2f2ef-9105-49b9-a6f6-8d5485335228\"]}]}",
            'resources_suggest_api_response' => "{\"resources\":[]}",
            'title' => '.NET Developer for Logistics Project',
            'start_date' => fake()->dateTimeBetween('+2 weeks', '+3 weeks'),
            'end_date' => fake()->dateTimeBetween('+1 months', '+2 months'),
            'timezone' => 'UTC',
            'primary_industry_id' => 120,
            'description' => "Project Overview\nThis capacity will be responsible for software development activities under a workstream managed by DHL Development manager for the eMerge project. Their services include coding, unit testing and technical design related activities.\n\nClient Context\nThe client is from the Logistics industry, specifically DHL, which is a major player in the logistics and supply chain sector.\n\nScope & Deliverables\nKey deliverables include software development based on the .NET framework (Blazor) and supported by MS Azure resources, unit testing coverage in line with standard policies, configuration and consumption of cloud resources managed by the client, and development environment provided by the client to leverage activities in scope according to policies, tools and processes.\n\nTechnical & Collaboration Setup\nThe project will be conducted 100% remotely from Europe. The development environment will be a Virtual Machine (VDE) provided by the client.\n\nCompliance & Special Requirements\nThe project requires adherence to standard policies for unit testing and cloud resource management.\n\nOther Details\nThe project is expected to have a volume of approximately 100 man-days and will involve one consultant. The working language is English.",
            'key_deliverables' => null,
            'milestones' => null,
            'team_composition' => null,
        ]);
        $rfp->locations()->attach(Location::whereIn('id_string', ['sk', 'cz'])->pluck('id')->toArray());
        $rfp->certifications()->attach(1);
        $rfp->secondaryIndustries()->attach(Industry::where('type', IndustryType::Secondary)->take(2)->pluck('id'));
        return $rfp;
    }

    private function creatPositionForRfp(Rfp $rfp, int $technologiesCount = 4, int $toolCount = 3)
    {
        $position = $rfp->rfpPositions()->create([
            'rfp_id' => $rfp->id,
            'job_title' => fake()->jobTitle(),
            'number_of_resources' => fake()->numberBetween(1, 5),
            'seniority_level' => fake()->randomElement(SeniorityLevel::class),
            'workload' => fake()->numberBetween(50, 100),
            'rate_min' => fake()->numberBetween(10, 100),
            'rate_max' => fake()->numberBetween(100, 300),
        ]);
        $position->languages()->attach([1, 2]);
        $position->technologies()->attach(TechnologiesAssistantSeeder::getRandomTechnologyIds($technologiesCount));
        $position->tools()->attach(TechnologiesAssistantSeeder::getRandomToolIds($toolCount));
        return $position;
    }
}
