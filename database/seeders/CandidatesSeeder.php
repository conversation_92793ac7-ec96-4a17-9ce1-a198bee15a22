<?php

namespace Database\Seeders;

use App\Enums\Country;
use App\Enums\Education;
use App\Enums\Seniority;
use App\Models\Candidate;
use App\Models\CandidateExperience;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CandidatesSeeder extends Seeder
{
    // Required, otherwise candidates would regenerate their own public ids
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (Candidate::count() > 0) {
            $this->command->warn('Candidates seeder already run, skipping...');

            return;
        }

        $this->seedCandidatesOfVendorCompany();
        $this->seedCandidatesOfSecondVendorCompany();
        $this->seedTestCandidates();
    }

    public function seedCandidatesOfVendorCompany(): void
    {
        $matchingApiPayload = json_decode(file_get_contents(database_path('seeders/data/candidates/joan_matching_api_payload.json')), true);
        $matchingApiResponse = json_decode(file_get_contents(database_path('seeders/data/candidates/joan_matching_api_response.json')), true);

        $candidates = [
            [
                'candidate' => [
                    'id' => 1,
                    'public_id' => 'rBb4Na',
                    'vendor_id' => 1,
                    'origin_id' => null,
                    'cv_resource_id' => null,
                    'cv_parsed_raw_data' => null,
                    'clone' => false,
                    'finished' => true,
                    'internal_name' => 'Joan of Ark',
                    'name' => 'Jane',
                    'profession' => 'Software Engineer',
                    'seniority' => Seniority::Senior,
                    'rate' => 40,
                    'last_job_title' => 'Senior Android Developer',
                    'years_of_experience' => 10,
                    'highest_education' => Education::Master,
                    'field_of_study' => 'Computer Science',
                    'country' => Country::Poland,
                    'city' => 'Kraków',
                ],

                'skills' => [
                    [
                        'id' => 1,
                        'technology_id' => 100, // Java
                        'years_of_experience' => 10,
                    ],
                    [
                        'id' => 2,
                        'technology_id' => 10, // Android
                        'years_of_experience' => 8,
                    ],
                ],

                'experiences' => [
                    [
                        'experience' => [
                            'id' => 1,
                            'public_id' => 'ra0mqa',
                            'order' => 1,
                            'name' => 'Senior Android Developer at Large Automotive Company',
                            'description' => 'Started as an intern on this position and slowly build up my way to the top. Key team player.',
                            'length_type' => 'years',
                            'length' => 5,
                        ],
                        'skills' => [1, 2],
                    ],
                ],
            ],
            [
                'candidate' => [
                    'id' => 3,
                    'public_id' => 'lTs3Gb',
                    'vendor_id' => 1,
                    'origin_id' => 1,
                    'cv_resource_id' => null,
                    'cv_parsed_raw_data' => null,
                    'clone' => true,
                    'finished' => true,
                    'internal_name' => 'Joan of Ark',
                    'name' => 'Jane',
                    'profession' => 'Software Engineer',
                    'seniority' => Seniority::Senior,
                    'rate' => 40,
                    'last_job_title' => 'Senior Android Developer',
                    'years_of_experience' => 10,
                    'highest_education' => Education::Master,
                    'field_of_study' => 'Computer Science',
                    'country' => Country::Poland,
                    'city' => 'Kraków',
                ],

                'skills' => [
                    [
                        'id' => 5,
                        'technology_id' => 100, // Java
                        'years_of_experience' => 10,
                    ],
                    [
                        'id' => 6,
                        'technology_id' => 10, // Android
                        'years_of_experience' => 8,
                    ],
                ],

                'experiences' => [
                    [
                        'experience' => [
                            'id' => 2,
                            'public_id' => 's9fg84',
                            'order' => 1,
                            'name' => 'Senior Android Developer at Large Automotive Company',
                            'description' => 'Started as an intern on this position and slowly build up my way to the top. Key team player.',
                            'length_type' => 'years',
                            'length' => 5,
                        ],
                        'skills' => [5, 6],
                    ],
                ],
                'assignments' => [
                    'company_id' => null,
                    'tender_id' => null,
                    'tender_position_id' => 1,
                    'manager_id' => null,
                    'start_date' => null,
                    'end_date' => null,
                    'status' => 'approved',
                    'rejection_reason' => null,
                    'client_note' => null,
                    'matching_api_payload' => $matchingApiPayload,
                    'matching_api_response' => $matchingApiResponse,
                    'matching_api_response_status' => 200
                ],
            ],
        ];

        $this->seedCandidates($candidates);
    }

    public function seedCandidatesOfSecondVendorCompany(): void
    {
        $candidates = [
            [
                'candidate' => [
                    'id' => 2,
                    'public_id' => '65sGf6',
                    'vendor_id' => 4,
                    'origin_id' => null,
                    'cv_resource_id' => null,
                    'cv_parsed_raw_data' => null,
                    'clone' => false,
                    'finished' => true,
                    'internal_name' => 'Count of St. Germain',
                    'name' => 'Francis',
                    'profession' => 'Software Architect',
                    'seniority' => Seniority::Senior,
                    'rate' => 80,
                    'last_job_title' => 'Senior Java Architect',
                    'years_of_experience' => 15,
                    'highest_education' => Education::Doctor,
                    'field_of_study' => 'Computer Science',
                    'country' => Country::France,
                    'city' => 'Paris',
                ],

                'skills' => [
                    [
                        'id' => 3,
                        'technology_id' => 100, // Java
                        'years_of_experience' => 15,
                    ],
                    [
                        'id' => 4,
                        'technology_id' => 10, // Android
                        'years_of_experience' => 12,
                    ],
                ],
            ],
        ];

        $this->seedCandidates($candidates);
    }

    public function seedTestCandidates()
    {
        $candidates = include database_path('seeders/data/candidates/test_candidates.php');

        $this->seedCandidates($candidates);
    }

    private function seedCandidates(array $candidates): void
    {
        collect($candidates)->each(function (array $data) {
            /** @var Candidate $candidate */
            $candidate = Candidate::forceCreate($data['candidate']);

            $candidate->skills()->createMany($data['skills'] ?? []);
            $candidate->assignment()->create($data['assignments'] ?? []);

            collect($data['experiences'] ?? [])->each(function (array $experienceData) use ($candidate) {
                /** @var CandidateExperience $experience */
                $experience = $candidate->experiences()->forceCreate($experienceData['experience']);
                $experience->skills()->attach($experienceData['skills']);
            });
        });
    }
}
