<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Certification;

class CertificationsSeeder extends Seeder
{
    public function run(): void
    {
        $certifications = [
            ['id_string' => 'iso_9001', 'name' => 'ISO 9001'],
            ['id_string' => 'iso_27001', 'name' => 'ISO 27001'],
            ['id_string' => 'iso_20000', 'name' => 'ISO 20000'],
            ['id_string' => 'iso_27017', 'name' => 'ISO 27017'],
            ['id_string' => 'iso_27018', 'name' => 'ISO 27018'],
            ['id_string' => 'iso_27701', 'name' => 'ISO 27701'],
            ['id_string' => 'iso_27799', 'name' => 'ISO 27799'],
            ['id_string' => 'iso_31000', 'name' => 'ISO 31000'],
            ['id_string' => 'iso_45001', 'name' => 'ISO 45001'],
            ['id_string' => 'soc_1', 'name' => 'SOC 1'],
            ['id_string' => 'soc_2', 'name' => 'SOC 2 (Type I & II)'],
            ['id_string' => 'gdpr_compliance', 'name' => 'GDPR Compliance'],
            ['id_string' => 'pci_dss', 'name' => 'PCI-DSS'],
            ['id_string' => 'nist', 'name' => 'NIST'],
            ['id_string' => 'fedramp', 'name' => 'FedRAMP'],
            ['id_string' => 'swift_csp', 'name' => 'SWIFT CSP'],
            ['id_string' => 'hipaa_compliance', 'name' => 'HIPAA Compliance'],
            ['id_string' => 'fda_21_cfr_part_11', 'name' => 'FDA 21 CFR Part 11'],
            ['id_string' => 'cissp', 'name' => 'CISSP'],
            ['id_string' => 'cism', 'name' => 'CISM'],
            ['id_string' => 'ceh', 'name' => 'CEH'],
            ['id_string' => 'tl_9000', 'name' => 'TL 9000'],
            ['id_string' => 'basel_iii_compliance', 'name' => 'Basel III Compliance'],
            ['id_string' => 'psd2_compliance', 'name' => 'PSD2 Compliance'],
        ];

        foreach ($certifications as $certification) {
            Certification::firstOrCreate(
                ['id_string' => $certification['id_string']],
                ['name' => $certification['name']]
            );
        }
    }
}
