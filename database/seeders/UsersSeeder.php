<?php

namespace Database\Seeders;

use App\Enums\UserRole;
use App\Models\Company;
use App\Models\User;
use Illuminate\Database\Seeder;

class UsersSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (User::role(UserRole::SuperAdmin->value)->exists()) {
            $this->command->warn('Users seeder already run, skipping...');
            return;
        }

        $this->seedMasterAdmin();
        $this->seedExampleVendorUser();
        $this->seedUserOfUnapprovedCompany();
        $this->seedUserOfClientCompany();
        $this->seedUserOfSecondVendorCompany();
    }

    private function seedMasterAdmin(): void
    {
        $user = User::forceCreate([
            'id' => 1,
            'name' => 'Master',
            'surname' => 'Admin',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => 'Password1!',
        ]);
        $user->assignRole(UserRole::SuperAdmin);
    }

    private function seedExampleVendorUser(): void
    {
        if (Company::count() <= 0) {
            $this->command->error('Cannot seed client user as there are no companies, skipping...');

            return;
        }

        $user = User::forceCreate([
            'id' => 2,
            'company_id' => Company::first()->id,
            'name' => 'John',
            'surname' => 'Doe',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => 'Password1!',
        ]);

        $user->assignRole(UserRole::Vendor);
    }

    private function seedUserOfUnapprovedCompany(): void
    {
        /** @var Company $company */
        $company = Company::awaitingApproval()->first();
        if (!$company) {
            $this->command->error('Cannot seed client user as there are no unapproved companies, skipping...');
            return;
        }

        if ($company->users()->count() > 0) {
            $this->command->warn('Unapproved company was already seeded with user, skipping...');
            return;
        }

        $user = User::forceCreate([
            'id' => 3,
            'company_id' => $company->id,
            'name' => 'Jane',
            'surname' => 'Doe',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => 'Password1!',
        ]);

        $user->assignRole(UserRole::Vendor);
    }

    private function seedUserOfClientCompany(): void
    {
        /** @var Company $company */
        $company = Company::where('is_vendor', false)->first();
        if (!$company) {
            $this->command->error('Cannot seed client user as there are no client companies, skipping...');
            return;
        }

        if ($company->users()->count() > 0) {
            $this->command->warn('Client company was already seeded with user, skipping...');

            return;
        }

        $user = User::forceCreate([
            'id' => 4,
            'company_id' => $company->id,
            'name' => 'Ignác',
            'surname' => 'Bajza',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => 'Password1!',
        ]);

        $user->assignRole(UserRole::Client);
    }

    private function seedUserOfSecondVendorCompany(): void
    {
        /** @var Company $company */
        $company = Company::find(4);
        if (!$company) {
            $this->command->error('Cannot seed client user as there is no second vendor companies, skipping...');
            return;
        }

        if ($company->users()->count() > 0) {
            $this->command->warn('Second client company was already seeded with user, skipping...');
            return;
        }

        $user = User::forceCreate([
            'id' => 5,
            'company_id' => $company->id,
            'name' => 'Ivan',
            'surname' => 'Krasko',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => 'Password1!',
        ]);

        $user->assignRole(UserRole::Vendor);
    }
}
