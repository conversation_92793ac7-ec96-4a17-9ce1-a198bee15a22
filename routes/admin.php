<?php

use App\Enums\UserRole;
use App\Http\Controllers\Admin\BenchSpecialistCandidatesController;
use App\Http\Controllers\Admin\BenchSpecialistsController;
use App\Http\Controllers\Admin\BenchSpecialistUnapprovedChangesController;
use App\Http\Controllers\Admin\CandidatesController;
use App\Http\Controllers\Admin\CandidatesOptionListController;
use App\Http\Controllers\Admin\ClientsController;
use App\Http\Controllers\Admin\CompaniesController;
use App\Http\Controllers\Admin\CompaniesOptionListController;
use App\Http\Controllers\Admin\CompanyNotesController;
use App\Http\Controllers\Admin\CompanyUnapprovedChangesController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\EmployeePositionsController;
use App\Http\Controllers\Admin\IndustriesController;
use App\Http\Controllers\Admin\ReviewsController;
use App\Http\Controllers\Admin\SolutionsController;
use App\Http\Controllers\Admin\SolutionUnapprovedChangesController;
use App\Http\Controllers\Admin\StaffUsersController;
use App\Http\Controllers\Admin\TechnologiesController;
use App\Http\Controllers\Admin\TenderCandidatesController;
use App\Http\Controllers\Admin\TenderPositionsController;
use App\Http\Controllers\Admin\TendersController;
use App\Http\Controllers\Admin\TenderVendorsController;
use App\Http\Controllers\Admin\VendorsController;
use App\Http\Controllers\Admin\VendorsOptionListController;
use App\Http\Controllers\Admin\VendorUnapprovedChangesController;
use App\Http\Controllers\Admin\UsersController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('dashboard', DashboardController::class)->name('dashboard');
    Route::get('reviews', ReviewsController::class)->name('reviews');

    Route::prefix('companies')->group(function () {
        Route::get('option-list', CompaniesOptionListController::class)->name('companies.option-list');
        Route::get('vendors/option-list', VendorsOptionListController::class)->name('vendors.option-list');

        Route::put('{company}/is-vendor', [CompaniesController::class, 'updateIsVendor'])->name('companies.is-vendor');
        Route::put('{company}/notes', [CompaniesController::class, 'updateNotes'])->name('companies.notes');
        Route::get('{company}/vendor', [VendorsController::class, 'show'])->name('companies.vendor.show');
        Route::put('{company}/vendor', [VendorsController::class, 'update'])->name('companies.vendor.update');

        Route::post('{company}/unapproved-change', [CompanyUnapprovedChangesController::class, 'approve'])
            ->name('companies.unapproved-change.approve');
        Route::delete('{company}/unapproved-change', [CompanyUnapprovedChangesController::class, 'destroy'])
            ->name('companies.unapproved-change.destroy');

        Route::post('{company}/vendor/unapproved-change', [VendorUnapprovedChangesController::class, 'approve'])
            ->name('companies.vendor.unapproved-change.approve');
        Route::delete('{company}/vendor/unapproved-change', [VendorUnapprovedChangesController::class, 'destroy'])
            ->name('companies.vendor.unapproved-change.destroy');
    });
    Route::apiResource('companies.notes', CompanyNotesController::class)->except('show');
    Route::apiResource('companies', CompaniesController::class);

    Route::prefix('tenders/{tender}')->group(function () {
        Route::get('vendors', [TenderVendorsController::class, 'index'])->name('tenders.vendors.index');
        Route::put('vendors/allowed', [TenderVendorsController::class, 'bulkUpdateAllowed'])->name('tenders.vendors.allowed.bulk');
        Route::put('vendors/{vendor}/allowed', [TenderVendorsController::class, 'updateAllowed'])->name('tenders.vendors.allowed');
        Route::post('vendors/notify', [TenderVendorsController::class, 'notify'])->name('tenders.vendors.notify');

        Route::put('positions/{position}/apply', [TenderPositionsController::class, 'apply'])->name('tenders.positions.apply');

        Route::put('candidates/{candidate}/status', [TenderCandidatesController::class, 'updateStatus'])->name('tenders.candidates.status');
        Route::post('candidates/notify', [TenderCandidatesController::class, 'notify'])->name('tenders.candidates.notify');
    });
    Route::apiResource('tenders.positions', TenderPositionsController::class)->except('index');
    Route::apiResource('tenders.candidates', TenderCandidatesController::class)->except('store');
    Route::apiResource('tenders', TendersController::class);

    Route::prefix('solutions')->name('solutions.')->group(function () {
        Route::post('{solution}/unapproved-change', [SolutionUnapprovedChangesController::class, 'approve'])
            ->name('unapproved-change.approve');
        Route::delete('{solution}/unapproved-change', [SolutionUnapprovedChangesController::class, 'destroy'])
            ->name('unapproved-change.destroy');
    });
    Route::apiResource('solutions', SolutionsController::class);

    Route::prefix('candidates')->group(function () {
        Route::post('cv', [CandidatesController::class, 'parse'])->name('candidates.parse');
        Route::get('option-list', CandidatesOptionListController::class)->name('candidates.option-list');
    });
    Route::apiResource('candidates', CandidatesController::class);

    Route::prefix('bench')->name('bench.')->group(function () {
        Route::prefix('specialists')->name('specialists.')->group(function () {
            Route::post('{specialist}/unapproved-change', [BenchSpecialistUnapprovedChangesController::class, 'approve'])
                ->name('unapproved-change.approve');
            Route::delete('{specialist}/unapproved-change', [BenchSpecialistUnapprovedChangesController::class, 'destroy'])
                ->name('unapproved-change.destroy');
        });
        Route::apiResource('specialists', BenchSpecialistsController::class);
        Route::apiResource('specialists.candidates', BenchSpecialistCandidatesController::class)->only('show', 'update');
    });

    Route::get('users/roles', [UsersController::class, 'roles'])->name('users.roles.index');

    Route::apiResource('clients', ClientsController::class);
    Route::apiResource('industries', IndustriesController::class);
    Route::apiResource('users/staff', StaffUsersController::class)->parameters(['staff' => 'user']);
    Route::apiResource('users', UsersController::class);
    Route::apiResource('technologies', TechnologiesController::class);
    Route::apiResource('employee-positions', EmployeePositionsController::class);
});
