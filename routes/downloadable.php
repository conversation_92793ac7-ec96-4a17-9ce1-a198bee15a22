<?php

use App\Http\Controllers\Admin\CandidateExportController;
use App\Http\Controllers\Admin\CompaniesExportController;
use App\Http\Controllers\Admin\TenderCandidatesController;
use App\Http\Controllers\Admin\TenderVendorsExportController;
use App\Http\Controllers\Admin\VendorExportController;
use App\Enums\UserRole;
use Illuminate\Support\Facades\Route;


Route::prefix('admin')
    ->name('admin.')
    ->middleware(UserRole::SuperAdmin->middleware())
    ->group(
        static function (): void {
            Route::get('companies/export', CompaniesExportController::class)->name('companies.export');
            Route::get('companies/{company}/vendor/export', VendorExportController::class)->name('companies.vendor.export');

            Route::get('candidates/{candidate}/export', CandidateExportController::class)->name('candidates.export');

            Route::get('tenders/{tender}/vendors/export', TenderVendorsExportController::class)->name('tenders.vendors.export');
            Route::get('tenders/{tender}/candidates/export', [TenderCandidatesController::class, 'export'])->name('tenders.candidates.export');
            Route::get('tenders/{tender}/candidates/cvs/export', [TenderCandidatesController::class, 'exportCvs'])->name('tenders.candidates.cvs.export');
        }
    );
