<?php

use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\LogoutController;
use App\Http\Controllers\Auth\RegistrationController;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Middleware\CaptureAuthParams;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Route;
use Laravel\Passport\Http\Controllers\AccessTokenController;
use Laravel\Passport\Http\Controllers\ApproveAuthorizationController;
use Lara<PERSON>\Passport\Http\Controllers\AuthorizationController;
use Laravel\Passport\Http\Controllers\DenyAuthorizationController;
use Lara<PERSON>\Passport\Http\Controllers\TransientTokenController;

Route::post('/auth/register', RegistrationController::class)->name('register');

Route::domain('auth.'.config('app.domain'))
    ->group(static function (): void {

        Route::any('/', static function (): RedirectResponse {
            return redirect()->to(
                config('app.url')
            );
        });

        Route::middleware('web')
            ->group(static function (): void {

                // Authentication Routes
                Route::get('login', LoginController::class)
                    ->middleware('prevent-page-cache')
                    ->name('login');
                Route::post('login', [LoginController::class, 'login'])->name('login.post');
                Route::post('logout', LogoutController::class)->middleware(['auth:api'])->name('logout');

                // OAuth Routes (Laravel Passport)
                Route::get('/authorize', [AuthorizationController::class, 'authorize'])
                    ->middleware(CaptureAuthParams::class)
                    ->name('passport.authorizations.authorize');
                Route::post('/approve', [ApproveAuthorizationController::class, 'approve'])->name('passport.authorizations.approve');
                Route::delete('/deny', [DenyAuthorizationController::class, 'deny'])->name('passport.authorizations.deny');
                Route::post('/token', [AccessTokenController::class, 'issueToken'])->name('passport.token');
                Route::post('/token/refresh', [TransientTokenController::class, 'refresh'])->name('passport.token.refresh');

                Route::prefix('forgot-password')
                    ->as('forgot-password.')
                    ->controller(ForgotPasswordController::class)
                    ->group(
                        static function (): void {
                            Route::get('/', 'show')->name('show');
                            Route::post('/', 'store')->name('store');
                        }
                    );

                Route::prefix('reset-password')
                    ->as('reset-password.')
                    ->controller(ResetPasswordController::class)
                    ->group(
                        static function (): void {
                            Route::get('/', 'show')->name('show');
                            Route::post('/', 'store')->name('store');
                        }
                    );
            });
    });
