<?php

use App\Http\Controllers\BenchSpecialistsController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Bench Routes
|--------------------------------------------------------------------------
|
| The following routes are all routes that provide specialists, teams, or
| data or services closely bound to the bench.
|
*/

Route::prefix('bench')
    ->middleware('vendor')
    ->name('bench')
    ->group(
        static function (): void {
            Route::apiResource('specialists', BenchSpecialistsController::class);
        }
    );
