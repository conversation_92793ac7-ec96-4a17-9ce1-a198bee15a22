<?php

declare(strict_types=1);

use App\Enums\UserRole;
use App\Http\Controllers\Enterprise\SalesSupportController;
use App\Http\Resources\WelcomeResource;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/', static function (Request $request): WelcomeResource|RedirectResponse {
    if (!$request->wantsJson()) {
        return redirect()->away('https://nordics.io');
    }

    return new WelcomeResource(null);
})->name('welcome');

Route::get('sales-support/last-login', [SalesSupportController::class, 'lastLogin'])->name('sales-support.last-login')->middleware('auth.basic', UserRole::SuperAdmin->middleware());
