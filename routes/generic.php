<?php

use App\Http\Controllers\ClientsController;
use App\Http\Controllers\EmployeePositionsController;
use App\Http\Controllers\HealthCheckController;
use App\Http\Controllers\IndustriesController;
use App\Http\Controllers\ResourcesController;
use App\Http\Controllers\TechnologiesController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::get('health', HealthCheckController::class)->name('health');

Route::apiResource('clients', ClientsController::class)->only('index');
Route::apiResource('industries', IndustriesController::class)->only('index');
Route::apiResource('technologies', TechnologiesController::class)->only('index');
Route::apiResource('employee-positions', EmployeePositionsController::class)->only('index');

Route::middleware('auth:api')->group(function () {
    Route::apiResource('resources', ResourcesController::class)->only('store');
});
