<?php

use App\Http\Controllers\Enterprise\Assistant\RfpController;
use App\Http\Controllers\Enterprise\CandidatesController;
use App\Http\Controllers\Enterprise\CompaniesController;
use App\Http\Controllers\Enterprise\CompaniesSearchController;
use App\Http\Controllers\Enterprise\CompanyUsersController;
use App\Http\Controllers\Enterprise\ResetController;
use App\Http\Controllers\Enterprise\TechnologyController;
use App\Http\Controllers\Enterprise\TenderCandidatesController;
use App\Http\Controllers\Enterprise\TenderController;
use App\Http\Controllers\Enterprise\TenderInvitationsController;
use App\Http\Controllers\Enterprise\TenderMatchingController;
use App\Http\Controllers\Enterprise\WorkspaceCompaniesController;
use App\Http\Controllers\Enterprise\WorkspaceController;
use Illuminate\Foundation\Http\Middleware\HandlePrecognitiveRequests;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Support\Facades\Route;

Route::prefix('enterprise')
    ->name('enterprise.')
    ->middleware(SubstituteBindings::class)
    ->group(function () {

        Route::prefix('assistant')
            ->name('assistant.')
            ->group(static function (): void {

                Route::prefix('rfp')
                    ->name('rfp.')
                    ->controller(RfpController::class)
                    ->group(static function (): void {

                        Route::get('/', 'index')->name('index');
                        Route::post('/', 'store')->name('store');

                        Route::prefix('{rfp}')
                            ->whereUuid('rfp')
                            ->middleware('can:access,rfp') // Alias can() does not work.
                            ->group(static function (): void {

                                Route::get('/', 'show')->name('show');
                                Route::put('/', 'update')->name('update')->middleware([HandlePrecognitiveRequests::class]);
                                Route::delete('/', 'delete')->name('delete');
                                Route::get('/info', 'info')->name('info');
                                Route::get('/resources', 'resources')->name('resources');
                                Route::get('/resources/suggested', 'suggestedResources')->name('resources-suggested');
                                Route::patch('/step', 'setActiveStep')->name('setActiveStep');
                                Route::post('/tender', 'createTender')->name('createTender');

                            });

                    });

            });

        Route::prefix('companies')
            ->as('companies.')
            ->controller(CompaniesController::class)
            ->group(static function (): void {

                Route::prefix('search')
                    ->controller(CompaniesSearchController::class)
                    ->group(static function (): void {

                        Route::get('/', 'search')->name('search')->middleware([HandlePrecognitiveRequests::class]);
                        Route::get('recommended', 'recommended')->name('recommended');

                    });

                // This route has to be defined after subroutes otherwise path may be colliding with public_id.
                Route::get('{company:public_id}', 'show')->name('show');

            });

        Route::prefix('workspace')
            ->name('workspace.')
            ->controller(WorkspaceController::class)
            ->group(static function (): void {

                Route::get('/', 'show')->name('show');
                Route::post('/', 'store')->name('store');

                Route::prefix('companies')
                    ->name('companies.')
                    ->controller(WorkspaceCompaniesController::class)
                    ->group(static function (): void {

                        Route::get('/', 'index')->name('index');
                        Route::get('/filters', 'filterOptions')->name('filters');
                        Route::post('import', 'import')->name('import');
                        Route::post('import/status', 'showImportStatus')->name('import.status');
                        Route::post('import/update', 'updateCompanyImport')->name('import.update');
                        Route::delete('{company:public_id}', 'destroy')->name('destroy');

                    });

            });

        Route::prefix('technologies')
            ->name('technologies.')
            ->controller(TechnologyController::class)
            ->group(static function (): void {

                Route::get('/', 'index')->name('index');

            });

        Route::prefix('tenders')
            ->name('tenders.')
            ->controller(TenderController::class)
            ->group(static function (): void {

                Route::get('/', 'index')->name('index');

                Route::prefix('matching/{tenderMatch}')
                    ->name('matching.')
                    ->whereUuid('tenderMatch')
                    ->controller(TenderMatchingController::class)
                    ->group(static function (): void {

                        Route::get('/', 'show')->name('show');

                    });

                Route::prefix('{tender:public_id}')
                    ->name('tender.')
                    ->group(static function (): void {

                        Route::get('/', 'show')->name('show');
                        Route::put('/', 'update')->name('update');

                        Route::prefix('matching')
                            ->name('matching.')
                            ->controller(TenderMatchingController::class)
                            ->group(static function (): void {

                                Route::get('/', 'index')->name('index');
                                // TODO(Marian Rusnak): Temporary endpoint for testing purposes.
                                Route::post('/', 'runMatching')->name('runMatching');

                            });

                        Route::prefix('invitation')
                            ->name('invitation.')
                            ->controller(TenderInvitationsController::class)
                            ->group(static function (): void {

                                Route::get('/', 'index')->name('index');
                                Route::post('send', 'send')->name('send');

                                Route::prefix('{company:public_id}')
                                    ->name('companies.')
                                    ->withoutScopedBindings()
                                    ->group(static function (): void {

                                        Route::post('/', 'addCompany')->name('add');
                                        Route::delete('/', 'removeCompany')->name('remove');

                                    });

                            });

                        Route::prefix('candidates')
                            ->name('candidates.')
                            ->controller(TenderCandidatesController::class)
                            ->group(static function (): void {

                                Route::get('/', 'index')->name('index');
                                Route::put('{candidate:public_id}/application', 'updateApplication')
                                    ->name('application.update')
                                    ->withoutScopedBindings();

                            });

                    });

            });

        Route::prefix('candidates')
            ->name('candidates.')
            ->controller(CandidatesController::class)
            ->group(static function (): void {

                // TODO(Marian Rusnak): Quick temporary endpoints, refactor later.
                Route::get('/bench', 'bench')->name('bench');
                Route::get('/bench/filters', 'benchFilterOptions')->name('bench.filterOptions');

                Route::get('/assigned', 'assigned')->name('assigned');
                Route::get('/assigned/filters', 'assignedFilterOptions')->name('assigned.filterOptions');

                Route::prefix('{candidate:public_id}')
                    ->name('candidate.')
                    ->group(static function (): void {

                        Route::get('/', 'show')->name('show');
                        Route::put('/assignment', 'updateAssignment')->name('updateAssignment');

                    });

            });

        Route::prefix('company')
            ->name('company.')
            ->group(static function (): void {

                Route::prefix('users')
                    ->name('users.')
                    ->controller(CompanyUsersController::class)
                    ->group(static function (): void {

                        Route::get('/', 'index')->name('index');

                    });

            });

        Route::delete('reset', ResetController::class)->name('destroy');

    });
