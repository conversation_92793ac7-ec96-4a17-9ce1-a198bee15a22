<?php

use App\Http\Controllers\Company\CandidateAssignmentsController;
use App\Http\Controllers\Company\CandidatesController;
use App\Http\Controllers\Company\ProfileController;
use App\Http\Controllers\Company\SolutionsController;
use App\Http\Controllers\Company\VendorProfileController;
use App\Http\Controllers\CompanyClientsController;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::prefix('company')->name('company.')->group(function () {
    Route::get('/', [ProfileController::class, 'show'])->name('show');
    Route::put('/', [ProfileController::class, 'update'])->name('update');
    Route::delete('/unapproved-change', [ProfileController::class, 'discardUnapprovedChange'])->name('unapproved-change.discard');

    Route::prefix('vendor')->name('vendor.')->group(function () {
        Route::get('/', [VendorProfileController::class, 'show'])->name('show');
        Route::put('/', [VendorProfileController::class, 'update'])->name('update');
        Route::put('/is-vendor', [VendorProfileController::class, 'updateIsVendor'])->name('is-vendor');
        Route::delete('/unapproved-change', [VendorProfileController::class, 'discardUnapprovedChange'])->name('unapproved-change.discard');
    });

    Route::middleware('vendor')->group(function () {
        Route::apiResource('solutions', SolutionsController::class)->only('index');

        Route::post('candidates/cv', [CandidatesController::class, 'parse'])->name('candidates.parse');
        Route::post('candidates/{id}/bench', [CandidatesController::class, 'enlistToBench'])->name('candidates.bench.enlist');

        Route::prefix('candidates/{candidate:public_id}/assignment')
            ->name('candidates.assignments.')
            ->middleware(SubstituteBindings::class)
            ->controller(CandidateAssignmentsController::class)
            ->group(static function (): void {

                Route::post('/', 'store')->name('store');
                Route::delete('/', 'destroy')->name('destroy');

            });

        Route::apiResource('candidates', CandidatesController::class)->except('store');

        Route::get('clients', [CompanyClientsController::class, 'index'])->name('clients.index');
    });
});
