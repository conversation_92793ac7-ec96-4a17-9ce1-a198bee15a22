<?php

use App\Http\Controllers\TenderCandidatesController;
use App\Http\Controllers\TenderPositionsController;
use App\Http\Controllers\TendersController;
use App\Http\Controllers\TendersDashboardController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Vendor Routes
|--------------------------------------------------------------------------
|
| The following routes are all routes that provide either vendors, or
| data or services closely bound to vendors.
|
*/

Route::get('tenders/dashboard', TendersDashboardController::class);
Route::apiResource('tenders', TendersController::class)->only('index', 'show');

Route::put('tenders/{tender}/positions/{position}/apply', [TenderPositionsController::class, 'apply'])
    ->middleware('vendor')
    ->name('tenders.positions.apply');
Route::apiResource('tenders.positions', TenderPositionsController::class)->only('show');

Route::put('tenders/{tender}/candidates/{candidate}/status', [TenderCandidatesController::class, 'updateStatus'])
    ->name('tenders.candidates.status');
Route::put('tenders/{tender}/candidates/{candidate}/note', [TenderCandidatesController::class, 'updateNote'])
    ->name('tenders.candidates.note');
Route::apiResource('tenders.candidates', TenderCandidatesController::class)->only('index');
