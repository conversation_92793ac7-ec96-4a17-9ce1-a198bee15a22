<?php

use App\Http\Controllers\SolutionsController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Vendor Routes
|--------------------------------------------------------------------------
|
| The following routes are all routes that provide either vendors, or
| data or services closely bound to vendors.
|
*/

Route::apiResource('solutions', SolutionsController::class)->only('index', 'show');
Route::apiResource('solutions', SolutionsController::class)->middleware('vendor')->except('index', 'show');

Route::delete('solutions/{slug}/unapproved-change', [SolutionsController::class, 'discardUnapprovedChange'])
    ->middleware('vendor')
    ->name('solutions.unapproved-change.destroy');

Route::post('solutions/{slug}/contact', [SolutionsController::class, 'requestContact'])->name('solutions.request-contact');
