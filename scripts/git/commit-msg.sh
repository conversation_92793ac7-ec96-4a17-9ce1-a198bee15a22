#!/bin/sh

COMMIT_MSG_FILE=${1:-.git/COMMIT_EDITMSG}

if [ ! -f "$COMMIT_MSG_FILE" ]; then
  echo "Error: Commit message file not found: $COMMIT_MSG_FILE"
  exit 1
fi

JIRA_ID=$(git branch | grep '*' | sed 's/* //' | sed -E 's/^([A-Z]+-[0-9]+).*/\1/')
echo "Detected JIRA ID: $JIRA_ID"

if ! echo "$JIRA_ID" | grep -qE '^[A-Z]+-[0-9]+$'; then
  echo "Invalid JIRA ID format. Skipping prefix addition."
  exit 0
fi

PREFIX="#$JIRA_ID: "
if [ -f "$COMMIT_MSG_FILE" ] && ! grep -q "$PREFIX" "$COMMIT_MSG_FILE"; then
  echo "Adding prefix to commit message..."
  echo "$PREFIX$(cat "$COMMIT_MSG_FILE")" > "$COMMIT_MSG_FILE"
fi
