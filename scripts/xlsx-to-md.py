import io
import sys

import pandas as pd

sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python extract_xlsx.py <xlsx_path>", file=sys.stderr)
        sys.exit(1)

    try:
        xlsx_path = sys.argv[1]
        sheets = pd.read_excel(xlsx_path, sheet_name=None, engine="openpyxl")

        extracted_content = []
        for sheet_name, df in sheets.items():
            # Write the sheet name as a header
            extracted_content.append(f"Sheet: {sheet_name}\n")

            df_string = df.to_csv(sep=" ", index=False, header=False)
            extracted_content.append(df_string)
            extracted_content.append("\n")

        # Join all parts to form the final string
        final_output = "".join(extracted_content)
        print(final_output)

    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)
