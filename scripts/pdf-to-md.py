import io
import sys

from pymupdf4llm import to_markdown

sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python extract.py <pdf_path>", file=sys.stderr)
        sys.exit(1)

    try:
        pdf_path = sys.argv[1]
        extracted_text = to_markdown(pdf_path)
        print(extracted_text)

    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        sys.exit(1)

