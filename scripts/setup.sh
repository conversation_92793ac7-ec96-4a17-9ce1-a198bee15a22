#!/bin/bash
set -ex -o pipefail

dockerComposeProjectName=$1

# Workarounds for WSL.
if grep -qi microsoft /proc/sys/kernel/osrelease; then
  echo 'Running workarounds in WSL'
  chmod -R 777 .
  chmod 644 docker/php/mysql-client/my.cnf
fi

# Prepare .env files.
if [ ! -e .env ]; then
    cp .env.example .env
fi
if [ ! -e .env.docker-compose ]; then
    cp .env.docker-compose.example .env.docker-compose
fi
if [ -n "$dockerComposeProjectName" ]; then
  echo "# setup.sh overrides" >> .env
  echo "COMPOSE_PROJECT_NAME=$dockerComposeProjectName" >> .env
fi

# Install npm dependencies.
npm install
npx tailwindcss -i ./resources/css/app.css -o ./public/css/app.css

# Start containers.
docker compose up --build --detach --wait

# Install composer dependencies.
docker compose exec api sh -c "composer install"

# Prepare database.
docker compose exec api sh -c "php artisan migrate --seed"

# Generate Oauth keys.
docker compose exec api sh -c "php artisan passport:keys"

# Setup Oauth for metch-client UI.
docker compose exec api sh -c "
php artisan passport:client --public --no-interaction --name='metch-client' --redirect_uri='http://localhost:5173/authorize'
" | grep 'Client ID' | awk '{ print $4 }' > .client-id.metch-client || { echo "Failed to generate client ID"; exit 1; }

docker compose exec api sh -c "
php artisan tinker --execute=\"\App\Models\Passport\Platform::create([
  'oauth_client_id' => '\$(cat .client-id.metch-client)',
  'platform' => \App\Enums\Platform::Client,
  'url' => 'http://localhost:5173'
]);\"
"

# Setup Oauth for vendor UI.
docker compose exec api sh -c "
php artisan passport:client --public --no-interaction --name='vendor' --redirect_uri='http://localhost:5175/authorize'
" | grep 'Client ID' | awk '{ print $4 }' > .client-id.vendor || { echo "Failed to generate client ID"; exit 1; }

docker compose exec api sh -c "
php artisan tinker --execute=\"\App\Models\Passport\Platform::create([
  'oauth_client_id' => '\$(cat .client-id.vendor)',
  'platform' => \App\Enums\Platform::Vendor,
  'url' => 'http://localhost:5175'
]);\"
"

