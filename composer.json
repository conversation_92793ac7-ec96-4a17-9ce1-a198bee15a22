{"name": "nio/api", "type": "project", "description": "HTTP API for Nordics platform.", "keywords": ["api", "enterprise", "vendor management", "vendor intelligence", "tender"], "license": "proprietary", "require": {"php": "^8.4", "anhskohbo/no-captcha": "^3.7", "barryvdh/laravel-dompdf": "^3.1.1", "guzzlehttp/guzzle": "^7.10.0", "intervention/image": "^3.11.4", "jenssegers/agent": "^2.6.4", "laravel/framework": "^12.27.0", "laravel/helpers": "^1.8.1", "laravel/passport": "^12.4.2", "laravel/tinker": "^2.10.1", "lcobucci/clock": "^3.3.1", "lcobucci/jwt": "^5.5", "maatwebsite/excel": "^3.1.67", "propaganistas/laravel-phone": "^5.3.6", "sentry/sentry-laravel": "^4.15.1", "spatie/laravel-http-logger": "^1.11.1", "spatie/laravel-permission": "^6.21.0", "spatie/laravel-query-builder": "^6.3.5", "stechstudio/laravel-zipstream": "^5.7", "symfony/html-sanitizer": "^7.3.3", "symfony/mailer": "^7.3.3", "vladimir-yuldashev/laravel-queue-rabbitmq": "^14.3.0", "wildside/userstamps": "^3.1.0"}, "require-dev": {"fakerphp/faker": "^1.24.1", "brainmaestro/composer-git-hooks": "^3.0", "knuckleswtf/scribe": "^5.2.1", "laravel/pint": "^1.24.0", "mockery/mockery": "^1.6.12", "nunomaduro/collision": "^8.8.2", "phpunit/phpunit": "^12.3.7", "spatie/laravel-ignition": "^2.9.1"}, "autoload": {"psr-4": {"App\\": "app/", "Libs\\": "libs/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-install-cmd": ["vendor/bin/cghooks add --ignore-lock"], "post-update-cmd": ["vendor/bin/cghooks update"]}, "extra": {"laravel": {"dont-discover": []}, "hooks": {"config": {"stop-on-failure": ["pre-commit", "pre-push"]}, "pre-commit": [], "post-merge": "composer install", "commit-msg": ["bash scripts/git/commit-msg.sh"]}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "platform": {"php": "8.4"}, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true, "phpstan/extension-installer": false}}, "prefer-stable": true}