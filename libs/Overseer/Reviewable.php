<?php

namespace Libs\Overseer;

use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * FIXME: refactor a bit, especially functions handling relationships. Split into multiple files.
 *
 * @property int $merge_into_id
 * @property bool $is_draft
 * @property bool $is_awaiting_approval
 * @property bool $is_published
 */
trait Reviewable
{
    public static function bootReviewable()
    {
        static::addGlobalScope(new ReviewableScope);
    }

    public function merge_into(): BelongsTo
    {
        return $this->belongsTo(static::class, 'merge_into_id');
    }

    public function unapproved_change(): HasOne
    {
        return $this->hasOne(static::class, 'merge_into_id')->unapprovedChanges();
    }

    public function scopePublished(Builder $query): Builder
    {
        return $query->where("{$this->getTable()}.publish_status", PublishStatus::Published);
    }

    public function scopeDrafts(Builder $query): Builder
    {
        return $query->where("{$this->getTable()}.publish_status", PublishStatus::Draft);
    }

    public function scopeAwaitingApproval(Builder $query): Builder
    {
        return $query->where("{$this->getTable()}.publish_status", PublishStatus::AwaitingApproval);
    }

    public function scopeWithUnapprovedChanges(Builder $query): Builder
    {
        return $query->withoutGlobalScope(ReviewableScope::class);
    }

    public function scopeUnapprovedChanges(Builder $query): Builder
    {
        return $query->withUnapprovedChanges(ReviewableScope::class)->whereNotNull('merge_into_id');
    }

    public function isChanged(self $original, ?array $attributes = null, ?array $relationships = null): bool
    {
        $attributes = $attributes ?? $this->getFillable();
        $relationships = $relationships ?? [];

        return $this->areAttributesChanged($original, $attributes)
            || $this->areRelationshipsChanged($original, $relationships);
    }

    public function isNotChanged(self $original, ?array $attributes = null, ?array $relationships = null): bool
    {
        return ! $this->isChanged($original, $attributes, $relationships);
    }

    public function requestReview(array $data, array $relationships = []): ReviewStatus
    {
        $hasPendingChanges = (bool) $this->unapproved_change;

        // First, we need to fill in a clone model holding the changed data.
        $changes = $this->unapproved_change ?? $this->replicateForReview($relationships);
        $changes->fill($data);
        $changes->fillRelationships($relationships);
        $changes->save($data);

        // Next, we will check whether new update brings in any changes.
        if ($changes->isNotChanged($this, array_keys($data), array_keys($relationships))) {
            // If there are no changes but there is already existing pending change,
            // it means that user discarded the change, and we can therefore cancel review.
            if ($hasPendingChanges) {
                $changes->delete();

                return ReviewStatus::Discarded;
            }

            // Otherwise, there were no changes to begin with, but we still need to discard draft.
            $changes->forceDelete();

            return ReviewStatus::Unchanged;
        }

        return ReviewStatus::Accepted;
    }

    public function replicateForReview(array &$relationships = []): static
    {
        $this->unsetRelations();
        $this->load($this->reviewableRelationships ?? []);

        $clone = tap($this->replicate())->save();
        $this->replicateRelationships($clone, $relationships);
        $clone->awaitApproval();
        $clone->merge_into()->associate($this);
        $clone->save();

        return $clone;
    }

    public function awaitApproval(): static
    {
        $this->publish_status = PublishStatus::AwaitingApproval;

        return $this;
    }

    public function asDraft(): static
    {
        $this->publish_status = PublishStatus::Draft;

        return $this;
    }

    public function asPublished(): static
    {
        $this->publish_status = PublishStatus::Published;

        return $this;
    }

    public function publish(): bool
    {
        if (! $this->merge_into_id) {
            $this->publish_status = PublishStatus::Published;

            return $this->save();
        }

        // TODO: some kind of scope of properties, or protected properties
        $this->merge_into->fill($this->getAttributes())->save();
        $this->merge_into->fillRelationships($this->extractRelationships(), publishMerge: true);

        return $this->delete();
    }

    protected function isUnapprovedChange(): Attribute
    {
        return Attribute::get(fn () => $this->merge_into_id === null);
    }

    protected function isPublished(): Attribute
    {
        return Attribute::get(fn () => $this->publish_status === PublishStatus::Published);
    }

    protected function isAwaitingApproval(): Attribute
    {
        return Attribute::get(fn () => $this->publish_status === PublishStatus::AwaitingApproval);
    }

    protected function isDraft(): Attribute
    {
        return Attribute::get(fn () => $this->publish_status === PublishStatus::Draft);
    }

    private function areAttributesChanged(self $original, array $attributes): bool
    {
        $originalAttributes = collect($original->attributesToArray())->only($attributes);
        $modifiedAttributes = collect($this->attributesToArray())->only($attributes);

        return $originalAttributes->differentAssoc($modifiedAttributes);
    }

    /**
     * @throws null
     */
    private function areRelationshipsChanged(self $original, array $relationships): bool
    {
        foreach (($this->reviewableRelationships ?? []) as $name) {
            if (! in_array($name, $relationships)) {
                continue;
            }

            $originalRelationship = $original->{$name}();
            $modifiedRelationship = $this->{$name}();

            if ($originalRelationship instanceof BelongsToMany) {
                // For many-to-many relationship without pivot, we only need
                // to compare set of ids to related items
                $originalRelatedIds = $originalRelationship->allRelatedIds();
                $modifiedRelatedIds = $modifiedRelationship->allRelatedIds();

                if ($originalRelatedIds->different($modifiedRelatedIds)) {
                    return true;
                }
            } elseif ($originalRelationship instanceof HasMany) {
                // For one-to-many relationships, we need compare attributes
                // of records besides foreign keys, which can be done easy
                // by serializing them, sorting them and only then comparing
                $relationshipAttributes = $originalRelationship->getRelated()->getFillable();
                $prepareItems = fn ($relationship) => collect(
                    $relationship->select($relationshipAttributes)->get()->map(fn ($model) => $model->getAttributes())->toArray()
                )->map(fn ($item) => json_encode($item))->sort();

                $originalItems = $prepareItems($originalRelationship);
                $modifiedItems = $prepareItems($modifiedRelationship);

                if ($originalItems->different($modifiedItems)) {
                    return true;
                }
            } elseif ($originalRelationship instanceof HasOne) {
                // One-to-one relationship can be compared in a same way as
                // one-to-many, except on a single record instead of one.
                $relationshipAttributes = $originalRelationship->getRelated()->getFillable();
                $prepareItem = fn ($relationship) => json_encode($relationship->select($relationshipAttributes)->first()?->getAttributes());

                $originalItem = $prepareItem($originalRelationship);
                $modifiedItem = $prepareItem($modifiedRelationship);

                if ($originalItem !== $modifiedItem) {
                    return true;
                }
            } else {
                $relationshipType = get_class($originalRelationship);
                throw new Exception("Cannot check relationship [$name] of type [$relationshipType]!");
            }
        }

        return false;
    }

    /**
     * @throws null
     */
    private function fillRelationships(array $relationships, bool $publishMerge = false): void
    {
        foreach (($this->reviewableRelationships ?? []) as $name) {
            if (! array_key_exists($name, $relationships)) {
                continue;
            }

            $data = $relationships[$name];
            $relationship = $this->{$name}();

            if ($relationship instanceof BelongsToMany) {
                $relationship->sync($data);
            } elseif ($relationship instanceof HasMany) {
                $keyField = $relationship->getRelated()->getKeyName();
                $nameUppercase = ucfirst($name);

                $hasIds = collect($data)->some(fn ($value) => $value[$keyField] ?? false);
                $customSaver = method_exists($this, "storeNew{$nameUppercase}Entity")
                    ? fn (array $data) => $this->{"storeNew{$nameUppercase}Entity"}($this, $data, $publishMerge)
                    : null;

                if (! $hasIds && ! $customSaver) {
                    $relationship->delete();
                    $relationship->createMany($data);
                } else {
                    $data = collect($data);
                    $keepIds = $data->whereNotNull('id')->pluck('id');
                    $existingById = $relationship->get()->keyBy('id');

                    // Delete records that were removed from the list
                    $existingById->whereNotIn('id', $keepIds)->each(fn ($related) => $related->delete());

                    // Sync the rest - update existing and create new
                    $data->each(function ($data) use ($relationship, $keyField, $existingById, $customSaver) {
                        if ($data[$keyField] ?? false) {
                            $existingById
                                ->get($data[$keyField])
                                ->fill($data)
                                ->save();
                        } else {
                            if ($customSaver) {
                                $customSaver($data);
                            } else {
                                $relationship->create($data);
                            }
                        }
                    });
                }
            } elseif ($relationship instanceof HasOne) {
                if ($data) {
                    if ($model = $relationship->first()) {
                        $model->fill($data)->save();
                    } else {
                        $relationship->create($data);
                    }
                } else {
                    $relationship->delete();
                }
            } else {
                $relationshipType = get_class($relationship);
                throw new Exception("Cannot fill relationship [$name] of type [$relationshipType]!");
            }
        }
    }

    /**
     * @throws null
     */
    private function extractRelationships(): array
    {
        return collect($this->reviewableRelationships)->mapWithKeys(function ($name) {
            $relationship = $this->{$name}();

            if ($relationship instanceof BelongsToMany) {
                return [$name => $relationship->allRelatedIds()];
            } elseif ($relationship instanceof HasMany) {
                $relationshipAttributes = $relationship->getRelated()->getFillable();

                return [$name => $relationship->select($relationshipAttributes)->get()->map(fn ($model) => $model->getAttributes())->toArray()];
            } elseif ($relationship instanceof HasOne) {
                $relationshipAttributes = $relationship->getRelated()->getFillable();

                return [$name => $relationship->select($relationshipAttributes)->first()?->getAttributes()];
            } else {
                $relationshipType = get_class($relationship);
                throw new Exception("Cannot extract relationship [$name] of type [$relationshipType]!");
            }
        })->toArray();
    }

    /**
     * @throws null
     */
    private function replicateRelationships(self $clone, array &$relationships = []): void
    {
        $newIds = [];

        foreach (($this->reviewableRelationships ?? []) as $name) {
            $originalRelationship = $this->{$name}();
            $newRelationship = $clone->{$name}();

            if ($originalRelationship instanceof BelongsToMany) {
                $newRelationship->attach($originalRelationship->allRelatedIds());
            } elseif ($originalRelationship instanceof HasMany) {
                $originalRelationship->get()->each(function ($related) use (&$newIds, $name, $newRelationship) {
                    $relatedClone = $related->replicate();
                    $newRelationship->save($relatedClone);
                    $newIds[$name][$related->getKey()] = $relatedClone->getKey();
                });
            } elseif ($originalRelationship instanceof HasOne) {
                if ($related = $originalRelationship->first()) {
                    $relatedClone = $related->replicate();
                    $newRelationship->save($relatedClone);
                    $newIds[$name][$related->getKey()] = $relatedClone->getKey();
                }
            } else {
                $relationshipType = get_class($originalRelationship);
                throw new Exception("Cannot extract relationship [$name] of type [$relationshipType]!");
            }
        }

        $this->syncNewIdsOfRelationships($clone, $newIds, $relationships);
    }

    /**
     * @throws null
     */
    private function syncNewIdsOfRelationships(self $clone, array $newIds, array &$relationships): void
    {
        foreach (($this->reviewableRelationships ?? []) as $name) {
            if (! array_key_exists($name, $relationships)) {
                continue;
            }
            $relationship = $clone->{$name}();

            // Unlike other methods, here only few relationship types
            // are supported and the rest is ignored.
            if ($relationship instanceof HasMany) {
                $keyField = $relationship->getRelated()->getKeyName();
                $hasIds = collect($relationships[$name])->some(fn ($value) => $value[$keyField] ?? false);

                if ($hasIds) {
                    $updated = collect($relationships[$name])
                        ->map(function ($value) use ($name, $newIds, $keyField) {
                            if ($value[$keyField] ?? false) {
                                $value[$keyField] = $newIds[$name][$value[$keyField]];
                            }

                            return $value;
                        });
                    $relationships[$name] = $updated;
                }
            }
        }
    }
}
