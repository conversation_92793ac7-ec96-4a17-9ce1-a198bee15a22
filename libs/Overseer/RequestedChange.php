<?php

namespace Libs\Overseer;

use App\Models\Company;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class RequestedChange extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public function __construct(
        private Company $company,
        private mixed $model,
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        $company = $this->company->name;
        $entity = $this->entity();

        return $this
            ->subject(__('changes.requested_change.subject', ['company' => $company, 'entity' => $entity]))
            ->markdown('mail.changes.requested-change', [
                'company' => $company,
                'entity' => $entity,
                'url' => $this->getFrontendUrl(),
            ]);
    }

    private function entity(): string
    {
        $entity = strtolower(class_basename($this->model));
        $name = $this->model->name ?? null;

        return __("changes.entities.$entity", ['name' => $name]);
    }

    private function getFrontendUrl(): string
    {
        $name = strtolower(class_basename($this->model));
        $baseUrl = rtrim(config('app.frontend_url'), '/');
        $pageUrl = __("changes.entities_admin_url.$name", ['id' => $this->model->public_id]);

        return $baseUrl.$pageUrl;
    }
}
