<?php

namespace Libs\Overseer;

use App\Exceptions\AppException;
use Illuminate\Database\Eloquent\Model;

class NoPendingChangesException extends AppException
{
    public function __construct(Model $model)
    {
        $modelName = get_class($model);
        $modelId = $model->getKey();

        parent::__construct(
            message: __('exceptions.no_pending_changes'),
            code: 409,
            devMessage: "There are no pending changes for model [$modelName] with id [$modelId].",
        );
    }
}
