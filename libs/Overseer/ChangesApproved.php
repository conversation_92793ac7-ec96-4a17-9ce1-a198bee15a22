<?php

namespace Libs\Overseer;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class ChangesApproved extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public function __construct(
        private mixed $model,
    ) {}

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build(): self
    {
        $entity = $this->entity();

        return $this
            ->subject(__('changes.approved.subject', ['entity' => $entity]))
            ->markdown('mail.changes.approved', [
                'entity' => $entity,
            ]);
    }

    private function entity(): string
    {
        $entity = strtolower(class_basename($this->model));
        $name = $this->model->name ?? null;

        return __("changes.entities.$entity", ['name' => $name]);
    }
}
