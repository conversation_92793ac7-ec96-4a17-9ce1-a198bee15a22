<?php

namespace Libs\Overseer;

use App\Http\Filtering\Filters\Filter;
use App\Http\Filtering\FilterState;

class AwaitingApprovalFilter implements Filter
{
    private ?string $default = null;

    private ?string $relationship = null;

    private ?array $relationships = null;

    public function __construct(
        private ?string $key = 'awaiting_approval',
    ) {}

    public function filterKey(): string
    {
        return $this->key;
    }

    public function defaultValue(): ?string
    {
        return $this->default;
    }

    public function filter(FilterState $state, string $value, bool $negate): void
    {
        $value = (bool) $value ^ $negate;

        if ($this->relationship) {
            $this->filterRelationship($state, $this->relationship, $value);
        } else {
            $has = $value ? 'has' : 'doesntHave';

            $state->query()->where(fn ($subQuery) => $subQuery
                ->{$has}('unapproved_change')
                ->where('publish_status', $value ? '=' : '!=', PublishStatus::AwaitingApproval, $value ? 'or' : 'and')
            );

            foreach (($this->relationships ?? []) as $relationship) {
                $where = $value ? 'orWhere' : 'where';

                $state->query()->{$where}(
                    fn ($subQuery) => $this->filterRelationship($state->subQuery($subQuery), $relationship, $value)
                );
            }
        }
    }

    private function filterRelationship(FilterState $state, string $relationship, string $value): void
    {
        $has = $value ? 'has' : 'doesntHave';
        $whereRelation = $value ? 'orWhereHas' : 'whereDoesntHave';

        $state->query()->where(fn ($subQuery) => $subQuery
            ->{$has}("$relationship.unapproved_change")
            ->{$whereRelation}($relationship, fn ($whereQuery) => $whereQuery->where('publish_status', '=', PublishStatus::AwaitingApproval))
        );
    }

    public function default(?string $default): self
    {
        $this->default = $default;

        return $this;
    }

    public function onlyRelationship(?string $relationship): self
    {
        $this->relationship = $relationship;

        return $this;
    }

    public function withRelationship(string $relationship): self
    {
        $this->relationships = $this->relationships ?? [];
        $this->relationships[] = $relationship;

        return $this;
    }
}
