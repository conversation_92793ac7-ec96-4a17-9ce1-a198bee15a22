<?php

namespace Libs\Vision\Parser\Exceptions;

use App\Exceptions\AppException;
use Exception;

class ResumeParsingException extends AppException
{
    private function __construct(string $driver, int $code, Exception $root)
    {
        parent::__construct(
            message: __('exceptions.failed_to_parse_resume'),
            code: $code,
            previous: $root,
            devMessage: "Resume parsing via driver [$driver] failed with message [$root->message].",
        );
    }

    public static function serverError(string $driver, Exception $root): self
    {
        return new self($driver, 500, $root);
    }

    public static function requestError(string $driver, Exception $root): self
    {
        return new self($driver, 400, $root);
    }
}
