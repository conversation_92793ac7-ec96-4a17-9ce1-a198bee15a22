<?php

namespace Libs\Vision\Parser\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Libs\Vision\Parser\POPOs\ParsedResumeData;

class ParsedCVDataCast implements CastsAttributes
{
    public function get($model, string $key, $value, array $attributes): ?ParsedResumeData
    {
        $data = json_decode($value, true);

        if (! $data) {
            return null;
        }

        return new ParsedResumeData($data['meta'], $data['data'], $data['rawData']);
    }

    public function set($model, string $key, $value, array $attributes): string
    {
        return json_encode($value);
    }
}
