<?php

namespace Libs\Vision\Parser;

use App\Repositories\TechnologiesRepository;
use Libs\Vision\Parser\Drivers\ResumeParserDriver;
use Libs\Vision\Parser\POPOs\ParsedResumeData;
use Libs\Warehouse\Resource;
use Libs\Warehouse\Warehouse;

class ResumeParser
{
    public function __construct(
        private ResumeParserDriver $driver,
        private Warehouse $warehouse,
        private TechnologiesRepository $technologiesRepository,
    ) {}

    public function parseResume(Resource $resource, array $extra = []): ParsedResumeData
    {
        $resumeContents = $this->warehouse->read($resource);

        $rawData = $this->driver->parseResume($resource->filename, $resumeContents, $extra);
        $data = $this->collectDataFromResponse($rawData);
        $meta = $this->collectMeta($resource, $rawData);

        return ParsedResumeData::make($meta, $data, $rawData);
    }

    private function collectDataFromResponse(array $rawData): array
    {
        $technologiesByEmsiId = $this->technologiesRepository->allByEmsiId();
        $experiences = $this->driver->extractExperiences($rawData);
        $experiencesByParserId = collect($experiences)->keyBy('parser_id');

        return [
            'internal_name' => $this->driver->extractFullNameNormalized($rawData),
            'name' => $this->driver->extractPartialNameNormalized($rawData),
            'country' => $this->driver->extractCountryCode($rawData),
            'city' => $this->driver->extractCity($rawData),
            'rate' => $this->driver->extractRate($rawData),

            'profession' => $this->driver->extractProfession($rawData),
            'seniority' => $this->driver->extractSeniority($rawData),
            'last_job_title' => $this->driver->extractLastJobTitle($rawData),
            'years_of_experience' => $this->driver->extractYearsOfExperience($rawData),
            'highest_education' => $this->driver->extractHighestEducation($rawData),
            'field_of_study' => $this->driver->extractFieldOfStudy($rawData),

            'experiences' => $experiences,
            'skills' => $this->driver->extractSkills($rawData, $technologiesByEmsiId, $experiencesByParserId),
        ];
    }

    private function collectMeta(Resource $resource, array $rawData): array
    {
        return [
            'source_resource_id' => $resource->id,
            'identifier' => $this->driver->extractIdentifier($rawData),
            'filename' => $resource->filename,
            'is_resume_probability' => $this->driver->extractIsResumeProbability($rawData),
            'processed_at' => now()->toAtomString(),
        ];
    }
}
