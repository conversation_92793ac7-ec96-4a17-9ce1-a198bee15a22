<?php

namespace Libs\Vision\Parser\Drivers;

use App\Enums\Education;
use App\Enums\LengthType;
use App\Enums\Seniority;
use App\Exceptions\VerboseRequestException;
use Exception;
use Illuminate\Http\Client\Factory as HttpFactory;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\Validation\UnauthorizedException;
use Libs\Vision\Parser\Exceptions\ResumeParsingException;

class AffindaResumeParserDriver implements ResumeParserDriver
{
    private const DRIVER_NAME = 'affinda';

    private const PARSE_RESUME_ENDPOINT = 'https://api.affinda.com/v2/resumes';

    private const AFFINDA_TO_NIO_EDUCATION_LEVELS = [
        'bachelors' => Education::Bachelor,
        'masters' => Education::Master,
        'doctoral' => Education::Doctor,
    ];

    public function __construct(
        private HttpFactory $httpFactory,
    ) {}

    /**
     * @throws ResumeParsingException
     */
    public function parseResume(string $filename, string $contents, array $extra = []): array
    {
        try {
            $response = circuit_breaker('vision_affinda_resume_parser', fn () => $this->httpFactory
                ->attach('file', $contents, $filename)
                ->withToken(config('services.affinda.key'))
                ->timeout(50)
                ->post(self::PARSE_RESUME_ENDPOINT, [
                    'identifier' => $extra['identifier'] ?? null,
                    'fileName' => $filename,
                ])
            );
        } catch (Exception $e) {
            // This is either some fundamental error, or service is unavailable
            throw ResumeParsingException::serverError(self::DRIVER_NAME, $e);
        }

        // Request failed due to our API code being invalid. Should be a rare
        // case if not impossible in production, but still, we should give
        // signalize to the client that request may be retried later.
        if ($response->unauthorized()) {
            throw ResumeParsingException::serverError(self::DRIVER_NAME, new UnauthorizedException);
        }

        // There was other error on the Affinda side, most probably issue
        // with the CV itself. We will return request error, indicating that
        // the request should not be retried as is.
        if ($response->failed()) {
            throw ResumeParsingException::requestError(self::DRIVER_NAME, new VerboseRequestException($response));
        }

        return $this->postProcessRawData($response->json(), $extra);
    }

    public function extractFullNameNormalized(array $rawData): ?string
    {
        $nameData = array_get($rawData, 'data.name');
        $nameParts = [
            $nameData['title'] ?? null,
            $nameData['first'] ?? null,
            $nameData['last'] ?? null,
            $nameData['middle'] ?? null,
        ];

        return $this->limitString(collect($nameParts)->filter()->join(' ') ?: null);
    }

    public function extractPartialNameNormalized(array $rawData): ?string
    {
        $nameData = array_get($rawData, 'data.name');

        $surnameShort = isset($nameData['last']) && $nameData['last']
            ? Str::ucfirst($nameData['last'][0]).'.'
            : null;

        $nameParts = [
            $nameData['first'] ?? null,
            $surnameShort,
        ];

        return $this->limitString(collect($nameParts)->filter()->join(' ') ?: null);
    }

    public function extractCountryCode(array $rawData): ?string
    {
        $country = Str::lower(array_get($rawData, 'data.location.countryCode'));
        $countries = array_keys(__('countries'));

        return in_array($country, $countries) ? $country : null;
    }

    public function extractCity(array $rawData): ?string
    {
        return $this->limitString(array_get($rawData, 'data.location.city'));
    }

    public function extractRate(array $rawData): ?int
    {
        return null;
    }

    public function extractProfession(array $rawData): ?string
    {
        return $this->limitString(array_get($rawData, 'data.profession'));
    }

    public function extractSeniority(array $rawData): ?Seniority
    {
        return null;
    }

    public function extractLastJobTitle(array $rawData): ?string
    {
        $jobs = collect(array_get($rawData, 'data.workExperience', []));

        if ($jobs->isEmpty()) {
            return null;
        }

        $currentJobs = $jobs->where('dates.isCurrent', true);

        // First, check if there is only one current job - we can use it straight away
        if ($currentJobs->count() === 1) {
            return $this->limitString($currentJobs->first()['jobTitle'] ?? null);
        }

        // Otherwise, we will return job title of the most recently started position
        if ($currentJobs->isNotEmpty()) {
            return $this->limitString($currentJobs->sortBy('dates.startDate')->first()['jobTitle'] ?? null);
        }

        // Lastly, if there are no current jobs, we will pick
        // the most recently started position overall
        return $this->limitString($jobs->sortBy('dates.startDate')->first()['jobTitle'] ?? null);
    }

    public function extractYearsOfExperience(array $rawData): ?int
    {
        return array_get($rawData, 'data.totalYearsExperience') ?: null;
    }

    public function extractHighestEducation(array $rawData): ?Education
    {
        $education = $this->findHighestEducation(array_get($rawData, 'data.education'));

        if (! $education) {
            return null;
        }

        $level = array_get($education, 'accreditation.educationLevel');

        return self::AFFINDA_TO_NIO_EDUCATION_LEVELS[$level] ?? null;
    }

    public function extractFieldOfStudy(array $rawData): ?string
    {
        $education = $this->findHighestEducation(array_get($rawData, 'data.education'));
        $fieldOfStudy = array_get($education, 'accreditation.education');

        if (! $fieldOfStudy) {
            return null;
        }

        // Unfortunately, Affinda does not provide field of study out of the box,
        // so we will need to do some guess work. Education may contain degree
        // itself, usually at the beginning as "Master of", etc. We will try
        // to detect that and remove it from the string.
        $stringableFieldOfStudy = Str::of($fieldOfStudy)->lower()->trim();

        // This only handles specific case of "Master of Science in Computer Science".
        // TODO: make this more generic in the future
        if (preg_match('/((doctor|master|bachelor) of science in )(.* science)/', $stringableFieldOfStudy, $matches)) {
            return $this->limitString(Str::of($matches[3])->trim()->title());
        }

        // In the list of prefixes below, order matters! Highers items in the list
        // will be matched first. This will handle the rest of cases.
        $prefixes = [
            'doctor of',
            'doctor in',
            'doctorate in',
            'doctorate',
            'doctor',

            'master of',
            'master in',
            'masters in',
            'masters',
            'master',

            'bachelor of',
            'bachelor in',
            'bachelors in',
            'bachelors',
            'bachelor',
        ];

        foreach ($prefixes as $prefix) {
            if ($stringableFieldOfStudy->startsWith($prefix)) {
                return $this->limitString($stringableFieldOfStudy->after($prefix)->trim()->title());
            }
        }

        // If nothing was matched, return original field of study
        return $this->limitString($fieldOfStudy);
    }

    public function extractExperiences(array $rawData): array
    {
        return collect(array_get($rawData, 'data.workExperience', []))->map(function (array $rawData, int $index) {
            $nameParts = [
                $rawData['jobTitle'] ?? null,
                $rawData['organization'] ?? null,
            ];

            $isCurrentPosition = array_get($rawData, 'dates.isCurrent') ?: false;
            $monthsInPosition = array_get($rawData, 'dates.monthsInPosition');
            $lengthType = match (true) {
                $isCurrentPosition => LengthType::Ongoing,
                $monthsInPosition === null => LengthType::Unspecified,
                $monthsInPosition <= 18 => LengthType::Months,
                default => LengthType::Years,
            };
            $length = match ($lengthType) {
                LengthType::Unspecified,
                LengthType::Ongoing => null,
                LengthType::Months => $monthsInPosition,
                LengthType::Years => round($monthsInPosition / 12),
            };

            return [
                'pseudo_id' => $index,
                'parser_id' => $rawData['id'],
                'name' => $this->limitString(collect($nameParts)->filter()->join(' at ') ?: null, 100),
                'description' => $rawData['jobDescription'] ?? 'No description',
                'length_type' => $lengthType,
                'length' => $length,
            ];
        })->whereNotNull('name')->toArray();
    }

    public function extractSkills(array $rawData, Collection $technologiesByEmsiId, Collection $experiencesByParserId): array
    {
        return collect(array_get($rawData, 'data.skills', []))->map(function (array $rawData) use ($technologiesByEmsiId, $experiencesByParserId) {
            $experiences = collect($rawData['sources'] ?? [])
                ->filter(fn (array $source) => $source['section'] === 'WorkExperience')
                ->map(fn (array $source) => $experiencesByParserId[$source['workExperienceId']]['pseudo_id'] ?? null)
                ->whereNotNull()
                ->toArray();

            return [
                'technology_id' => $technologiesByEmsiId->get(array_get($rawData, 'emsiId'))->id ?? null,
                'years_of_experience' => (int) round(array_get($rawData, 'numberOfMonths') / 12) ?: null,
                'experiences' => [],
                'new_experiences' => $experiences,
            ];
        })->whereNotNull('technology_id')->values()->toArray();
    }

    public function extractIdentifier(array $rawData): string
    {
        return array_get($rawData, 'meta.identifier');
    }

    public function extractIsResumeProbability(array $rawData): ?int
    {
        return array_get($rawData, 'data.isResumeProbability');
    }

    private function postProcessRawData(array $rawData, array $extra): array
    {
        // Ensure that there always will be first name of the candidate
        // If no name is returned from Affinda, sensible default (Anonymous)
        // will be used instead
        if ($extra['fallbackName'] && ! $this->extractPartialNameNormalized($rawData)) {
            array_set($rawData, 'data.name.first', $extra['fallbackName']);
        }

        // We do not wish to store any files. If Affinda returned headshot
        // of the candidate, we will erase it before saving it to database
        if (array_get($rawData, 'data.headShot')) {
            array_set($rawData, 'data.headShot', null);
        }

        return $rawData;
    }

    private function findHighestEducation(?array $educations): ?array
    {
        if (! $educations || count($educations) === 0) {
            return null;
        }

        $levels = array_keys(self::AFFINDA_TO_NIO_EDUCATION_LEVELS);

        // This is a classic manual search for the highest value, since we
        // need to do custom checks, this is simpler to do. It will pick up
        // the first education with the highest level, meaning if there are
        // two masters, the second one will be ignored.
        $highestEducationLevelIndex = -1;
        $highestEducationIndex = -1;

        foreach ($educations as $index => $education) {
            $level = array_get($education, 'accreditation.educationLevel');
            $levelIndex = array_search($level, $levels);

            if ($levelIndex !== false && $levelIndex > $highestEducationLevelIndex) {
                $highestEducationLevelIndex = $levelIndex;
                $highestEducationIndex = $index;
            }
        }

        return $educations[$highestEducationIndex] ?? null;
    }

    private function limitString(?string $string, int $length = 50): ?string
    {
        return $string ? substr($string, 0, $length) : $string;
    }
}
