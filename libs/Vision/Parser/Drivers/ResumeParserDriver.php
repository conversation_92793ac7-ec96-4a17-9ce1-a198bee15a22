<?php

namespace Libs\Vision\Parser\Drivers;

use App\Enums\Education;
use App\Enums\Seniority;
use Illuminate\Support\Collection;

interface ResumeParserDriver
{
    public function parseResume(string $filename, string $contents, array $extra = []): array;

    public function extractFullNameNormalized(array $rawData): ?string;

    public function extractPartialNameNormalized(array $rawData): ?string;

    public function extractCountryCode(array $rawData): ?string;

    public function extractCity(array $rawData): ?string;

    public function extractRate(array $rawData): ?int;

    public function extractProfession(array $rawData): ?string;

    public function extractSeniority(array $rawData): ?Seniority;

    public function extractLastJobTitle(array $rawData): ?string;

    public function extractYearsOfExperience(array $rawData): ?int;

    public function extractHighestEducation(array $rawData): ?Education;

    public function extractFieldOfStudy(array $rawData): ?string;

    public function extractExperiences(array $rawData): array;

    public function extractSkills(array $rawData, Collection $technologiesByEmsiId, Collection $experiencesByParserId): array;

    public function extractIdentifier(array $rawData): string;

    public function extractIsResumeProbability(array $rawData): ?int;
}
