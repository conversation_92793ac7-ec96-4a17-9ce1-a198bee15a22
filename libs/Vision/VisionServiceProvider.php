<?php

namespace Libs\Vision;

use Illuminate\Support\ServiceProvider;
use Libs\Vision\Parser\Drivers\AffindaResumeParserDriver;
use Libs\Vision\Parser\Drivers\ResumeParserDriver;
use Libs\Vision\Parser\ResumeParser;

class VisionServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(ResumeParser::class);
        $this->app->singleton(ResumeParserDriver::class, AffindaResumeParserDriver::class);
    }
}
