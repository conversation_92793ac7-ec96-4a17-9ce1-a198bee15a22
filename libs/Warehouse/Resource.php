<?php

namespace Libs\Warehouse;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Resource extends Model
{
    use DependentModelsTrait;
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'user_id',
        'type', 'hash', 'name',
        'disk', 'directory', 'filename', 'path',
        'extra',
    ];

    protected $casts = [
        'extra' => 'array',
    ];

    public function scopeType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    public function scopeHash(Builder $query, string $hash): Builder
    {
        return $query->where('hash', $hash);
    }
}
