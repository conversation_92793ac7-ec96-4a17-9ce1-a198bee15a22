<?php

namespace Libs\Warehouse\JsonResources;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\MissingValue;
use Libs\Warehouse\Warehouse;

class FileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->when($this->id ?? false, $this->id ?? null),
            'format' => $this->format,
            'mime' => $this->mime,
            'size' => $this->size,
            'name' => $this->name,
            'url' => $this->url,
        ];
    }

    public static function make(...$parameters)
    {
        /** @noinspection PhpUnhandledExceptionInspection */
        throw_if(count($parameters) <= 0, new Exception('Cannot create warehouse file JSON resource without resource model instance.'));
        $resource = array_shift($parameters);

        if (! $resource) {
            return new MissingValue;
        }

        /* @var Warehouse $warehouse */
        $warehouse = app(Warehouse::class);

        return $warehouse->jsonResource($resource, $parameters);
    }

    public static function collection($resource)
    {
        return parent::collection(collect($resource)->map(fn($item) => self::make($item)));
    }
}
