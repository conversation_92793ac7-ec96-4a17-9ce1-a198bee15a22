<?php

namespace Libs\Warehouse\JsonResources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\MissingValue;
use Libs\Warehouse\Resource;
use Libs\Warehouse\Warehouse;

class ImageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     */
    public function toArray($request): array
    {
        return [
            'id' => $this->when($this->id ?? false, $this->id ?? null),
            'url' => $this->url,
            'thumbnails' => $this->when($this->thumbnails ?? false, fn () => static::collection($this->thumbnails)),
        ];
    }

    public static function main(?Resource $resource, ?string $fallbackUrl = null): static|MissingValue
    {
        if (! $resource) {
            return static::fromUrl($fallbackUrl);
        }

        /* @var Warehouse $warehouse */
        $warehouse = app(Warehouse::class);

        $main = $warehouse->jsonResource($resource);
        $main->thumbnails = null;

        return static::make($main);
    }

    public static function thumbnail(?Resource $resource, string $id, ?string $fallbackUrl = null): static|MissingValue
    {
        if (! $resource) {
            return static::fromUrl($fallbackUrl);
        }

        /* @var Warehouse $warehouse */
        $warehouse = app(Warehouse::class);

        $thumbnail = $warehouse->jsonResource($resource)->thumbnails->firstOrFail(fn ($item) => $item->id === $id);

        return static::make($thumbnail);
    }

    public static function fromUrl(?string $url): static|MissingValue
    {
        if (! $url) {
            return new MissingValue;
        }

        return static::make((object) compact('url'));
    }
}
