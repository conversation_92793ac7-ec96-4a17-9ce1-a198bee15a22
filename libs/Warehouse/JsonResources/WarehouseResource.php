<?php

namespace Libs\Warehouse\JsonResources;

use Exception;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\MissingValue;
use Libs\Warehouse\Warehouse;

class WarehouseResource extends JsonResource
{
    public static function make(...$parameters)
    {
        /** @noinspection PhpUnhandledExceptionInspection */
        throw_if(count($parameters) <= 0, new Exception('Cannot create warehouse JSON resource without resource model instance.'));
        $resource = array_shift($parameters);

        if (! $resource) {
            return new MissingValue;
        }

        /* @var Warehouse $warehouse */
        $warehouse = app(Warehouse::class);

        return $warehouse->jsonResource($resource, $parameters);
    }
}
