<?php

namespace Libs\Warehouse\Exceptions;

use App\Exceptions\AppException;

class FailedToRemoveResourceException extends AppException
{
    public function __construct(string $path, array $config)
    {
        $configJson = json_encode($config, JSON_UNESCAPED_UNICODE);

        parent::__construct(
            message: __('exceptions.failed_to_remove_resource'),
            code: 500,
            devMessage: "Failed to remove resource from the path [$path] with the given config <$configJson>.",
        );
    }
}
