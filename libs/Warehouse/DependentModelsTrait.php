<?php

namespace Libs\Warehouse;

use App\Models\Solution;
use App\Models\SolutionMedium;
use App\Models\Traits\ProtectedDeleteTrait;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait DependentModelsTrait
{
    use ProtectedDeleteTrait;

    protected array $dependentRelationships = [
        'solutions', 'solution_media',
    ];

    public function solutions(): HasMany
    {
        return $this->hasMany(Solution::class, 'cover_resource_id');
    }

    public function solution_media(): HasMany
    {
        return $this->hasMany(SolutionMedium::class);
    }
}
