<?php

namespace Libs\Warehouse;

use Illuminate\Contracts\Foundation\Application;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;
use InvalidArgumentException;

class Warehouse
{
    private array $resourceConfigs = [];

    public function __construct(
        private Application $app,
        private ResourcesRepository $repository,
    ) {}

    /**
     * Attempts to look up requested resource. If not, fails with exception.
     *
     * If integer is passed as ID, internal incremental table ID is used for search.
     * If string is passed as ID, generated public ID is used for search.
     *
     * @param  int|string  $id  identifier of the resource
     * @return resource requested resource if found
     */
    public function find(int|string $id): Resource
    {
        return $this->repository->find($id);
    }

    /**
     * Stores requested resource. If not forced and resource already exists, returns
     * instance to an existing one.
     *
     * @param  string  $type  type of the resource to be stored
     * @param  mixed  $contents  contents of the resource
     * @param  bool  $force  whether to create new resource even if it already exists [default: false]
     * @return resource instance of stored resource
     */
    public function store(string $type, mixed $contents, bool $force = false): Resource
    {
        // To optimize storage, we will first verify whether given resource
        // has been already uploaded or not and try to return it instead.
        // However, sometimes we may wish to store it anyway, but not
        // by default.
        if (! $force && $existing = $this->repository->findByContentsHash($type, $contents)) {
            return $existing;
        }

        $config = $this->getResourceConfig($type);
        $data = $config['driver']->store($contents, $config['config']);
        $data['user_id'] = auth()->user()?->getAuthIdentifier();
        $data['type'] = $type;
        $data['hash'] = $this->repository->computeContentsHash($contents);

        return $this->repository->store($data);
    }

    /**
     * Reads contents of the resource from filesystem.
     *
     * @param  int|string|resource  $resource  resource to be read, or its identifier
     * @return string contents of the resource
     */
    public function read(int|string|Resource $resource): string
    {
        $resource = $resource instanceof Resource
            ? $resource
            : $this->repository->find($resource);

        $config = $this->getResourceConfig($resource->type);

        return $config['driver']->read($resource, $config['config']);
    }

    /**
     * Deletes resource from database and filesystem.
     *
     * May throw exception if removal failed.
     *
     * @param  int|string|resource  $resource  resource to be removed, or its identifier
     * @return bool true if save was successful, false otherwise
     */
    public function remove(int|string|Resource $resource): bool
    {
        $resource = $resource instanceof Resource
            ? $resource
            : $this->repository->find($resource);

        $config = $this->getResourceConfig($resource->type);

        return $config['driver']->remove($resource, $config['config'])
            && $resource->delete();
    }

    /**
     * Returns validation rules for the requested resource
     *
     * @param  string  $type  type of the resource
     * @return array rules for the given type
     */
    public function rules(string $type): array
    {
        $config = $this->getResourceConfig($type);

        return $config['driver']->rules($config['config']);
    }

    /**
     * Wraps given Warehouse resource in Laravel's JSON API resource.
     *
     * @param  resource  $resource  resource to be wrapped
     * @param  array  $parameters  extra parameters for construction
     * @return JsonResource JSON API resource
     */
    public function jsonResource(Resource $resource, array $parameters = []): JsonResource
    {
        $config = $this->getResourceConfig($resource->type);

        return $config['driver']->jsonResource($resource, $parameters, $config['config']);
    }

    /**
     * Returns list of resource types that can be uploaded via API.
     *
     * @return Collection list of uploadable types
     */
    public function uploadableTypes(): Collection
    {
        return collect(config('warehouse.resources'))
            ->filter(fn ($type) => ($type['uploadable'] ?? true))
            ->keys();
    }

    private function getResourceConfig(string $type): array
    {
        if (! array_key_exists($type, $this->resourceConfigs)) {
            $config = config("warehouse.resources.$type");

            if ($config['template'] ?? null) {
                $template = config("warehouse.templates.{$config['template']}");
                $config = array_merge_recursive_distinct($template, $config);
            }

            if (! array_key_exists('driver', $config)) {
                throw new InvalidArgumentException("Resource type [$type] does not have driver defined!");
            }

            $driver = $this->app->make("warehouse_driver_{$config['driver']}");
            $config['driver'] = $driver;

            $this->resourceConfigs[$type] = $config;
        }

        return $this->resourceConfigs[$type];
    }
}
