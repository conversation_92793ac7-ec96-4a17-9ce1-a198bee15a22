<?php

namespace Libs\Warehouse;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ResourcesRepository
{
    /**
     * @return Collection<resource>
     */
    public function all(): Collection
    {
        return $this->query()->get();
    }

    public function query(): Builder
    {
        return Resource::query();
    }

    public function find(int|string $id): Resource
    {
        $query = is_int($id)
            ? $this->query()->where('id', $id)
            : $this->query()->where('public_id', $id);

        /** always returns Resource @noinspection PhpIncompatibleReturnTypeInspection */
        return $query->firstOrFail();
    }

    public function findOfType(string $type, int|string $id): Resource
    {
        $query = is_int($id)
            ? $this->query()->where('id', $id)
            : $this->query()->where('public_id', $id);

        return $query->type($type)->firstOrFail();
    }

    public function findByContentsHash(string $type, mixed $contents): ?Resource
    {
        $hash = $this->computeContentsHash($contents);

        return $this->query()
            ->type($type)
            ->hash($hash)
            ->first();
    }

    public function store(array $data): Resource
    {
        return Resource::create($data);
    }

    public function computeContentsHash(mixed $contents): string
    {
        return is_file($contents)
            ? hash_file('sha256', $contents)
            : hash('sha256', $contents);

    }
}
