<?php

namespace Libs\Warehouse\Drivers;

use Illuminate\Http\Resources\Json\JsonResource;
use Libs\Warehouse\Resource;

interface Driver
{
    public function store(mixed $contents, array $config): array;

    public function read(Resource $resource, array $config): string;

    public function remove(Resource $resource, array $config): bool;

    public function rules(array $config): array;

    public function jsonResource(Resource $resource, array $parameters, array $config): JsonResource;
}
