<?php

namespace Libs\Warehouse\Drivers;

use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Filesystem\FilesystemManager;
use Illuminate\Http\Resources\Json\JsonResource;
use InvalidArgumentException;
use Libs\Warehouse\Exceptions\FailedToRemoveResourceException;
use Libs\Warehouse\Exceptions\FailedToSaveResourceException;
use Libs\Warehouse\Exceptions\MissingFileExtensionException;
use Libs\Warehouse\JsonResources\FileResource;
use Libs\Warehouse\Resource;
use Symfony\Component\HttpFoundation\File\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class FileDriver implements Driver
{
    public function __construct(
        private FilesystemManager $filesystemManager,
    ) {}

    /**
     * @throws MissingFileExtensionException
     * @throws FailedToSaveResourceException
     */
    public function store(mixed $contents, array $config): array
    {
        if (! $contents instanceof File) {
            throw new InvalidArgumentException("File driver can only work with instance of Symfony's File class!");
        }

        $name = str_random(40);
        $extra = [
            'format' => strtolower($contents->guessExtension() ?? throw new MissingFileExtensionException),
            'mime' => $contents->getMimeType() ?? throw new MissingFileExtensionException,
            'size' => $contents->getSize(),
            'name' => $contents instanceof UploadedFile ? $contents->getClientOriginalName() : $contents->getFilename(),
        ];

        $fileConfig = $this->extractFileConfig($name, $extra['format'], $config);
        $this->processAndSaveFile($contents, $fileConfig, $config);

        return [
            'name' => $name,
            'disk' => $fileConfig->disk,
            'directory' => $fileConfig->directory,
            'filename' => $fileConfig->filename,
            'path' => $fileConfig->path,
            'extra' => $extra,
        ];
    }

    /**
     * @throws FileNotFoundException
     */
    public function read(Resource $resource, array $config): string
    {
        $data = $this->filesystemManager->disk($resource->disk)->get($resource->path);
        throw_if(! $data, FileNotFoundException::class);

        return $data;
    }

    /**
     * @throws FailedToRemoveResourceException
     */
    public function remove(Resource $resource, array $config): bool
    {
        if (! $this->filesystemManager->disk($resource->disk)->delete($resource->path)) {
            throw new FailedToRemoveResourceException($resource->path, $config);
        }

        return true;
    }

    public function rules(array $config): array
    {
        $allowed = 'file';
        if ($config['allowed_types'] ?? false) {
            $allowed = 'mimes:'.implode(',', array_wrap($config['allowed_types']));
        }

        $maxSize = $config['max_size'] ?? '10240';

        return [
            'content' => "required|$allowed|max:$maxSize",
        ];
    }

    public function jsonResource(Resource $resource, array $parameters, array $config): JsonResource
    {
        $id = $resource->public_id;
        $format = $resource->extra['format'];
        $mime = $resource->extra['mime'];
        $size = $resource->extra['size'];
        $name = $resource->extra['name'];
        $url = $this->createFileUrl($resource->disk, $resource->path, $resource->updated_at->timestamp);

        return new FileResource((object) compact('id', 'format', 'mime', 'size', 'name', 'url'));
    }

    private function createFileUrl(string $disk, string $path, int $timestamp): string
    {
        return $this->filesystemManager->disk($disk)->url($path).'?timestamp='.$timestamp;
    }

    private function extractFileConfig(string $name, string $format, array $config): object
    {
        $disk = $config['disk'] ?? 'public';
        $directory = $config['directory'] ?? '';
        $filename = "$name.$format";
        $path = $directory ? "$directory/$filename" : $filename;

        return (object) compact('format', 'disk', 'directory', 'filename', 'path');
    }

    /**
     * @throws FailedToSaveResourceException
     */
    private function processAndSaveFile(File $contents, object $fileConfig, array $imageConfig): void
    {
        if (! $this->filesystemManager->disk($fileConfig->disk)->put($fileConfig->path, $contents->getContent())) {
            throw new FailedToSaveResourceException($fileConfig->path, $imageConfig);
        }
    }
}
