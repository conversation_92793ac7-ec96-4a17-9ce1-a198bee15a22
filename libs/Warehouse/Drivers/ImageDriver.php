<?php

namespace Libs\Warehouse\Drivers;

use Illuminate\Contracts\Filesystem\FileNotFoundException;
use Illuminate\Filesystem\FilesystemManager;
use Illuminate\Http\Resources\Json\JsonResource;
use Intervention\Image\ImageManager;
use Intervention\Image\Interfaces\ImageInterface;
use Libs\Warehouse\Exceptions\FailedToRemoveResourceException;
use Libs\Warehouse\Exceptions\FailedToSaveResourceException;
use Libs\Warehouse\JsonResources\ImageResource;
use Libs\Warehouse\Resource;

class ImageDriver implements Driver
{
    public function __construct(
        private ImageManager $imageManager,
        private FilesystemManager $filesystemManager,
    ) {}

    /**
     * @throws FailedToSaveResourceException
     */
    public function store(mixed $contents, array $config): array
    {
        $name = str_random(40);

        $fileConfig = $this->extractImageFileConfig($name, $config);
        $this->processAndSaveImage($contents, $fileConfig, $config);

        $extra = [];
        if ($config['thumbnails'] ?? false) {
            $extra['thumbnails'] = [];

            foreach ($config['thumbnails'] as $thumbnail) {
                $thumbnailName = "{$name}_{$thumbnail['id']}";
                $thumbnailFileConfig = $this->extractImageFileConfig($thumbnailName, $config, $thumbnail);
                $this->processAndSaveImage($contents, $thumbnailFileConfig, $thumbnail);

                $extra['thumbnails'][$thumbnail['id']] = [
                    'name' => $thumbnailName,
                    'disk' => $thumbnailFileConfig->disk,
                    'directory' => $thumbnailFileConfig->directory,
                    'filename' => $thumbnailFileConfig->filename,
                    'path' => $thumbnailFileConfig->path,
                ];
            }
        }

        return [
            'name' => $name,
            'disk' => $fileConfig->disk,
            'directory' => $fileConfig->directory,
            'filename' => $fileConfig->filename,
            'path' => $fileConfig->path,
            'extra' => $extra,
        ];
    }

    /**
     * @throws FileNotFoundException
     */
    public function read(Resource $resource, array $config): string
    {
        $data = $this->filesystemManager->disk($resource->disk)->get($resource->path);
        throw_if(! $data, FileNotFoundException::class);

        return $data;
    }

    /**
     * @throws FailedToRemoveResourceException
     */
    public function remove(Resource $resource, array $config): bool
    {
        if (! $this->filesystemManager->disk($resource->disk)->delete($resource->path)) {
            throw new FailedToRemoveResourceException($resource->path, $config);
        }

        if ($resource->extra['thumbnails'] ?? false) {
            foreach ($resource->extra['thumbnails'] as $thumbnail) {
                if (! $this->filesystemManager->disk($thumbnail['disk'])->delete($thumbnail['path'])) {
                    throw new FailedToRemoveResourceException($thumbnail['path'], $config);
                }
            }
        }

        return true;
    }

    public function rules(array $config): array
    {
        $allowed = 'image';
        if ($config['allowed_types'] ?? false) {
            $allowed = 'mimes:'.implode(',', array_wrap($config['allowed_types']));
        }

        $maxSize = $config['max_size'] ?? '10240';

        return [
            'content' => "required|$allowed|max:$maxSize",
        ];
    }

    public function jsonResource(Resource $resource, array $parameters, array $config): JsonResource
    {
        $id = $resource->public_id;
        $url = $this->createImageUrl($resource->disk, $resource->path, $resource->updated_at->timestamp);

        $thumbnails = isset($resource->extra['thumbnails'])
            ? collect($resource->extra['thumbnails'])->map(fn ($thumbnail, $id) => (object) [
                'id' => $id,
                'url' => $this->createImageUrl($thumbnail['disk'], $thumbnail['path'], $resource->updated_at->timestamp),
            ]
            )->values()
            : null;

        return new ImageResource((object) compact('id', 'url', 'thumbnails'));
    }

    /**
     * @throws FailedToSaveResourceException
     */
    private function processAndSaveImage(mixed $contents, object $fileConfig, array $imageConfig): void
    {
        // Wrap image contents in Intervention image object for further processing
        $image = $this->imageManager->read($contents);

        $condition = ($imageConfig['if'] ?? fn (ImageInterface $image) => true)($image);

        // Next, we will do any necessary image processing as specified in configuration
        if ($condition
            && ($imageConfig['commands'] ?? false)
        ) {
            foreach ($imageConfig['commands'] as $command => $args) {
                $image->{$command}(...array_wrap($args));
            }
        }

        $contents = $image->encodeByExtension($fileConfig->format);

        if (! $this->filesystemManager->disk($fileConfig->disk)->put($fileConfig->path, $contents)) {
            throw new FailedToSaveResourceException($fileConfig->path, $imageConfig);
        }
    }

    private function extractImageFileConfig(string $name, array $mainConfig, array $imageConfig = []): object
    {
        $format = $imageConfig['format'] ?? $mainConfig['format'] ?? 'png';
        $disk = $imageConfig['disk'] ?? $mainConfig['disk'] ?? 'public';
        $directory = $imageConfig['directory'] ?? $mainConfig['directory'] ?? '';
        $filename = "$name.$format";
        $path = $directory ? "$directory/$filename" : $filename;

        return (object) compact('format', 'disk', 'directory', 'filename', 'path');
    }

    private function createImageUrl(string $disk, string $path, int $timestamp): string
    {
        return $this->filesystemManager->disk($disk)->url($path).'?timestamp='.$timestamp;
    }
}
