<?php

namespace Libs\Warehouse;

use App\Observers\PublicIdObserver;
use Illuminate\Filesystem\FilesystemManager;
use Illuminate\Support\ServiceProvider;
use Intervention\Image\Drivers\Imagick\Driver as ImagickDriver;
use Intervention\Image\ImageManager;
use Libs\Warehouse\Drivers\FileDriver;
use Libs\Warehouse\Drivers\ImageDriver;

class WarehouseServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton(Warehouse::class);
        $this->app->singleton(ResourcesRepository::class);

        $this->app->singleton(
            ImageDriver::class,
            fn ($app) => new ImageDriver(
                new ImageManager(ImagickDriver::class),
                $app->make(FilesystemManager::class),
            ),
        );
        $this->app->alias(ImageDriver::class, 'warehouse_driver_image');

        $this->app->singleton(FileDriver::class);
        $this->app->alias(FileDriver::class, 'warehouse_driver_file');
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Resource::observe(PublicIdObserver::class);
    }
}
