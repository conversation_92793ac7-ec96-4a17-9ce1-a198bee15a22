default:
  image: docker
  services:
    - docker:dind

test:
  stage: test
  script:
    - 'apk update && apk add bash'
    - 'sh scripts/install-via-docker'
    - 'cp .env.example .env'
    - 'sh vendor/bin/sail up -d'
    - 'echo "Waiting for MariaDB to start (it has 15 seconds to start)..."'
    - 'sleep 16'
    - 'sh vendor/bin/sail test --log-junit report.xml'
    - 'sh vendor/bin/sail stop'
  rules:
    - if: '$CI_PIPELINE_SOURCE == "web"'
      when: always
    - when: never
  artifacts:
    when: always
    reports:
      junit: report.xml
