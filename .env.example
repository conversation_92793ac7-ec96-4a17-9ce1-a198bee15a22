# Compose
COMPOSE_PROJECT_NAME=nio
COMPOSE_DB_PORT=5806
COMPOSE_API_PROXY_PORT=5815
COMPOSE_MAIL_PORT=5825

# App
APP_NAME="Nordics API"
APP_ENV=local
APP_KEY=base64:zIkixPL+MuYHsv6N1I3wKkSdMe7kjBuqSgGlu6wt8qI=
APP_DEBUG=true
APP_URL=http://localhost
APP_PORT=5815
APP_DOMAIN=localhost
APP_FRONTEND_URL=http://localhost:5173

# Logging
LOG_CHANNEL=stack
LOG_DAYS=7
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Database
DB_CONNECTION=mysql
DB_HOST=db
DB_PORT=3306
DB_DATABASE=api
DB_USERNAME=nordics
DB_PASSWORD=pass

FILESYSTEM_DISK=local

# Cache
CACHE_DRIVER=redis
CACHE_LIFETIME_COMPANY_SEARCH=10

# Redis
REDIS_HOST=cache

# Mail
MAIL_MAILER=smtp
MAIL_HOST=mail
MAIL_PORT=1025
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"
MAIL_ADDRESS_NIO_STAFF=<my-email>@nordics.io

# 3rd party
AFFINDA_API_KEY=

NOCAPTCHA_SECRET=6LdIBGQrAAAAAAAhXG-Tio7aYgQZyZRe7uZijPJL
NOCAPTCHA_SITEKEY=6LdIBGQrAAAAAOzLixjNXfECi1ZXLA-MoF8B_5Sr

# Documentation
SCRIBE_TYPE=laravel
SCRIBE_AUTH_KEY=

# Nordics APIs
SIMILARITY_API_URL=https://similarity-api-reworked.mangograss-25f115e1.germanywestcentral.azurecontainerapps.io

ASSISTANT_API_URL=https://nordics-assistant-dev.graystone-4eee231c.germanywestcentral.azurecontainerapps.io
ASSISTANT_API_KEY=sk-vPxrnRKXv4VnB14gu7qvcQ

MATCHING_API_URL=https://nordics-vendor-matching-dev.graystone-4eee231c.germanywestcentral.azurecontainerapps.io
MATCHING_API_KEY=sk-vPxrnRKXv4VnB14gu7qvcQ

CANDIDATE_MATCHING_API_URL=https://nordics-candidate-matching-dev.graystone-4eee231c.germanywestcentral.azurecontainerapps.io
CANDIDATE_MATCHING_API_KEY=sk-vPxrnRKXv4VnB14gu7qvcQ

MIN_RFP_CHARACTERS=100
COMPANY_UPLOAD_MAX=30

# Queue
QUEUE_CONNECTION=database
RABBITMQ_HOST=
RABBITMQ_PORT=
RABBITMQ_SCIENCE_USER=
RABBITMQ_SCIENCE_PASSWORD=
RABBITMQ_SCIENCE_VHOST=

# Session
SESSION_DRIVER=file
SESSION_LIFETIME=120

# Sentry
SENTRY_LARAVEL_DSN=https://<YOUR_PUBLIC_KEY>@o<YOUR_ORG>.ingest.sentry.io/<PROJECT_ID>
SENTRY_TRACES_SAMPLE_RATE=1.0
